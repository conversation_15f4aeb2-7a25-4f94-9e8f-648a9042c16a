import logging
import uuid
import os
import shutil
import asyncio
import aiofiles
import aiofiles.os
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import config
from services.utils import initialize_assistant, upload_files
from services.mongo_manager import MongoManager

logger = logging.getLogger(__name__)

class TemplatedProjectsUtils:
    """Utility class for handling templated project operations."""

    def __init__(self, templated_projects_collection, insights_collection):
        self.templated_projects_collection = templated_projects_collection
        self.insights_collection = insights_collection

    def _generate_project_id(self) -> str:
        """
        Generate a timestamp-based project ID in the format: project_YYYYMMDDHHMMSSMMM

        Returns:
            str: Project ID with timestamp format
        """
        now = datetime.now(timezone.utc)
        # Format: project_YYYYMMDDHHMMSSMMM
        timestamp_str = now.strftime("%Y%m%d%H%M%S") + f"{now.microsecond // 1000:03d}"
        return f"project_{timestamp_str}"

    def _generate_file_id(self) -> str:
        """
        Generate a UUID for file ID.

        Returns:
            str: UUID string like '6a85b57b-bc83-4a19-b23d-e1ad7a93d4f1'
        """
        return str(uuid.uuid4())

    def _should_generate_file_id(self, file_type: str) -> bool:
        """
        Check if we should generate file_id for this file type.
        CSV and XLSX files have different file_id handling.

        Args:
            file_type (str): The file type/extension

        Returns:
            bool: True if we should generate file_id, False for csv/xlsx
        """
        return file_type.lower() not in ['csv', 'xlsx']

    async def _copy_project_folders(self, template_project_id: str, new_project_id: str) -> Dict[str, Any]:
        """
        Copy entire data_analysis and non_data_analysis folders from template to new project.
        Much simpler approach - just copy the whole folder structure.

        Args:
            template_project_id (str): The template project ID to copy from
            new_project_id (str): The new project ID to copy to

        Returns:
            Dict[str, Any]: Result with copy status information
        """
        try:
            # Source and target directories
            template_dir = os.path.join("files", template_project_id)
            new_dir = os.path.join("files", new_project_id)

            # Create new project directory
            try:
                await aiofiles.os.makedirs(new_dir, exist_ok=True)
            except Exception:
                await asyncio.get_event_loop().run_in_executor(
                    None, lambda: os.makedirs(new_dir, exist_ok=True)
                )

            copied_folders = []
            copy_messages = []

            # Copy data_analysis folder if it exists
            template_data_analysis = os.path.join(template_dir, config.data_analysis_folder_name)
            new_data_analysis = os.path.join(new_dir, config.data_analysis_folder_name)

            if await self._folder_exists(template_data_analysis):
                if await self._copy_folder(template_data_analysis, new_data_analysis):
                    copied_folders.append(config.data_analysis_folder_name)
                    copy_messages.append(f"Copied {config.data_analysis_folder_name} folder")
                else:
                    copy_messages.append(f"Failed to copy {config.data_analysis_folder_name} folder")
            else:
                copy_messages.append(f"{config.data_analysis_folder_name} folder not found in template")

            # Copy non_data_analysis folder if it exists
            template_non_data_analysis = os.path.join(template_dir, config.non_data_analysis_folder_name)
            new_non_data_analysis = os.path.join(new_dir, config.non_data_analysis_folder_name)

            if await self._folder_exists(template_non_data_analysis):
                if await self._copy_folder(template_non_data_analysis, new_non_data_analysis):
                    copied_folders.append(config.non_data_analysis_folder_name)
                    copy_messages.append(f"Copied {config.non_data_analysis_folder_name} folder")
                else:
                    copy_messages.append(f"Failed to copy {config.non_data_analysis_folder_name} folder")
            else:
                copy_messages.append(f"{config.non_data_analysis_folder_name} folder not found in template")

            if copied_folders:
                success_msg = f"Copied folders: {', '.join(copied_folders)}"
                logger.info(success_msg)
                return {
                    "success": True,
                    "status": "copied",
                    "message": success_msg,
                    "copied_folders": copied_folders
                }
            else:
                # Create folders and copy individual files from template root
                try:
                    await aiofiles.os.makedirs(os.path.join(new_dir, config.data_analysis_folder_name), exist_ok=True)
                    await aiofiles.os.makedirs(os.path.join(new_dir, config.non_data_analysis_folder_name), exist_ok=True)
                    logger.info("Created empty data_analysis and non_data_analysis folders")
                except Exception:
                    await asyncio.get_event_loop().run_in_executor(
                        None, lambda: os.makedirs(os.path.join(new_dir, config.data_analysis_folder_name), exist_ok=True)
                    )
                    await asyncio.get_event_loop().run_in_executor(
                        None, lambda: os.makedirs(os.path.join(new_dir, config.non_data_analysis_folder_name), exist_ok=True)
                    )

                # Copy individual files from template root to appropriate folders
                copied_files = await self._copy_individual_files_from_template(template_dir, new_dir)

                if copied_files:
                    success_msg = f"Created folder structure and copied {len(copied_files)} individual files: {', '.join(copied_files)}"
                    logger.info(success_msg)
                    return {
                        "success": True,
                        "status": "individual_files_copied",
                        "message": success_msg,
                        "copied_files": copied_files
                    }
                else:
                    warning_msg = f"Template folders not found, created empty structure. {'; '.join(copy_messages)}"
                    logger.warning(warning_msg)
                    return {
                        "success": True,  # Still success since we created the structure
                        "status": "created_empty",
                        "message": warning_msg,
                        "copy_attempts": copy_messages
                    }

        except Exception as e:
            logger.error(f"Error copying project folders from {template_project_id} to {new_project_id}: {str(e)}")
            return {
                "success": False,
                "status": "error",
                "message": f"Folder copy failed: {str(e)}"
            }

    async def _folder_exists(self, folder_path: str) -> bool:
        """
        Check if a folder exists using async I/O.

        Returns:
            bool: True if folder exists, False otherwise
        """
        try:
            try:
                return await aiofiles.os.path.exists(folder_path) and await aiofiles.os.path.isdir(folder_path)
            except Exception:
                return await asyncio.get_event_loop().run_in_executor(
                    None, lambda: os.path.exists(folder_path) and os.path.isdir(folder_path)
                )
        except Exception:
            return False

    async def _copy_folder(self, source_folder: str, target_folder: str) -> bool:
        """
        Copy an entire folder using async I/O with fallback to sync.

        Returns:
            bool: True if copy was successful, False otherwise
        """
        try:
            # Use lambda to properly pass the dirs_exist_ok parameter
            def copy_tree():
                return shutil.copytree(source_folder, target_folder, dirs_exist_ok=True)

            await asyncio.get_event_loop().run_in_executor(None, copy_tree)
            return True

        except Exception as e:
            logger.error(f"Error copying folder {source_folder} to {target_folder}: {str(e)}")
            return False

    async def _copy_individual_files_from_template(self, template_dir: str, new_dir: str) -> List[str]:
        """
        Copy individual files from template root directory to appropriate folders in new project.

        Args:
            template_dir (str): Template project directory
            new_dir (str): New project directory

        Returns:
            List[str]: List of successfully copied files
        """
        copied_files = []

        try:
            # Check if template directory exists
            template_exists = await self._folder_exists(template_dir)
            if not template_exists:
                logger.warning(f"Template directory not found: {template_dir}")
                return copied_files

            # Get list of files in template root directory
            try:
                files_in_template = await asyncio.get_event_loop().run_in_executor(
                    None, os.listdir, template_dir
                )
            except Exception as e:
                logger.error(f"Error listing template directory {template_dir}: {str(e)}")
                return copied_files

            # Copy each file to appropriate folder based on file type
            # Group files by their base name to handle .enc/.pkl files together
            processed_base_names = set()

            for file_name in files_in_template:
                source_file_path = os.path.join(template_dir, file_name)

                # Skip if it's a directory
                try:
                    is_file = await asyncio.get_event_loop().run_in_executor(
                        None, os.path.isfile, source_file_path
                    )
                    if not is_file:
                        continue
                except Exception:
                    continue

                # Get file extension and base name
                file_extension = file_name.split('.')[-1].lower() if '.' in file_name else ''
                file_base_name = os.path.splitext(file_name)[0]

                # Skip if we've already processed this base name
                if file_base_name in processed_base_names:
                    continue

                # Determine file type and target folder
                if file_extension in ['csv', 'xlsx']:
                    target_folder = os.path.join(new_dir, config.data_analysis_folder_name)
                    # For CSV/XLSX: copy actual file + .pkl file
                    files_to_copy = [
                        f"{file_base_name}.{file_extension}",  # actual file
                        f"{file_base_name}.pkl"  # .pkl file
                    ]
                elif file_extension in ['enc', 'pkl']:
                    target_folder = os.path.join(new_dir, config.non_data_analysis_folder_name)
                    # For .enc/.pkl: copy both .enc and .pkl files
                    files_to_copy = [
                        f"{file_base_name}.enc",  # .enc file
                        f"{file_base_name}.pkl"   # .pkl file
                    ]
                else:
                    # For other extensions, treat as non-data files
                    target_folder = os.path.join(new_dir, config.non_data_analysis_folder_name)
                    files_to_copy = [
                        f"{file_base_name}.enc",  # .enc file
                        f"{file_base_name}.pkl"   # .pkl file
                    ]

                # Copy all required files for this base name
                for file_to_copy in files_to_copy:
                    source_path = os.path.join(template_dir, file_to_copy)
                    target_path = os.path.join(target_folder, file_to_copy)

                    try:
                        file_exists = await asyncio.get_event_loop().run_in_executor(
                            None, os.path.exists, source_path
                        )

                        if file_exists:
                            await asyncio.get_event_loop().run_in_executor(
                                None, shutil.copy2, source_path, target_path
                            )
                            copied_files.append(file_to_copy)
                            logger.info(f"Copied individual file: {file_to_copy} to {target_folder}")
                        else:
                            logger.warning(f"Required file not found: {source_path}")
                    except Exception as e:
                        logger.error(f"Error copying file {file_to_copy}: {str(e)}")
                        continue

                # Mark this base name as processed
                processed_base_names.add(file_base_name)

            return copied_files

        except Exception as e:
            logger.error(f"Error in _copy_individual_files_from_template: {str(e)}")
            return copied_files

    async def _copy_files_for_template_creation(self, original_project: Dict[str, Any], template_project_id: str):
        """
        Copy files from original project to template location during template creation.

        Args:
            original_project (Dict[str, Any]): The original project document
            template_project_id (str): The template project ID to copy files to
        """
        try:
            file_details = original_project.get("file_details", [])
            if not file_details:
                logger.info("No files to copy for template creation")
                return

            # Get original project directory
            original_project_id = original_project.get("project_id")
            original_project_dir = os.path.join("files", original_project_id)
            template_project_dir = os.path.join("files", template_project_id)

            # Copy entire project folders from original to template location
            copied_folders = []
            copy_messages = []

            # Copy data_analysis folder
            original_data_analysis = os.path.join(original_project_dir, config.data_analysis_folder_name)
            template_data_analysis = os.path.join(template_project_dir, config.data_analysis_folder_name)

            if await self._folder_exists(original_data_analysis):
                if await self._copy_folder(original_data_analysis, template_data_analysis):
                    copied_folders.append(config.data_analysis_folder_name)
                    copy_messages.append(f"Copied {config.data_analysis_folder_name} folder")
                else:
                    copy_messages.append(f"Failed to copy {config.data_analysis_folder_name} folder")
            else:
                copy_messages.append(f"{config.data_analysis_folder_name} folder not found in original")

            # Copy non_data_analysis folder
            original_non_data_analysis = os.path.join(original_project_dir, config.non_data_analysis_folder_name)
            template_non_data_analysis = os.path.join(template_project_dir, config.non_data_analysis_folder_name)

            if await self._folder_exists(original_non_data_analysis):
                if await self._copy_folder(original_non_data_analysis, template_non_data_analysis):
                    copied_folders.append(config.non_data_analysis_folder_name)
                    copy_messages.append(f"Copied {config.non_data_analysis_folder_name} folder")
                else:
                    copy_messages.append(f"Failed to copy {config.non_data_analysis_folder_name} folder")
            else:
                copy_messages.append(f"{config.non_data_analysis_folder_name} folder not found in original")

            if copied_folders:
                logger.info(f"Template creation: copied folders {', '.join(copied_folders)} to template {template_project_id}")
            else:
                logger.warning(f"Template creation: no folders copied. {'; '.join(copy_messages)}")

        except Exception as e:
            logger.error(f"Error copying files for template creation: {str(e)}")

    async def create_templated_project_from_insights(self, project_id: str) -> Dict[str, Any]:
        """
        Create a templated project copy from an insights collection project.

        Args:
            project_id (str): The project ID from insights collection to copy

        Returns:
            Dict[str, Any]: Result containing success status and templated project data
        """
        try:
            # Get the original project from insights collection
            original_project = await self.insights_collection.find_one({"project_id": project_id})

            if not original_project:
                raise ValueError(f"Project with ID {project_id} not found in insights collection")

            # Generate template project ID
            template_project_id = self._generate_project_id()

            # Copy files from original project to template location
            await self._copy_files_for_template_creation(original_project, template_project_id)

            # Create templated project copy with updated file paths
            templated_project = self._create_templated_copy(original_project, template_project_id)

            # Check if templated project already exists
            existing_templated = await self.templated_projects_collection.find_one({
                "original_project_id": project_id
            })

            if existing_templated:
                # Update existing templated project
                await self.templated_projects_collection.replace_one(
                    {"original_project_id": project_id},
                    templated_project
                )
                logger.info(f"Updated existing templated project for original project {project_id}")
                action = "updated"
            else:
                # Insert new templated project
                result = await self.templated_projects_collection.insert_one(templated_project)
                templated_project["_id"] = result.inserted_id
                logger.info(f"Created new templated project for original project {project_id}")
                action = "created"

            return {
                "success": True,
                "action": action,
                "project_id": templated_project["project_id"],
                "original_project_id": project_id,
                "message": f"Templated project {action} successfully"
            }

        except Exception as e:
            logger.error(f"Error creating templated project from {project_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create templated project: {str(e)}"
            }

    def _create_templated_copy(self, original_project: Dict[str, Any], template_project_id: str) -> Dict[str, Any]:
        """
        Create a templated copy of a project, excluding user-specific data and timestamps.

        Args:
            original_project (Dict[str, Any]): The original project document
            template_project_id (str): The template project ID to use

        Returns:
            Dict[str, Any]: The templated project document
        """

        # Create base templated project structure
        templated_project = {
            "project_id": template_project_id,
            "original_project_id": original_project.get("project_id"),
            "title": original_project.get("title", config.DEFAULT_PROJECT_TITLE),
            "model_name": original_project.get("model_name", config.default_model_name),
            "is_templated_project": True,
            "created_at": datetime.now(timezone.utc).timestamp(),

            # Copy non-user-specific data
            "response": self._clean_responses(original_project.get("response", [])),
            "chat_response": self._clean_responses(original_project.get("chat_response", [])),
            "prompt": original_project.get("prompt", []),
            "chat_prompt": original_project.get("chat_prompt", []),
            "pages": original_project.get("pages", []),
            "full_text": original_project.get("full_text", []),
            "complete_chat": self._clean_complete_chat(original_project.get("complete_chat", [])),

            # Clean file details with updated template paths
            "file_details": self._clean_file_details(original_project.get("file_details", []), template_project_id),

            # Exclude user-specific fields
            "user_id": "",  # Keep empty as requested
            "assistant_id": "",  # Reset for template
            "thread_id": "",  # Reset for template

            # Set template-specific flags
            "isDeleted": False,
        }

        # Remove the original MongoDB _id and timestamp fields
        # These will be auto-generated when inserted into templated_projects collection

        return templated_project

    def _clean_file_details(self, file_details: List[Dict[str, Any]], template_project_id: str) -> List[Dict[str, Any]]:
        """
        Clean file details by removing timestamps and user-specific data, and update paths to template location.

        Args:
            file_details (List[Dict[str, Any]]): Original file details
            template_project_id (str): The template project ID for updating paths

        Returns:
            List[Dict[str, Any]]: Cleaned file details with updated paths
        """
        cleaned_details = []

        for file_detail in file_details:
            file_name = file_detail.get("file_name", "")
            file_type = file_detail.get("file_type", "")

            # Create new file path pointing to template location
            if file_type.lower() in ['csv', 'xlsx']:
                new_file_path = os.path.join("files", template_project_id, config.data_analysis_folder_name, file_name)
            else:
                new_file_path = os.path.join("files", template_project_id, config.non_data_analysis_folder_name, file_name)

            cleaned_detail = {
                "file_name": file_name,
                "file_type": file_type,
                "file_path": new_file_path,  # Updated path to template location
                "file_description": file_detail.get("file_description", ""),
                "custom_prompt": file_detail.get("custom_prompt", ""),
                "custom_prompt_content": file_detail.get("custom_prompt_content", ""),
                # Exclude file_id and any timestamps
                # file_id will need to be regenerated when the template is used
            }
            cleaned_details.append(cleaned_detail)

        return cleaned_details

    def _clean_responses(self, responses: List[Any]) -> List[Any]:
        """
        Clean response data by removing any user-specific information.

        Args:
            responses (List[Any]): Original responses

        Returns:
            List[Any]: Cleaned responses
        """
        # For now, keep responses as-is since they contain insights
        # In the future, you might want to clean specific fields within responses
        return responses

    def _clean_complete_chat(self, complete_chat: List[Any]) -> List[Any]:
        """
        Clean complete chat data by removing user-specific information.

        Args:
            complete_chat (List[Any]): Original complete chat data

        Returns:
            List[Any]: Cleaned complete chat data
        """
        # For now, keep complete chat as-is since it contains conversation flow
        # In the future, you might want to clean specific fields within chat data
        return complete_chat

    async def get_templated_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a templated project by its ID.

        Args:
            project_id (str): The templated project ID

        Returns:
            Optional[Dict[str, Any]]: The templated project document or None if not found
        """
        return await self.templated_projects_collection.find_one({
            "project_id": project_id
        })

    async def list_templated_projects(self, limit: int = 50, skip: int = 0) -> List[Dict[str, Any]]:
        """
        List all templated projects with pagination.

        Args:
            limit (int): Maximum number of projects to return
            skip (int): Number of projects to skip

        Returns:
            List[Dict[str, Any]]: List of templated projects
        """
        cursor = self.templated_projects_collection.find(
            {"isDeleted": {"$ne": True}},
            {
                "project_id": 1,
                "original_project_id": 1,
                "title": 1,
                "created_at": 1,
                "model_name": 1,
                "_id": 0
            }
        ).sort("created_at", -1).skip(skip).limit(limit)

        return await cursor.to_list(length=limit)

    async def import_all_templated_projects_to_insights(self, user_id: str) -> Dict[str, Any]:
        """
        Import all templated projects to insights collection for a specific user.

        Args:
            user_id (str): The user ID to assign to the imported projects

        Returns:
            Dict[str, Any]: Result containing success status and import details
        """
        try:
            # Get all non-deleted templated projects
            templated_projects_cursor = self.templated_projects_collection.find({
                "isDeleted": {"$ne": True},
                "is_templated_project": True
            })

            templated_projects = await templated_projects_cursor.to_list(length=None)

            if not templated_projects:
                return {
                    "success": True,
                    "imported_count": 0,
                    "skipped_count": 0,
                    "message": "No templated projects found to import"
                }

            imported_count = 0
            skipped_count = 0
            imported_projects = []

            for templated_project in templated_projects:
                try:
                    # Validate that the templated project has required fields
                    if not templated_project.get('project_id'):
                        logger.warning(f"Skipping templated project without project_id: {templated_project.get('_id', 'unknown')}")
                        skipped_count += 1
                        continue

                    # First, copy the project folders (data_analysis and non_data_analysis)
                    template_project_id = templated_project.get("project_id")
                    new_project_id = self._generate_project_id()

                    folder_copy_result = await self._copy_project_folders(template_project_id, new_project_id)

                    # Create insights project from templated project
                    insights_project = await self._create_insights_project_from_template(
                        templated_project, user_id, new_project_id
                    )

                    # Handle CSV/XLSX files - create assistant, thread, and upload files
                    insights_project = await self._handle_csv_xlsx_files(insights_project, user_id)

                    # Insert into insights collection (allow multiple imports of same template)
                    result = await self.insights_collection.insert_one(insights_project)
                    insights_project["_id"] = result.inserted_id

                    imported_count += 1
                    imported_projects.append({
                        "project_id": insights_project["project_id"],
                        "title": insights_project["title"]
                    })

                    logger.info(f"Imported templated project {templated_project['project_id']} as {insights_project['project_id']} for user {user_id}")

                except Exception as e:
                    project_info = {
                        'project_id': templated_project.get('project_id', 'missing'),
                        'title': templated_project.get('title', 'missing'),
                        '_id': str(templated_project.get('_id', 'missing'))
                    }
                    logger.error(f"Error importing templated project {project_info}: {str(e)}")
                    skipped_count += 1
                    continue

            return {
                "success": True,
                "imported_count": imported_count,
                "skipped_count": skipped_count,
                "total_templated_projects": len(templated_projects),
                "imported_projects": imported_projects,
                "message": f"Successfully imported {imported_count} templated projects for user {user_id}"
            }

        except Exception as e:
            logger.error(f"Error importing templated projects for user {user_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to import templated projects: {str(e)}"
            }

    async def _create_insights_project_from_template(self, templated_project: Dict[str, Any], user_id: str, new_project_id: str) -> Dict[str, Any]:
        """
        Create an insights project from a templated project for a specific user.
        The imported project will have is_templated_project: True and be assigned to the user.

        Args:
            templated_project (Dict[str, Any]): The templated project document
            user_id (str): The user ID to assign to the project
            new_project_id (str): The pre-generated project ID to use

        Returns:
            Dict[str, Any]: The insights project document with is_templated_project: True
        """

        # Create insights project structure
        insights_project = {
            "project_id": new_project_id,
            "user_id": user_id,  # Assign to the specified user
            "title": templated_project.get("title", config.DEFAULT_PROJECT_TITLE),
            "model_name": templated_project.get("model_name", config.default_model_name),
            "timestamp": datetime.now(timezone.utc).timestamp(),  # New timestamp for user's project

            # Copy content from template
            "response": templated_project.get("response", []),
            "chat_response": templated_project.get("chat_response", []),
            "prompt": templated_project.get("prompt", []),
            "chat_prompt": templated_project.get("chat_prompt", []),
            "pages": templated_project.get("pages", []),
            "full_text": templated_project.get("full_text", []),
            "complete_chat": templated_project.get("complete_chat", []),

            # Copy file details with updated paths
            "file_details": self._prepare_file_details_for_insights(
                templated_project.get("file_details", []), new_project_id
            ),

            # Reset for new user project
            "assistant_id": "",
            "thread_id": "",

            # Set flags
            "isDeleted": False,
            "is_templated_project": True  # Keep as templated project for user
        }

        return insights_project

    def _prepare_file_details_for_insights(self, template_file_details: List[Dict[str, Any]], new_project_id: str) -> List[Dict[str, Any]]:
        """
        Prepare file details from template for insights collection.
        Simply updates file paths to point to new project folder and generates file_ids.

        Args:
            template_file_details (List[Dict[str, Any]]): File details from template
            new_project_id (str): The new project ID for file organization

        Returns:
            List[Dict[str, Any]]: Prepared file details for insights with updated paths
        """
        prepared_details = []

        for file_detail in template_file_details:
            file_name = file_detail.get("file_name", "")
            file_type = file_detail.get("file_type", "")
            original_file_path = file_detail.get("file_path", "")

            # Update file path to point to new project folder
            # Replace the template project ID with new project ID in the path
            new_file_path = original_file_path
            if original_file_path:
                # Extract template project ID from path and replace with new project ID
                path_parts = original_file_path.split(os.sep)
                if len(path_parts) >= 2 and path_parts[0] == "files":
                    # Replace the project ID part (second element) with new project ID
                    path_parts[1] = new_project_id
                    new_file_path = os.sep.join(path_parts)

            # Generate file_id for non-csv/xlsx files
            file_id = ""
            if self._should_generate_file_id(file_type):
                file_id = self._generate_file_id()

            prepared_detail = {
                "file_name": file_name,
                "file_type": file_type,
                "file_path": new_file_path,  # Updated path with new project ID
                "file_description": file_detail.get("file_description", ""),
                "custom_prompt": file_detail.get("custom_prompt", ""),
                "custom_prompt_content": file_detail.get("custom_prompt_content", ""),
                "file_id": file_id,  # UUID for non-csv/xlsx, empty for csv/xlsx
                "template_file": True  # Flag to indicate this came from a template
            }
            prepared_details.append(prepared_detail)

            logger.info(f"Prepared file detail: {file_name} (type: {file_type}, file_id: {file_id or 'none'}, path: {new_file_path})")

        return prepared_details

    async def _handle_csv_xlsx_files(self, insights_project: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """
        Handle CSV/XLSX files by creating assistant, thread, and uploading files to get file_ids.

        Args:
            insights_project (Dict[str, Any]): The insights project with file details
            user_id (str): The user ID

        Returns:
            Dict[str, Any]: Updated insights project with assistant_id, thread_id, and file_ids
        """
        try:
            file_details = insights_project.get("file_details", [])
            csv_xlsx_files = [f for f in file_details if f.get("file_type", "").lower() in ['csv', 'xlsx']]

            if not csv_xlsx_files:
                logger.info("No CSV/XLSX files found, skipping assistant/thread creation")
                return insights_project

            logger.info(f"Found {len(csv_xlsx_files)} CSV/XLSX files, creating assistant and thread")

            # Create assistant and thread
            model = insights_project.get("model_name", config.default_model_name)

            # Get assistant instructions from config or use default
            assistant_instructions = getattr(config, 'assistant_instructions',
                "You are a helpful data analysis assistant. Analyze the uploaded files and provide insights.")
            assistant_name = getattr(config, 'assistant_name', "Data Analysis Assistant")

            # Initialize assistant
            assistant = await initialize_assistant(assistant_name, assistant_instructions, model)
            logger.info(f"Created assistant with ID: {assistant.id}")

            # Create thread
            thread = await asyncio.get_event_loop().run_in_executor(
                None, lambda: config.openai_client.beta.threads.create()
            )
            logger.info(f"Created thread with ID: {thread.id}")

            # Update insights project with assistant and thread IDs
            insights_project["assistant_id"] = assistant.id
            insights_project["thread_id"] = thread.id

            # Upload CSV/XLSX files one by one and update file_ids
            updated_file_details = []
            for file_detail in file_details:
                if file_detail.get("file_type", "").lower() in ['csv', 'xlsx']:
                    # Upload the file and get file_id
                    file_path = file_detail.get("file_path", "")
                    if file_path and os.path.exists(file_path):
                        try:
                            logger.info(f"Uploading file: {file_path}")
                            uploaded_files = await upload_files(file_path)

                            if uploaded_files and len(uploaded_files) > 0:
                                new_file_id = uploaded_files[0]['file_id']
                                file_detail["file_id"] = new_file_id
                                logger.info(f"Successfully uploaded {file_detail['file_name']} with file_id: {new_file_id}")
                            else:
                                logger.warning(f"Failed to upload file: {file_path}")
                                file_detail["file_id"] = ""
                        except Exception as e:
                            logger.error(f"Error uploading file {file_path}: {str(e)}")
                            file_detail["file_id"] = ""
                    else:
                        logger.warning(f"File not found or invalid path: {file_path}")
                        file_detail["file_id"] = ""

                updated_file_details.append(file_detail)

            # Update the insights project with updated file details
            insights_project["file_details"] = updated_file_details

            logger.info(f"Completed CSV/XLSX file handling. Assistant ID: {assistant.id}, Thread ID: {thread.id}")
            return insights_project

        except Exception as e:
            logger.error(f"Error handling CSV/XLSX files: {str(e)}")
            # Return original project if there's an error
            return insights_project

