from pydantic import BaseModel
from typing import List

class TitleUpdateRequest(BaseModel):
    project_id: str
    new_title: str

class TableData(BaseModel):
    chart_name: str
    type_of_chart: str
    chart_decription: str
    x_labels: List[str]
    y_labels: List[str]
    values: List[int]

class InsightExtraction(BaseModel):
    summary: str
    tables: List[TableData]
    suggested_questions: List[str]
class TitleUpdateRequest(BaseModel):
    project_id: str
    new_title: str

class Tools(BaseModel):
    Filename:List[str]
    FileTool:bool
    WebSeach:bool
    SelfAnswer:bool


class SourceSelecton(BaseModel):
    intent:str
    query:str
    source:List[Tools]
    clarification:str
    answer:str



# data analyst models(better to create a new file fo these)
class DataAnalysisRequest(BaseModel):
    prompt: str
    project_id: str
    user_id: str

class DataAnalysisResponse(BaseModel):
    summary: str
    suggested_questions: List[str]
    datasets: List[str]
    images: List[str]

class SetModelRequest(BaseModel):
    project_id: str
    model_name: str

class CreateTemplatedProjectRequest(BaseModel):
    project_id: str

class ImportTemplatedProjectsRequest(BaseModel):
    user_id: str



# Enhanced Compliance Report Model for structured output
class ComplianceReport(BaseModel):
    framework: str
    is_valid: bool = True  # Default to True for backward compatibility
    individual_factors_score: dict[str, float] = {}  # Each factor and its score
    average_score: float = 0.0
    summary: str = ""
    reason: str = ""  # Used when is_valid is False

class ComplianceRequest(BaseModel):
    project_id: str
    file_names: List[str]
    framework_type: str  # e.g., "HIPAA", "GDPR", "PCI-DSS", etc.

class MarkdownResponse(BaseModel):
    markdown_content: str

# Document Intent Extraction Model
class ContextIntent(BaseModel):
    intent_description: str
    keywords: List[str]

class DocumentIntentExtraction(BaseModel):
    context_intent: ContextIntent