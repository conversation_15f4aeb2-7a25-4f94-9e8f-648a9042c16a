# Main config file - imports from modular config files for backward compatibility
# This maintains the existing interface while organizing configs into logical modules

# Import all configurations from modular files
from config.models import *
from config.database import *
from config.api import *
from config.files import *
from config.urls import *
from config.logging import *
from config.server import *
from config.messages import *

# All configurations are now imported from modular files above
# This file maintains backward compatibility while organizing configs into logical modules