#!/usr/bin/env python3
"""
Simple script to print API keys from config/api.py module.
This demonstrates how to access the centralized API key configuration.
"""

# Import the API configuration module
from config.api import (
    print_api_keys_status,
    print_api_keys_masked,
    get_api_keys_dict,
    openai_api_key,
    deepseek_api_key,
    serper_api_key,
    google_api_key,
    USE_HARDCODED_KEYS
)

def main():
    """Main function to demonstrate API key access."""
    
    print("🔑 API Keys Configuration Demo")
    print()
    
    # Method 1: Print full status (shows actual keys - be careful!)
    print("Method 1: Full Status (SHOWS ACTUAL KEYS)")
    print_api_keys_status()
    print()
    
    # Method 2: Print masked status (safer for logs)
    print("Method 2: Masked Status (SAFER)")
    print_api_keys_masked()
    print()
    
    # Method 3: Access individual keys
    print("Method 3: Individual Key Access")
    print("-" * 40)
    print(f"OpenAI Key: {openai_api_key}")
    print(f"DeepSeek Key: {deepseek_api_key}")
    print(f"Serper Key: {serper_api_key}")
    print(f"Google Key: {google_api_key}")
    print()
    
    # Method 4: Get as dictionary
    print("Method 4: Dictionary Access")
    print("-" * 40)
    keys_dict = get_api_keys_dict()
    for key, value in keys_dict.items():
        print(f"{key}: {value}")
    print()
    
    # Show configuration source
    print("Configuration Info:")
    print("-" * 40)
    print(f"Using hardcoded keys: {USE_HARDCODED_KEYS}")
    print(f"Source: {'config/api.py hardcoded values' if USE_HARDCODED_KEYS else 'Environment variables'}")

if __name__ == "__main__":
    main()
