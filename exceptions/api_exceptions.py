from fastapi import HTTPException
from services.logging_utils import Logging<PERSON>elper
from config.messages import (
    ERROR_INVALID_AUTH, ERROR_TOKEN_LIMIT, ERROR_TIMEOUT, ERROR_CONNECTION,
    ERROR_UNSUPPORTED_FILE_TYPE, ERROR_UNSUPPORTED_FILE_TYPE_WITH_TYPE,
    ERROR_FILE_TOO_LARGE, ERROR_RATE_LIMIT, ERROR_QUOTA_EXCEEDED,
    ERROR_SERVER, ERROR_SERVICE_OVERLOADED, ERROR_REQUEST_RATE
)

def _get_context_or_fallback():
    """Get user/project context with fallback to None for error handling."""
    try:
        from services.logging_middleware import get_current_user_id, get_current_project_id
        return get_current_user_id(), get_current_project_id()
    except:
        return None, None

class APIError(HTTPException):
    """Base class for all API errors"""
    def __init__(self, status_code: int, detail: str):
        super().__init__(status_code=status_code, detail=detail)

class AuthenticationError(APIError):
    """Base class for authentication related errors"""
    def __init__(self, detail: str):
        super().__init__(status_code=401, detail=detail)

class InvalidAuthError(AuthenticationError):
    """Invalid authentication error"""
    def __init__(self):
        super().__init__(
            detail=ERROR_INVALID_AUTH
        )

class InvalidAPIKeyError(AuthenticationError):
    """Invalid API key error"""
    def __init__(self):
        super().__init__(
            detail="The AI API key provided appear to be invalid. Please contact your administrator to verify correct settings."
        )

class OrganizationAccessError(AuthenticationError):
    """Organization access error"""
    def __init__(self):
        super().__init__(
            detail="Your account needs to be associated with an organization to access this service. Please contact your administrator for assistance."
        )

class AccessRestrictedError(APIError):
    """Access restricted by region error"""
    def __init__(self):
        super().__init__(
            status_code=403,
            detail="Access to this service is currently restricted in your region. Please contact your administrator for more information."
        )

class RateLimitError(APIError):
    """Rate limit exceeded error"""
    def __init__(self):
        super().__init__(
            status_code=429,
            detail=ERROR_RATE_LIMIT
        )

class QuotaExceededError(APIError):
    """Quota exceeded error"""
    def __init__(self):
        super().__init__(
            status_code=429,
            detail=ERROR_QUOTA_EXCEEDED
        )

class ServerError(APIError):
    """Server error"""
    def __init__(self):
        super().__init__(
            status_code=500,
            detail=ERROR_SERVER
        )

class ServiceOverloadedError(APIError):
    """Service overloaded error"""
    def __init__(self):
        super().__init__(
            status_code=503,
            detail=ERROR_SERVICE_OVERLOADED
        )

class RequestRateError(APIError):
    """Request rate too high error"""
    def __init__(self):
        super().__init__(
            status_code=503,
            detail=ERROR_REQUEST_RATE
        )

class InvalidRequestError(APIError):
    """Invalid request error"""
    def __init__(self):
        super().__init__(
            status_code=400,
            detail="Your request contains invalid parameters. Please check your input and try again."
        )

class ContentFilterError(APIError):
    """Content filter error"""
    def __init__(self):
        super().__init__(
            status_code=400,
            detail="Your request contains content that cannot be processed. Please modify your input and try again."
        )

class ContextLengthExceededError(APIError):
    """Context length exceeded error"""
    def __init__(self):
        super().__init__(
            status_code=400,
            detail="Your request contains too much text to process. Please reduce the amount of text and try again."
        )

class TokenLimitError(APIError):
    """Token limit exceeded error"""
    def __init__(self):
        super().__init__(
            status_code=400,
            detail=ERROR_TOKEN_LIMIT
        )

class TimeoutError(APIError):
    """Request timeout error"""
    def __init__(self):
        super().__init__(
            status_code=504,
            detail=ERROR_TIMEOUT
        )

class ConnectionError(APIError):
    """Connection error"""
    def __init__(self):
        super().__init__(
            status_code=503,
            detail=ERROR_CONNECTION
        )

class UnsupportedFileTypeError(APIError):
    """Unsupported file type error"""
    def __init__(self, file_type: str = None):
        if file_type:
            detail = ERROR_UNSUPPORTED_FILE_TYPE_WITH_TYPE.format(file_type)
        else:
            detail = ERROR_UNSUPPORTED_FILE_TYPE
        super().__init__(status_code=415, detail=detail)

class FileTooLargeError(APIError):
    """File too large error"""
    def __init__(self):
        super().__init__(
            status_code=413,
            detail=ERROR_FILE_TOO_LARGE
        )

def _sanitize_error_message(error_message: str) -> str:
    """
    Sanitize error message to remove any provider/model specific information.

    Args:
        error_message: The original error message

    Returns:
        str: Sanitized error message with sensitive info removed
    """
    # Convert to lowercase for processing
    sanitized = error_message.lower()

    # List of sensitive terms to remove (provider names, model names, etc.)
    sensitive_terms = [
        'openai', 'gpt-4', 'gpt-3.5', 'gpt-4o', 'gpt-4-turbo',
        'deepseek', 'deepseek-chat', 'deepseek-coder',
        'claude', 'anthropic', 'gemini', 'google',
        'llama', 'meta', 'mistral', 'cohere',
        'text-davinci', 'text-curie', 'text-babbage', 'text-ada',
        'embedding', 'ada-002', 'text-embedding',
        'langchain', 'huggingface', 'transformers'
    ]

    # Remove sensitive terms
    for term in sensitive_terms:
        sanitized = sanitized.replace(term, '[AI_SERVICE]')

    return sanitized


def handle_openai_error(error: Exception) -> None:
    """
    Handle LLM API errors and raise appropriate custom exceptions.
    All error messages are sanitized to prevent leaking provider/model details.

    Args:
        error: The LLM API error to handle
    """
    # If the error is already one of our custom exceptions, just re-raise it
    if isinstance(error, APIError):
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.error("Re-raising custom API error", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(error), "error_type": type(error).__name__})
        raise error

    # Log the original error for debugging purposes (logs are internal, not sent to frontend)
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.error("LLM API error", user_id=user_id, project_id=project_id,
                       extra_data={"error": str(error), "error_type": type(error).__name__})

    # Sanitize error message to remove provider/model details
    error_message = _sanitize_error_message(str(error))

    # Authentication errors
    if "invalid authentication" in error_message:
        raise InvalidAuthError()
    elif "incorrect api key" in error_message or "invalid api key" in error_message:
        raise InvalidAPIKeyError()
    elif "must be a member of an organization" in error_message:
        raise OrganizationAccessError()

    # Access errors
    elif "country, region, or territory not supported" in error_message:
        raise AccessRestrictedError()

    # Rate limit errors
    elif "rate limit" in error_message:
        raise RateLimitError()
    elif "quota" in error_message or "exceeded your current quota" in error_message:
        raise QuotaExceededError()
    elif "slow down" in error_message or "please reduce your request frequency" in error_message:
        raise RequestRateError()

    # Server errors
    elif "server had an error" in error_message or "internal server error" in error_message:
        raise ServerError()
    elif "engine is currently overloaded" in error_message or "service unavailable" in error_message:
        raise ServiceOverloadedError()
    elif "timed out" in error_message or "timeout" in error_message:
        raise TimeoutError()
    elif "connection" in error_message and ("error" in error_message or "failed" in error_message):
        raise ConnectionError()

    # Content errors
    elif "content filter" in error_message or "content policy" in error_message:
        raise ContentFilterError()
    elif "maximum context length" in error_message or "context window" in error_message:
        raise ContextLengthExceededError()
    elif "token limit" in error_message or "tokens" in error_message and "exceed" in error_message:
        raise TokenLimitError()

    # Request errors
    elif "invalid request" in error_message or "invalid parameter" in error_message:
        raise InvalidRequestError()
    elif "file too large" in error_message or "exceeds the maximum allowed file size" in error_message:
        raise FileTooLargeError()
    elif "unsupported file type" in error_message or "file type not supported" in error_message:
        # For security, don't extract file type from error message as it might contain
        # sensitive information like model names or provider details
        raise UnsupportedFileTypeError()

    # For any unhandled errors, log and raise a generic server error
    else:
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.error("Unhandled OpenAI error", user_id=user_id, project_id=project_id,
                           extra_data={"error_message": error_message})
        raise ServerError()


def handle_llm_error(error: Exception) -> None:
    """
    Handle LLM-related errors from LangChain and different providers.
    This function distinguishes between LLM/API errors and general application errors.
    All error processing is done securely to prevent provider/model info leakage.

    Args:
        error: The error to handle (could be from LangChain, OpenAI, DeepSeek, etc.)
    """
    # If the error is already one of our custom exceptions, just re-raise it
    if isinstance(error, APIError):
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.error("Re-raising custom API error", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(error), "error_type": type(error).__name__})
        raise error

    # Log the original error for debugging purposes (logs are internal, not sent to frontend)
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.error("LLM error occurred", user_id=user_id, project_id=project_id,
                       extra_data={"error": str(error), "error_type": type(error).__name__})

    # Sanitize error message to remove provider/model details before processing
    error_message = _sanitize_error_message(str(error))
    error_type = type(error).__name__.lower()

    # Check if this is a known LLM/API related error (using sanitized message)
    if _is_llm_related_error(error, error_message, error_type):
        # Use the existing error handler for LLM-related errors
        # This will map the error to appropriate user-friendly messages
        handle_openai_error(error)
    else:
        # For non-LLM errors (code errors, logic errors, etc.),
        # return a generic technical difficulties message
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.error("Non-LLM error in LLM operation", user_id=user_id, project_id=project_id,
                           extra_data={"error_message": error_message, "error_type": error_type})
        raise ServerError()


def _is_llm_related_error(error: Exception, error_message: str, error_type: str) -> bool:
    """
    Determine if an error is LLM/API related or a general application error.

    Args:
        error: The original exception
        error_message: Lowercase error message
        error_type: Lowercase error type name

    Returns:
        bool: True if this is an LLM/API related error, False otherwise
    """
    # LangChain specific error types
    langchain_error_types = [
        'outputparserexception',
        'langchainexception',
        'ratelimiterror',
        'apierror',
        'authenticationerror',
        'permissionerror'
    ]

    # LLM/API specific error patterns in messages (excluding provider names for security)
    llm_error_patterns = [
        'api key',
        'authentication',
        'authorization',
        'rate limit',
        'quota',
        'token',
        'invalid request',
        'bad request',
        'timeout',
        'connection',
        'service unavailable',
        'server error',
        'internal server error',
        'bad gateway',
        'gateway timeout',
        'too many requests',
        'context length',
        'maximum context length',
        'content policy',
        'safety',
        'moderation',
        'billing',
        'usage',
        'organization',
        'country',
        'region',
        'territory'
        # Note: Removed 'model', 'deepseek', 'openai' to prevent provider info leakage
    ]

    # Check error type
    if any(llm_type in error_type for llm_type in langchain_error_types):
        return True

    # Check error message patterns
    if any(pattern in error_message for pattern in llm_error_patterns):
        return True

    # Check if error has attributes that suggest it's from an HTTP/API client
    if hasattr(error, 'status_code') or hasattr(error, 'response'):
        return True

    # Check for common HTTP client error types
    http_error_types = ['httperror', 'connectionerror', 'timeout', 'requestexception']
    if any(http_type in error_type for http_type in http_error_types):
        return True

    return False


def handle_general_error(error: Exception) -> None:
    """
    Handle general application errors that are not LLM-related.
    Always returns the generic technical difficulties message.

    Args:
        error: The error to handle
    """
    # If the error is already one of our custom exceptions, just re-raise it
    if isinstance(error, APIError):
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.error("Re-raising custom API error", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(error), "error_type": type(error).__name__})
        raise error

    # Log the original error for debugging purposes
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.error("General application error", user_id=user_id, project_id=project_id,
                       extra_data={"error": str(error), "error_type": type(error).__name__})

    # Always return generic error message for non-LLM errors
    raise ServerError()