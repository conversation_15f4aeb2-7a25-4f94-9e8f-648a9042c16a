"""
Comprehensive Token Tracking Service
Ensures no LLM or Assistant usage is left without tracking tokens.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
import json
from services.token_utils import get_tokens, estimate_tokens_from_messages, estimate_output_tokens
from services.logging_utils import LoggingHelper
import config

logger = logging.getLogger(__name__)


@dataclass
class TokenEvent:
    """Individual token consumption event"""
    timestamp: str
    operation_type: str  # "content_moderation", "intent_generation", "query_routing", "main_query", "title_generation", "assistant_query", "compliance_report"
    operation_subtype: str  # "file_upload", "query_processing", "streaming", "structured_output"
    model_name: str
    provider: str  # "openai", "deepseek"
    model_tier: str  # "premium", "economy", "vision"
    input_tokens: int
    output_tokens: int
    total_tokens: int
    operation_details: Dict[str, Any]
    success: bool = True
    error_message: Optional[str] = None


class TokenEventLogger:
    """
    Comprehensive token event logger that tracks ALL LLM and Assistant usage.
    Thread-safe and designed to capture every token consumption event.
    """
    
    def __init__(self, project_id: str, user_id: str = None, query_id: str = None, mongo_manager=None):
        self.project_id = project_id
        self.user_id = user_id
        self.query_id = query_id or self._generate_query_id()
        self.response_id = None  # Will be set when response is generated
        self.events: List[TokenEvent] = []
        self._total_input_tokens = 0
        self._total_output_tokens = 0
        self.mongo_manager = mongo_manager

        # Use project+user specific logging
        LoggingHelper.info(f"TokenEventLogger initialized",
                          user_id=self.user_id, project_id=self.project_id,
                          extra_data={"query_id": self.query_id})
    
    def _generate_query_id(self) -> str:
        """Generate unique query ID for tracking"""
        import uuid
        return f"query_{uuid.uuid4().hex[:8]}"

    def set_response_id(self, response_id: str) -> None:
        """Set the response ID to link token events with the response"""
        self.response_id = response_id
        LoggingHelper.info("Token events linked to response_id",
                          user_id=self.user_id, project_id=self.project_id,
                          extra_data={"response_id": response_id, "query_id": self.query_id})
    
    def log_event(self, 
                  operation_type: str,
                  model_name: str,
                  input_tokens: int,
                  output_tokens: int,
                  operation_subtype: str = "general",
                  operation_details: Dict[str, Any] = None,
                  success: bool = True,
                  error_message: str = None) -> None:
        """
        Log individual token consumption event.
        This method MUST be called for every LLM interaction.
        """
        try:
            # Resolve model information
            provider, model_tier = self._resolve_model_info(model_name)
            
            # Create token event
            event = TokenEvent(
                timestamp=datetime.now(timezone.utc).isoformat(),
                operation_type=operation_type,
                operation_subtype=operation_subtype,
                model_name=model_name,
                provider=provider,
                model_tier=model_tier,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=input_tokens + output_tokens,
                operation_details=operation_details or {},
                success=success,
                error_message=error_message
            )
            
            # Add to events list
            self.events.append(event)
            
            # Update totals
            self._total_input_tokens += input_tokens
            self._total_output_tokens += output_tokens
            
            # Structured logging for monitoring
            self._log_to_application_logs(event)

            LoggingHelper.debug(f"Token event logged: {operation_type} - Input: {input_tokens}, Output: {output_tokens}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"operation_type": operation_type, "model_name": model_name,
                                         "input_tokens": input_tokens, "output_tokens": output_tokens})

        except Exception as e:
            LoggingHelper.error(f"Failed to log token event: {str(e)}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"error": str(e), "operation_type": operation_type})
            # Don't let token logging failures break the main flow
    
    def _resolve_model_info(self, model_name: str) -> tuple[str, str]:
        """Resolve provider and tier from model name"""
        try:
            from services.llm_service import resolve_model_config, get_model_tier
            provider, _ = resolve_model_config(model_name)
            tier = get_model_tier(model_name)
            return provider, tier
        except Exception as e:
            LoggingHelper.warning(f"Could not resolve model info for {model_name}: {e}",
                                 user_id=self.user_id, project_id=self.project_id,
                                 extra_data={"model_name": model_name, "error": str(e)})
            return "unknown", "unknown"
    
    def _log_to_application_logs(self, event: TokenEvent) -> None:
        """Log token event to application logs for monitoring"""
        log_data = {
            "event_type": "TOKEN_CONSUMPTION",
            "project_id": self.project_id,
            "query_id": self.query_id,
            "user_id": self.user_id,
            **asdict(event)
        }

        # Remove sensitive data from logs
        if "operation_details" in log_data:
            details = log_data["operation_details"].copy()
            # Truncate long text fields
            for key in ["query_text", "response_text", "context"]:
                if key in details and isinstance(details[key], str):
                    details[key] = details[key][:100] + "..." if len(details[key]) > 100 else details[key]
            log_data["operation_details"] = details

        # Enhanced logging with step-by-step details using project+user logging
        LoggingHelper.info(
            f"TOKEN_CONSUMPTION - Step: {event.operation_type} | "
            f"Subtype: {event.operation_subtype} | "
            f"Model: {event.model_name} | "
            f"Input: {event.input_tokens} | "
            f"Output: {event.output_tokens} | "
            f"Total: {event.total_tokens} | "
            f"Running Total Input: {self._total_input_tokens} | "
            f"Running Total Output: {self._total_output_tokens} | "
            f"Success: {event.success}",
            user_id=self.user_id, project_id=self.project_id,
            extra_data=log_data
        )
    
    def get_total_tokens(self) -> tuple[int, int]:
        """Get total input and output tokens for this query"""
        return self._total_input_tokens, self._total_output_tokens

    def log_step_summary(self, step_name: str, additional_info: str = "") -> None:
        """Log a summary of token consumption up to this step"""
        LoggingHelper.info(
            f"TOKEN_STEP_SUMMARY - {step_name} | "
            f"Total Events: {len(self.events)} | "
            f"Cumulative Input: {self._total_input_tokens} | "
            f"Cumulative Output: {self._total_output_tokens} | "
            f"Cumulative Total: {self._total_input_tokens + self._total_output_tokens}"
            f"{' | ' + additional_info if additional_info else ''}",
            user_id=self.user_id, project_id=self.project_id,
            extra_data={
                "step_name": step_name,
                "total_events": len(self.events),
                "cumulative_input": self._total_input_tokens,
                "cumulative_output": self._total_output_tokens,
                "cumulative_total": self._total_input_tokens + self._total_output_tokens,
                "additional_info": additional_info
            }
        )

    async def save_to_mongodb(self) -> Optional[str]:
        """Save all token events to MongoDB"""
        if not self.mongo_manager:
            LoggingHelper.warning("No mongo_manager provided - cannot save token events to database",
                                 user_id=self.user_id, project_id=self.project_id)
            return None

        if not self.events:
            LoggingHelper.info("No token events to save",
                              user_id=self.user_id, project_id=self.project_id)
            return None

        try:
            # Convert events to dict format for MongoDB
            events_data = [asdict(event) for event in self.events]

            # Save to MongoDB with response_id link
            result = await self.mongo_manager.save_token_events(
                project_id=self.project_id,
                query_id=self.query_id,
                user_id=self.user_id,
                events=events_data,
                response_id=self.response_id
            )

            if result:
                LoggingHelper.info(f"Successfully saved {len(self.events)} token events to MongoDB",
                                  user_id=self.user_id, project_id=self.project_id,
                                  extra_data={"mongodb_id": result, "events_count": len(self.events),
                                            "response_id": self.response_id, "query_id": self.query_id})
            else:
                LoggingHelper.error("Failed to save token events to MongoDB",
                                   user_id=self.user_id, project_id=self.project_id,
                                   extra_data={"events_count": len(self.events)})

            return result
        except Exception as e:
            LoggingHelper.error(f"Error saving token events to MongoDB: {str(e)}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"error": str(e), "events_count": len(self.events)})
            return None

    def get_events_summary(self) -> List[Dict[str, Any]]:
        """Get detailed breakdown of all token events"""
        return [asdict(event) for event in self.events]
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """Get comprehensive usage summary"""
        operations_breakdown = {}
        
        # Group by operation type
        for event in self.events:
            op_type = event.operation_type
            if op_type not in operations_breakdown:
                operations_breakdown[op_type] = {
                    "input": 0, "output": 0, "total": 0, "count": 0
                }
            
            operations_breakdown[op_type]["input"] += event.input_tokens
            operations_breakdown[op_type]["output"] += event.output_tokens
            operations_breakdown[op_type]["total"] += event.total_tokens
            operations_breakdown[op_type]["count"] += 1
        
        return {
            "total_input_tokens": self._total_input_tokens,
            "total_output_tokens": self._total_output_tokens,
            "total_tokens": self._total_input_tokens + self._total_output_tokens,
            "total_events": len(self.events),
            "operations_breakdown": operations_breakdown,
            "query_id": self.query_id,
            "project_id": self.project_id
        }
    
    def log_assistant_usage(self, thread_id: str, run_id: str, 
                           input_tokens: int, output_tokens: int,
                           operation_details: Dict[str, Any] = None) -> None:
        """Log OpenAI Assistant API usage"""
        self.log_event(
            operation_type="assistant_query",
            model_name="openai_assistant",  # Will be resolved to actual model
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            operation_subtype="assistant_api",
            operation_details={
                "thread_id": thread_id,
                "run_id": run_id,
                **(operation_details or {})
            }
        )
    
    def log_streaming_usage(self, operation_type: str, model_name: str,
                           estimated_input_tokens: int, 
                           accumulated_output_tokens: int,
                           operation_details: Dict[str, Any] = None) -> None:
        """Log streaming LLM usage"""
        self.log_event(
            operation_type=operation_type,
            model_name=model_name,
            input_tokens=estimated_input_tokens,
            output_tokens=accumulated_output_tokens,
            operation_subtype="streaming",
            operation_details=operation_details
        )
    
    def log_failed_operation(self, operation_type: str, model_name: str,
                            estimated_input_tokens: int,
                            error_message: str,
                            operation_details: Dict[str, Any] = None) -> None:
        """Log failed LLM operations (still consumed input tokens)"""
        self.log_event(
            operation_type=operation_type,
            model_name=model_name,
            input_tokens=estimated_input_tokens,
            output_tokens=0,  # No output on failure
            operation_subtype="failed",
            operation_details=operation_details,
            success=False,
            error_message=error_message
        )


class TokenTrackingMixin:
    """
    Mixin class to add token tracking capabilities to any service.
    Ensures consistent token tracking across all LLM operations.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.token_logger: Optional[TokenEventLogger] = None
        self.unified_logger: Optional['UnifiedUsageLogger'] = None  # Forward reference to avoid circular import

    def set_token_logger(self, token_logger: TokenEventLogger) -> None:
        """Set the token logger for this service instance"""
        self.token_logger = token_logger

    def set_unified_logger(self, unified_logger: 'UnifiedUsageLogger') -> None:
        """Set the unified usage logger for this service instance"""
        self.unified_logger = unified_logger

    def _estimate_input_tokens(self, messages: Union[List[Dict], str], additional_context: str = "") -> int:
        """Estimate input tokens for messages"""
        try:
            if isinstance(messages, str):
                text = messages + additional_context
            elif isinstance(messages, list):
                text = " ".join([msg.get("content", "") for msg in messages]) + additional_context
            else:
                text = str(messages) + additional_context

            return get_tokens(text)
        except Exception as e:
            # Use project+user logging if token_logger is available
            if self.token_logger:
                LoggingHelper.warning(f"Failed to estimate input tokens: {e}",
                                     user_id=self.token_logger.user_id,
                                     project_id=self.token_logger.project_id,
                                     extra_data={"error": str(e)})
            else:
                LoggingHelper.warning(f"Failed to estimate input tokens: {e}")
            return 0

    def _estimate_output_tokens(self, response: Union[str, Dict, Any]) -> int:
        """Estimate output tokens for response"""
        try:
            if isinstance(response, str):
                return get_tokens(response)
            elif isinstance(response, dict):
                return get_tokens(json.dumps(response))
            else:
                return get_tokens(str(response))
        except Exception as e:
            # Use project+user logging if token_logger is available
            if self.token_logger:
                LoggingHelper.warning(f"Failed to estimate output tokens: {e}",
                                     user_id=self.token_logger.user_id,
                                     project_id=self.token_logger.project_id,
                                     extra_data={"error": str(e)})
            else:
                LoggingHelper.warning(f"Failed to estimate output tokens: {e}")
            return 0

    def _track_operation(self, operation_type: str, model_name: str,
                        input_tokens: int, output_tokens: int,
                        operation_subtype: str = "general",
                        operation_details: Dict[str, Any] = None,
                        success: bool = True,
                        error_message: str = None) -> None:
        """Track token usage with unified logger only"""
        # Track with unified usage logger (UNIFIED SYSTEM)
        if self.unified_logger:
            # Detect if this is a vision model and use appropriate logging method
            from services.llm_service import get_model_tier
            model_tier = get_model_tier(model_name)

            if model_tier == "vision":
                # Use dedicated vision logging for better analytics
                self.unified_logger.log_vision_usage(
                    model_name=model_name,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    operation_type=operation_type,
                    operation_subtype=operation_subtype,
                    operation_details=operation_details,
                    success=success,
                    error_message=error_message
                )
            else:
                # Use standard LLM logging for text models
                self.unified_logger.log_llm_usage(
                    model_name=model_name,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    operation_type=operation_type,
                    operation_subtype=operation_subtype,
                    operation_details=operation_details,
                    success=success,
                    error_message=error_message
                )
        else:
            # Warning if no unified logger is set
            LoggingHelper.warning(f"No unified usage logger set for {operation_type} operation",
                                 extra_data={"operation_type": operation_type, "model_name": model_name})


def create_token_logger(project_id: str, user_id: str = None, query_id: str = None, mongo_manager=None) -> TokenEventLogger:
    """
    DEPRECATED: Factory function to create token logger.
    Use create_unified_usage_logger instead for new implementations.
    """
    LoggingHelper.warning("create_token_logger is deprecated. Use create_unified_usage_logger instead.")
    return TokenEventLogger(project_id=project_id, user_id=user_id, query_id=query_id, mongo_manager=mongo_manager)


def ensure_token_tracking(func):
    """
    Decorator to ensure token tracking is enabled for LLM operations.
    Raises warning if no token logger is available.
    """
    def wrapper(*args, **kwargs):
        # Check if token_logger is passed or available in instance
        has_logger = False
        token_logger = None

        if 'token_logger' in kwargs and kwargs['token_logger'] is not None:
            has_logger = True
            token_logger = kwargs['token_logger']
        elif len(args) > 0 and hasattr(args[0], 'token_logger') and args[0].token_logger is not None:
            has_logger = True
            token_logger = args[0].token_logger

        if not has_logger:
            # Use project+user logging if available
            LoggingHelper.warning(f"Token tracking not enabled for {func.__name__} - tokens will not be tracked!",
                                 extra_data={"function_name": func.__name__})
        else:
            # Log that token tracking is enabled
            LoggingHelper.debug(f"Token tracking enabled for {func.__name__}",
                               user_id=token_logger.user_id if token_logger else None,
                               project_id=token_logger.project_id if token_logger else None,
                               extra_data={"function_name": func.__name__})

        return func(*args, **kwargs)

    return wrapper
