"""
Comprehensive Search Consumption Tracking Service
Ensures no web search usage is left without tracking consumption.
Similar to token tracking but for search API usage.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
import json
from services.token_utils import get_tokens, estimate_output_tokens
from services.logging_utils import LoggingHelper
import config

logger = logging.getLogger(__name__)


@dataclass
class SearchEvent:
    """Individual search consumption event"""
    timestamp: str
    operation_type: str  # "web_search", "search_routing"
    operation_subtype: str  # "duckduckgo", "serper_api", "google_search"
    service_provider: str  # "duckduckgo", "serper", "google"
    service_tier: str  # "free", "paid", "premium"
    query: str
    query_tokens: int
    response_text: str
    response_tokens: int
    total_tokens: int
    operation_details: Dict[str, Any]
    success: bool = True
    error_message: Optional[str] = None


class SearchEventLogger:
    """
    Comprehensive search event logger that tracks ALL web search usage.
    Thread-safe and designed to capture every search consumption event.
    """
    
    def __init__(self, project_id: str, user_id: str = None, query_id: str = None, mongo_manager=None):
        self.project_id = project_id
        self.user_id = user_id
        self.query_id = query_id or self._generate_query_id()
        self.response_id = None  # Will be set when response is generated
        self.events: List[SearchEvent] = []
        self._total_search_operations = 0
        self._total_query_tokens = 0
        self._total_response_tokens = 0
        self.mongo_manager = mongo_manager

        # Use project+user specific logging
        LoggingHelper.info(f"SearchEventLogger initialized",
                          user_id=self.user_id, project_id=self.project_id,
                          extra_data={"query_id": self.query_id})

    def _generate_query_id(self) -> str:
        """Generate unique query ID"""
        return f"search_query_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S_%f')}"
    
    def _resolve_service_info(self, service_provider: str) -> tuple[str, str]:
        """Resolve service provider to tier information"""
        service_tiers = {
            "duckduckgo": ("duckduckgo", "free"),
            "serper": ("serper", "paid"),
            "google": ("google", "paid"),
            "bing": ("bing", "paid")
        }
        return service_tiers.get(service_provider.lower(), (service_provider, "unknown"))
    


    def log_event(self, 
                  operation_type: str,
                  service_provider: str,
                  query: str,
                  response_text: str,
                  operation_subtype: str = "general",
                  operation_details: Dict[str, Any] = None,
                  success: bool = True,
                  error_message: str = None) -> None:
        """
        Log individual search consumption event.
        This method MUST be called for every search interaction.
        """
        try:
            # Resolve service information
            provider, service_tier = self._resolve_service_info(service_provider)
            
            # Set token counts to 0 for web search operations as cost is calculated per operation/call
            query_tokens = 0
            response_tokens = 0
            total_tokens = 0
            
            # Create search event
            event = SearchEvent(
                timestamp=datetime.now(timezone.utc).isoformat(),
                operation_type=operation_type,
                operation_subtype=operation_subtype,
                service_provider=provider,
                service_tier=service_tier,
                query=query[:500],  # Truncate long queries for storage
                query_tokens=query_tokens,
                response_text=response_text[:1000] if response_text else "",  # Truncate for storage
                response_tokens=response_tokens,
                total_tokens=total_tokens,
                operation_details=operation_details or {},
                success=success,
                error_message=error_message
            )
            
            # Add to events list
            self.events.append(event)
            
            # Update totals
            self._total_search_operations += 1
            self._total_query_tokens += query_tokens
            self._total_response_tokens += response_tokens
            
            # Structured logging for monitoring
            self._log_to_application_logs(event)

            LoggingHelper.debug(f"Search event logged: {operation_type} - Query tokens: {query_tokens}, Response tokens: {response_tokens}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"operation_type": operation_type, "service_provider": service_provider,
                                         "query_tokens": query_tokens, "response_tokens": response_tokens})

        except Exception as e:
            LoggingHelper.error(f"Error logging search event: {str(e)}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"error": str(e), "operation_type": operation_type,
                                         "service_provider": service_provider})

    def _log_to_application_logs(self, event: SearchEvent) -> None:
        """Log search event to application logs for monitoring"""
        log_data = {
            "query_id": self.query_id,
            "operation_type": event.operation_type,
            "operation_subtype": event.operation_subtype,
            "service_provider": event.service_provider,
            "service_tier": event.service_tier,
            "query_tokens": event.query_tokens,
            "response_tokens": event.response_tokens,
            "total_tokens": event.total_tokens,
            "success": event.success,
            "timestamp": event.timestamp
        }

        # Enhanced logging with step-by-step details using project+user logging
        LoggingHelper.info(
            f"SEARCH_CONSUMPTION - Step: {event.operation_type} | "
            f"Subtype: {event.operation_subtype} | "
            f"Service: {event.service_provider} | "
            f"Query Tokens: {event.query_tokens} | "
            f"Response Tokens: {event.response_tokens} | "
            f"Total Tokens: {event.total_tokens} | "
            f"Running Total Operations: {self._total_search_operations} | "
            f"Success: {event.success}",
            user_id=self.user_id, project_id=self.project_id,
            extra_data=log_data
        )

    def get_total_consumption(self) -> tuple[int, int, int]:
        """Get total search consumption metrics"""
        return (
            self._total_search_operations,
            self._total_query_tokens,
            self._total_response_tokens
        )

    def log_step_summary(self, step_name: str, additional_info: str = "") -> None:
        """Log a summary of search consumption up to this step"""
        LoggingHelper.info(
            f"SEARCH_STEP_SUMMARY - {step_name} | "
            f"Total Operations: {self._total_search_operations} | "
            f"Cumulative Query Tokens: {self._total_query_tokens} | "
            f"Cumulative Response Tokens: {self._total_response_tokens} | "
            f"Cumulative Total Tokens: {self._total_query_tokens + self._total_response_tokens}"
            f"{' | ' + additional_info if additional_info else ''}",
            user_id=self.user_id, project_id=self.project_id,
            extra_data={
                "step_name": step_name,
                "total_operations": self._total_search_operations,
                "cumulative_query_tokens": self._total_query_tokens,
                "cumulative_response_tokens": self._total_response_tokens,
                "cumulative_total_tokens": self._total_query_tokens + self._total_response_tokens,
                "additional_info": additional_info
            }
        )

    async def save_to_mongodb(self) -> Optional[str]:
        """Save all search events to MongoDB"""
        if not self.mongo_manager:
            LoggingHelper.warning("No mongo_manager provided - cannot save search events to database",
                                 user_id=self.user_id, project_id=self.project_id)
            return None

        if not self.events:
            LoggingHelper.info("No search events to save",
                              user_id=self.user_id, project_id=self.project_id)
            return None

        try:
            # Convert events to dict format for MongoDB
            events_data = [asdict(event) for event in self.events]

            # Save to MongoDB with response_id link
            result = await self.mongo_manager.save_search_events(
                project_id=self.project_id,
                query_id=self.query_id,
                user_id=self.user_id,
                events=events_data,
                response_id=self.response_id
            )

            if result:
                LoggingHelper.info(f"Successfully saved {len(self.events)} search events to MongoDB",
                                  user_id=self.user_id, project_id=self.project_id,
                                  extra_data={"mongodb_id": result, "events_count": len(self.events),
                                            "response_id": self.response_id, "query_id": self.query_id})
            else:
                LoggingHelper.error("Failed to save search events to MongoDB",
                                   user_id=self.user_id, project_id=self.project_id,
                                   extra_data={"events_count": len(self.events)})

            return result
        except Exception as e:
            LoggingHelper.error(f"Error saving search events to MongoDB: {str(e)}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"error": str(e), "events_count": len(self.events)})
            return None

    def get_events_summary(self) -> List[Dict[str, Any]]:
        """Get detailed breakdown of all search events"""
        return [asdict(event) for event in self.events]

    def set_response_id(self, response_id: str) -> None:
        """Set response ID to link search events with response"""
        self.response_id = response_id
        LoggingHelper.debug(f"Search events linked to response_id: {response_id}",
                           user_id=self.user_id, project_id=self.project_id,
                           extra_data={"response_id": response_id, "query_id": self.query_id})

    # Convenience methods for specific search operations
    def log_duckduckgo_search(self, query: str, response_text: str,
                             operation_details: Dict[str, Any] = None,
                             success: bool = True, error_message: str = None) -> None:
        """Log DuckDuckGo search usage"""
        self.log_event(
            operation_type="web_search",
            service_provider="duckduckgo",
            query=query,
            response_text=response_text,
            operation_subtype="duckduckgo_search",
            operation_details=operation_details,
            success=success,
            error_message=error_message
        )

    def log_serper_search(self, query: str, response_text: str,
                         operation_details: Dict[str, Any] = None,
                         success: bool = True, error_message: str = None) -> None:
        """Log Serper API search usage"""
        self.log_event(
            operation_type="web_search",
            service_provider="serper",
            query=query,
            response_text=response_text,
            operation_subtype="serper_api_search",
            operation_details=operation_details,
            success=success,
            error_message=error_message
        )
