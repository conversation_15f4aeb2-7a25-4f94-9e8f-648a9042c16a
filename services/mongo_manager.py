import time
from typing import List, Union, Dict, Optional, Any
import config
from bson import ObjectId
import uuid
import logging
from datetime import datetime, timezone
from fastapi import HTTPException
from exceptions.api_exceptions import handle_openai_error
from services.logging_utils import LoggingHelper

def _get_context_or_fallback(user_id=None, project_id=None):
    """Get user/project context with fallback to None for system operations."""
    try:
        from services.logging_middleware import get_current_user_id, get_current_project_id
        return (
            user_id or get_current_user_id(),
            project_id or get_current_project_id()
        )
    except:
        return user_id, project_id

class MongoManager:
    def __init__(self, collection, images_collection, tables_collection, compliance_collection, usage_events_collection=None):
        self.collection = collection
        self.images_collection = images_collection
        self.tables_collection = tables_collection
        self.compliance_collection = compliance_collection
        self.users_collection = config.users_collection
        # Initialize unified usage_events_collection - create it if not provided
        if usage_events_collection is None:
            self.usage_events_collection = config.db["usage_events"]
        else:
            self.usage_events_collection = usage_events_collection

    async def get_or_initialize_project(self, project_id: str):
        project = await self.collection.find_one({"project_id": project_id})
        exists = False
        created = False
        if not project:
            project = {
                "project_id": project_id,
                "response": [],
                "chat_response": [],
                "prompt": [],
                "pages": [],
                "user_id": "",  # Initialize as an empty string
                # "context": [],
                'file_details': [],
                'full_text': [],
                'title': config.DEFAULT_PROJECT_TITLE,  # Set default title
                'timestamp': datetime.now(timezone.utc).timestamp(),
                "assistant_id": "",
                "thread_id": "",
                "model_name": config.default_model_name,
                "complete_chat":[],
                "isDeleted": False,  # Add isDeleted field set to False by default
                "is_templated_project": False,  # Add is_templated_project field set to False by default

                # "intent": []
            }
            created = True
            await self.collection.insert_one(project)
        else:
            exists = True
            needs_update = self._normalize_project_data(project)
            # Update the database if normalization added missing fields
            if needs_update:
                await self.collection.update_one(
                    {"project_id": project_id},
                    {"$set": {"is_templated_project": project["is_templated_project"]}},
                    upsert=False
                )
        return project, exists, created

    def _normalize_project_data(self, project: dict):
        # Track if we need to update the database
        needs_update = False

        # Remove user_id from the list of fields to normalize as arrays
        fields = ["response", "prompt", "chat_prompt", "chat_response", "context"]
        for field in fields:
            if isinstance(project.get(field), str):
                project[field] = [project[field]]

        # Handle user_id specifically - ensure it's a string
        if project.get("user_id") and isinstance(project.get("user_id"), list):
            # If it's currently an array, take the first element or empty string
            project["user_id"] = project["user_id"][0] if project["user_id"] else ""

        # Ensure is_templated_project field exists for existing projects
        # Only set to False if the field doesn't exist - preserve True values
        if "is_templated_project" not in project:
            project["is_templated_project"] = False
            needs_update = True

        return needs_update

    async def update_project(self, project_id: str, update_fields):
        # Handle the case where update_fields is a string (new_title)
        if isinstance(update_fields, str):
            update_fields = {"title": update_fields}

        # Handle context field if it exists
        if 'context' in update_fields and isinstance(update_fields['context'], str):
            update_fields['context'] = [update_fields['context']]

        await self.collection.update_one({"project_id": project_id}, {"$set": update_fields}, upsert=True)




    async def append_to_project(self, project_id: str, append_fields: dict):
        update_operations = {}
        set_operations = {}

        for key, value in append_fields.items():
            if key == 'file_details':
                # Handle file_details as a list of objects
                set_operations["file_details"] = value
            else:
                # For other fields, use $push as before
                update_operations[key] = value

        # Perform the update
        if update_operations:
            await self.collection.update_one(
                {"project_id": project_id},
                {"$push": update_operations},
                upsert=True
            )

        if set_operations:
            await self.collection.update_one(
                {"project_id": project_id},
                {"$set": set_operations},
                upsert=True
            )

    async def get_project(self, project_id: str):
        return await self.collection.find_one({"project_id": project_id})


    async def get_file_details(self, project_id: str, user_id: str) -> List[Dict[str, str]]:
        # Updated to use exact string match for user_id
        project = await self.collection.find_one(
            {"project_id": project_id, "user_id": user_id}
        )
        if not project:
            return []
        return project.get("file_details", [])


    async def get_assistant_id(self, project_id: str) -> Union[str, None]:
        project = await self.collection.find_one(
            {"project_id": project_id},
            {"assistant_id": 1}
        )

        if project and 'assistant_id' in project:
            return project['assistant_id']
        else:
            return None

    async def get_thread_id(self, project_id: str) -> Union[str, None]:
        project = await self.collection.find_one(
            {"project_id": project_id},
            {"thread_id": 1}
        )

        if project and 'thread_id' in project:
            return project['thread_id']
        else:
            return None




    async def get_file_name(self, project_id: str) -> List[str]:
        project = await self.get_project(project_id)
        if project and 'file_details' in project:
            file_details = project['file_details']
            return [file_detail["file_name"] for file_detail in file_details if file_detail]
        return []



    async def save_response_and_prompt(self, project_id: str, prompt:str, response: dict, complete_chat=Union[dict, str]):
        """
        Save the response to the MongoDB project document.

        :param project_id: The ID of the project to which the response belongs.
        :param response: The response data to be saved.
        """
        # Prepare the update data

        print("--------------------------Response ------",response)
        update_data = {
            "prompt": prompt,
            "response": response,
            "chat_prompt":prompt,
            "chat_response":response,
                }

        # Update the project document
        await self.append_to_project(project_id, update_data)
        project_data, _, _ = await self.get_or_initialize_project(project_id)

        project_data["complete_chat"] = complete_chat
        await self.update_project(project_id, project_data)

    async def set_model_for_project(self, project_id: str, model_name: str):
        await self.collection.update_one(
            {"project_id": project_id},
            {"$set": {"model_name": model_name}},
            upsert=True
        )


    async def get_context(self, project_id: str):
        project = await self.collection.find_one(
            {"project_id": project_id}
        )
        return project.get("context")

    async def get_model_for_project(self, project_id: str):
        project = await self.collection.find_one({"project_id": project_id})
        return project.get("model_name", config.default_model_name) if project else config.default_model_name




    async def update_prompt_and_response_to_empty(self, project_id: str):
        await self.collection.update_one(
            {"project_id": project_id},
            {
                "$set": {
                    "chat_prompt": [],
                    "chat_response": []
                }
            },
            upsert=True
        )



    async def get_project_object_id(self, project_id: str) -> Optional[ObjectId]:
        """
        Get the MongoDB ObjectId for a project from the insights collection.

        Args:
            project_id (str): The project ID to look up

        Returns:
            Optional[ObjectId]: The MongoDB ObjectId if found, None otherwise
        """
        project = await self.collection.find_one({"project_id": project_id}, {"_id": 1})
        return project["_id"] if project else None


    async def save_images(self, project_object_id: ObjectId, images: List[Dict[str, Any]]) -> ObjectId:
        """Save images and return the ObjectId of the inserted document"""
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.info("Saving images in mongo db", user_id=user_id, project_id=project_id,
                          extra_data={"project_object_id": str(project_object_id), "image_count": len(images)})
        image_document = {
            "project_ref": project_object_id,
            "images": images
        }

        result = await self.images_collection.insert_one(image_document)
        return result.inserted_id

    async def save_tables(self, project_object_id: ObjectId, table_data: dict) -> ObjectId:
        """Save tables and return the ObjectId of the inserted document"""
        tables_to_save = table_data["tables"] if isinstance(table_data["tables"], list) else [table_data["tables"]]

        table_document = {
            "project_ref": project_object_id,
            "tables": tables_to_save
        }

        result = await self.tables_collection.insert_one(table_document)
        return result.inserted_id

    async def save_compliance_report(self, project_id: str, compliance_data: dict):
        try:
            user_id, _ = _get_context_or_fallback(project_id=project_id)
            LoggingHelper.info("Starting to save compliance report", user_id=user_id, project_id=project_id,
                              extra_data={"file_name": compliance_data.get('file_name'), "framework_type": compliance_data.get('framework_type')})

            # Add project reference and timestamp
            compliance_data.update({
                "project_id": project_id,
                "created_at": datetime.now(timezone.utc)
            })

            # Insert into compliance collection
            try:
                result = await self.compliance_collection.insert_one(compliance_data)
                compliance_id = result.inserted_id
                LoggingHelper.info("Inserted compliance report", user_id=user_id, project_id=project_id,
                                  extra_data={"compliance_id": str(compliance_id)})
            except Exception as e:
                LoggingHelper.error("Error inserting compliance report into compliance collection", user_id=user_id, project_id=project_id,
                                   extra_data={"error": str(e)})
                raise

            # Get current compliance_ids for the file
            try:
                file_data = await self.collection.find_one(
                    {
                        "project_id": project_id,
                        "file_details.file_name": compliance_data["file_name"]
                    },
                    {"file_details.$": 1}
                )

                current_compliance_ids = {}
                if file_data and 'file_details' in file_data and len(file_data['file_details']) > 0:
                    current_compliance_ids = file_data['file_details'][0].get('compliance_ids', {})

                # Update with new compliance ID while preserving other frameworks
                current_compliance_ids[compliance_data['framework_type']] = compliance_id

                LoggingHelper.info("Updated compliance IDs", user_id=user_id, project_id=project_id,
                                  extra_data={"current_compliance_ids": current_compliance_ids})

            except Exception as e:
                LoggingHelper.error("Error retrieving current compliance IDs", user_id=user_id, project_id=project_id,
                                   extra_data={"error": str(e)})
                raise

            # Update the compliance IDs while preserving existing data
            try:
                update_result = await self.collection.update_one(
                    {
                        "project_id": project_id,
                        "file_details.file_name": compliance_data["file_name"]
                    },
                    {
                        "$set": {
                            "file_details.$.compliance_ids": current_compliance_ids
                        }
                    }
                )
                LoggingHelper.info("Updated compliance ID reference", user_id=user_id, project_id=project_id,
                                  extra_data={"matched_count": update_result.matched_count, "modified_count": update_result.modified_count})
            except Exception as e:
                LoggingHelper.error("Error updating compliance ID reference", user_id=user_id, project_id=project_id,
                                   extra_data={"error": str(e)})
                raise

            LoggingHelper.info("Successfully saved compliance report", user_id=user_id, project_id=project_id,
                              extra_data={"file_name": compliance_data.get('file_name'), "framework_type": compliance_data.get('framework_type')})

        except Exception as e:
            user_id, _ = _get_context_or_fallback(project_id=project_id)
            LoggingHelper.error("Error in save_compliance_report", user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e)})
            raise

    async def get_compliance_reports(self, project_id: str):
        """
        Retrieve all compliance reports for a project.

        Args:
            project_id (str): The project ID

        Returns:
            List of compliance reports for the project
        """
        cursor = self.compliance_collection.find(
            {"project_id": project_id},
            {"_id": 0}  # Exclude MongoDB _id from results
        )
        return await cursor.to_list(length=None)

    async def insert_project(self, project_id: str, project_data: dict):
        """
        Insert a new project document into the MongoDB collection.

        Args:
            project_id (str): The ID of the project to insert
            project_data (dict): The project data to insert
        """
        try:
            user_id, _ = _get_context_or_fallback(project_id=project_id)
            LoggingHelper.info("Inserting new project", user_id=user_id, project_id=project_id)
            # Ensure project_id is in the data
            project_data["project_id"] = project_id

            # Ensure isDeleted field is set to False by default if not already present
            if "isDeleted" not in project_data:
                project_data["isDeleted"] = False

            # Ensure is_templated_project field is set to False by default if not already present
            # Only set to False if the field doesn't exist - preserve True values
            if "is_templated_project" not in project_data:
                project_data["is_templated_project"] = False

            # Insert the project data
            await self.collection.insert_one(project_data)
            LoggingHelper.info("Successfully inserted project", user_id=user_id, project_id=project_id)
        except Exception as e:
            user_id, _ = _get_context_or_fallback(project_id=project_id)
            LoggingHelper.error("Error inserting project", user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e)})
            # Fall back to update_project if insert fails
            LoggingHelper.info("Falling back to update_project", user_id=user_id, project_id=project_id)
            await self.update_project(project_id, project_data)

    async def check_user_exists(self, email: str) -> bool:
        """
        Check if a user exists in the users collection by their email.

        Args:
            email (str): The email address of the user to check

        Returns:
            bool: True if user exists, False otherwise

        Raises:
            Exception: If there's an error accessing the database
        """
        try:
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.info("Checking if user exists", user_id=user_id, project_id=project_id,
                              extra_data={"email": email})
            user = await self.users_collection.find_one({"email": email})
            exists = user is not None
            LoggingHelper.info("User existence check completed", user_id=user_id, project_id=project_id,
                              extra_data={"email": email, "exists": exists})
            return exists
        except Exception as e:
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.error("Error checking user existence", user_id=user_id, project_id=project_id,
                               extra_data={"email": email, "error": str(e)})
            raise HTTPException(status_code=500, detail="Error checking user existence")

    # DEPRECATED: Legacy method - use save_usage_events instead
    async def save_token_events(self, project_id: str, query_id: str, user_id: str, events: List[Dict[str, Any]], response_id: str = None) -> Optional[str]:
        """
        DEPRECATED: This method is deprecated. Use save_usage_events instead.
        Legacy method for backward compatibility only.
        """
        LoggingHelper.warning("save_token_events is deprecated. Use save_usage_events instead.",
                             user_id=user_id, project_id=project_id)
        return None

    # DEPRECATED: Legacy method - use get_usage_events instead
    async def get_token_events(self, project_id: str, query_id: str = None, response_id: str = None) -> List[Dict[str, Any]]:
        """
        DEPRECATED: This method is deprecated. Use get_usage_events instead.
        Legacy method for backward compatibility only.
        """
        user_id, _ = _get_context_or_fallback(project_id=project_id)
        LoggingHelper.warning("get_token_events is deprecated. Use get_usage_events instead.",
                             user_id=user_id, project_id=project_id)
        return []

    # DEPRECATED: Legacy method - use get_usage_events_by_response_id instead
    async def get_token_events_by_response_id(self, response_id: str) -> Optional[Dict[str, Any]]:
        """
        DEPRECATED: This method is deprecated. Use get_usage_events_by_response_id instead.
        Legacy method for backward compatibility only.
        """
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.warning("get_token_events_by_response_id is deprecated. Use get_usage_events_by_response_id instead.",
                             user_id=user_id, project_id=project_id)
        return None

    # DEPRECATED: Legacy method - use save_usage_events instead
    async def save_search_events(self, project_id: str, query_id: str, user_id: str, events: List[Dict[str, Any]], response_id: str = None) -> Optional[str]:
        """
        DEPRECATED: This method is deprecated. Use save_usage_events instead.
        Legacy method for backward compatibility only.
        """
        LoggingHelper.warning("save_search_events is deprecated. Use save_usage_events instead.",
                             user_id=user_id, project_id=project_id)
        return None

    # DEPRECATED: Legacy method - use get_usage_events instead
    async def get_search_events(self, project_id: str, query_id: str = None, response_id: str = None) -> List[Dict[str, Any]]:
        """
        DEPRECATED: This method is deprecated. Use get_usage_events instead.
        Legacy method for backward compatibility only.
        """
        user_id, _ = _get_context_or_fallback(project_id=project_id)
        LoggingHelper.warning("get_search_events is deprecated. Use get_usage_events instead.",
                             user_id=user_id, project_id=project_id)
        return []

    # DEPRECATED: Legacy method - use get_usage_events_by_response_id instead
    async def get_search_events_by_response_id(self, response_id: str) -> Optional[Dict[str, Any]]:
        """
        DEPRECATED: This method is deprecated. Use get_usage_events_by_response_id instead.
        Legacy method for backward compatibility only.
        """
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.warning("get_search_events_by_response_id is deprecated. Use get_usage_events_by_response_id instead.",
                             user_id=user_id, project_id=project_id)
        return None

    async def save_usage_events(self, project_id: str, query_id: str, user_id: str, events: List[Dict[str, Any]], response_id: str = None) -> Optional[str]:
        """
        Save unified usage events to the usage_events collection.

        Args:
            project_id (str): The project ID
            query_id (str): The query ID
            user_id (str): The user ID
            events (List[Dict[str, Any]]): List of usage events
            response_id (str, optional): The response ID to link with insights collection

        Returns:
            Optional[str]: The inserted document ID if successful, None otherwise
        """
        try:
            LoggingHelper.info("Saving unified usage events", user_id=user_id, project_id=project_id,
                              extra_data={"event_count": len(events), "query_id": query_id})

            # Calculate totals across all service categories
            total_operations = len(events)
            total_input_tokens = sum(event.get('input_tokens', 0) for event in events)
            total_output_tokens = sum(event.get('output_tokens', 0) for event in events)

            # Calculate totals by service category
            category_totals = {}
            for event in events:
                category = event.get('service_category', 'unknown')
                if category not in category_totals:
                    category_totals[category] = {
                        "operations": 0,
                        "input_tokens": 0,
                        "output_tokens": 0,
                        "total_tokens": 0
                    }
                category_totals[category]["operations"] += 1
                category_totals[category]["input_tokens"] += event.get('input_tokens', 0)
                category_totals[category]["output_tokens"] += event.get('output_tokens', 0)
                category_totals[category]["total_tokens"] += event.get('total_tokens', 0)

            usage_document = {
                "project_id": project_id,
                "query_id": query_id,
                "user_id": user_id,
                "response_id": response_id,  # Link to insights collection response
                "timestamp": datetime.now(timezone.utc),
                "total_operations": total_operations,
                "total_input_tokens": total_input_tokens,
                "total_output_tokens": total_output_tokens,
                "total_tokens": total_input_tokens + total_output_tokens,
                "category_totals": category_totals,  # Breakdown by service category
                "events": events
            }

            result = await self.usage_events_collection.insert_one(usage_document)
            LoggingHelper.info("Successfully saved unified usage events", user_id=user_id, project_id=project_id,
                              extra_data={"document_id": str(result.inserted_id), "total_operations": total_operations,
                                        "total_tokens": total_input_tokens + total_output_tokens})
            if response_id:
                LoggingHelper.info("Usage events linked to response", user_id=user_id, project_id=project_id,
                                  extra_data={"response_id": response_id})
            return str(result.inserted_id)
        except Exception as e:
            LoggingHelper.error("Error saving unified usage events", user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e), "query_id": query_id})
            return None

    async def get_usage_events(self, project_id: str, query_id: str = None, response_id: str = None, service_category: str = None) -> List[Dict[str, Any]]:
        """
        Retrieve unified usage events for a project and optionally filter by query, response, or service category.

        Args:
            project_id (str): The project ID
            query_id (str, optional): The query ID to filter by
            response_id (str, optional): The response ID to filter by
            service_category (str, optional): The service category to filter by (llm, search, etc.)

        Returns:
            List[Dict[str, Any]]: List of usage event documents
        """
        try:
            query = {"project_id": project_id}
            if query_id:
                query["query_id"] = query_id
            if response_id:
                query["response_id"] = response_id
            if service_category:
                query["events.service_category"] = service_category

            cursor = self.usage_events_collection.find(query).sort("timestamp", -1)
            events = await cursor.to_list(length=None)

            # Remove MongoDB _id from results
            for event in events:
                if "_id" in event:
                    del event["_id"]

            return events
        except Exception as e:
            user_id, _ = _get_context_or_fallback(project_id=project_id)
            LoggingHelper.error("Error retrieving unified usage events", user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e), "query_id": query_id, "response_id": response_id, "service_category": service_category})
            return []

    async def get_usage_events_by_response_id(self, response_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve unified usage events for a specific response ID.

        Args:
            response_id (str): The response ID

        Returns:
            Optional[Dict[str, Any]]: Usage event document if found, None otherwise
        """
        try:
            event = await self.usage_events_collection.find_one({"response_id": response_id})
            if event and "_id" in event:
                del event["_id"]
            return event
        except Exception as e:
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.error("Error retrieving unified usage events by response_id", user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e), "response_id": response_id})
            return None

    async def get_total_usage_for_project(self, project_id: str, start_date: datetime = None, end_date: datetime = None) -> Dict[str, Any]:
        """
        Get aggregated usage statistics for a project.

        Args:
            project_id (str): The project ID
            start_date (datetime, optional): Start date for filtering
            end_date (datetime, optional): End date for filtering

        Returns:
            Dict[str, Any]: Aggregated usage statistics
        """
        try:
            match_query = {"project_id": project_id}
            if start_date or end_date:
                timestamp_filter = {}
                if start_date:
                    timestamp_filter["$gte"] = start_date
                if end_date:
                    timestamp_filter["$lte"] = end_date
                match_query["timestamp"] = timestamp_filter

            pipeline = [
                {"$match": match_query},
                {"$group": {
                    "_id": None,
                    "total_operations": {"$sum": "$total_operations"},
                    "total_input_tokens": {"$sum": "$total_input_tokens"},
                    "total_output_tokens": {"$sum": "$total_output_tokens"},
                    "total_tokens": {"$sum": "$total_tokens"},
                    "unique_queries": {"$addToSet": "$query_id"}
                }},
                {"$project": {
                    "_id": 0,
                    "total_operations": 1,
                    "total_input_tokens": 1,
                    "total_output_tokens": 1,
                    "total_tokens": 1,
                    "unique_query_count": {"$size": "$unique_queries"}
                }}
            ]

            result = await self.usage_events_collection.aggregate(pipeline).to_list(1)
            return result[0] if result else {
                "total_operations": 0,
                "total_input_tokens": 0,
                "total_output_tokens": 0,
                "total_tokens": 0,
                "unique_query_count": 0
            }
        except Exception as e:
            user_id, _ = _get_context_or_fallback(project_id=project_id)
            LoggingHelper.error("Error getting total usage for project", user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e), "project_id": project_id})
            return {
                "total_operations": 0,
                "total_input_tokens": 0,
                "total_output_tokens": 0,
                "total_tokens": 0,
                "unique_query_count": 0
            }
