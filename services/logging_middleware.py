"""
Logging middleware for automatic project+user context extraction and logging.

This middleware automatically extracts user_id and project_id from requests
and sets up request-scoped logging context for the project+user logging system.
"""

import logging
import time
from typing import Optional, Dict, Any, Tuple
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON><PERSON><PERSON>pp
from contextvars import ContextVar
import uuid

from services.project_user_logger import get_project_user_logger, log_structured
from services.auth_utils import decode_jwt_token
import config

# Context variables for request-scoped data
request_user_id: ContextVar[Optional[str]] = ContextVar('request_user_id', default=None)
request_project_id: ContextVar[Optional[str]] = ContextVar('request_project_id', default=None)
request_id: ContextVar[Optional[str]] = ContextVar('request_id', default=None)


class ProjectUserLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to automatically extract user_id and project_id from requests
    and set up project+user specific logging context.
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.main_logger = logging.getLogger(__name__)
    
    async def dispatch(self, request: Request, call_next):
        """Process request and set up logging context."""
        
        # Generate unique request ID
        req_id = str(uuid.uuid4())
        request_id.set(req_id)
        
        # Extract user_id and project_id from request
        user_id, project_id = await self._extract_user_project_ids(request)
        
        # Set context variables
        request_user_id.set(user_id)
        request_project_id.set(project_id)
        
        # Get project+user logger if IDs are available
        if user_id and project_id and config.PROJECT_USER_LOGGING_ENABLED:
            logger = get_project_user_logger(user_id, project_id, "middleware")
        else:
            logger = self.main_logger
        
        # Log request start
        start_time = time.time()
        await self._log_request_start(request, user_id, project_id, req_id, logger)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Log successful response
            duration = time.time() - start_time
            await self._log_request_success(request, response, user_id, project_id, 
                                          req_id, duration, logger)
            
            return response
            
        except Exception as e:
            # Log error
            duration = time.time() - start_time
            await self._log_request_error(request, e, user_id, project_id, 
                                        req_id, duration, logger)
            raise
    
    async def _extract_user_project_ids(self, request: Request) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract user_id and project_id from various sources in the request.
        
        Sources checked (in order):
        1. Form data (for multipart requests)
        2. Query parameters
        3. Path parameters
        4. JWT token (for user_id)
        5. Request headers
        
        Returns:
            Tuple of (user_id, project_id) or (None, None) if not found
        """
        user_id = None
        project_id = None
        
        try:
            # 1. Skip form data parsing to avoid consuming request body
            # Form data will be parsed by FastAPI route handlers
            # We'll extract user_id and project_id from other sources first
            
            # 2. Check query parameters
            user_id = user_id or request.query_params.get("user_id")
            project_id = project_id or request.query_params.get("project_id")
            
            # 3. Check path parameters
            if hasattr(request, "path_params"):
                user_id = user_id or request.path_params.get("user_id")
                project_id = project_id or request.path_params.get("project_id")
            
            # 4. Extract user_id from JWT token if not found yet
            if not user_id:
                user_id = await self._extract_user_from_token(request)
            
            # 5. Check custom headers
            user_id = user_id or request.headers.get("X-User-ID")
            project_id = project_id or request.headers.get("X-Project-ID")
            
            # 6. For JSON requests, try to parse body (non-destructive)
            if not (user_id and project_id) and request.headers.get("content-type") == "application/json":
                try:
                    # This is a bit tricky as we can't consume the body here
                    # We'll skip this for now and rely on other methods
                    pass
                except Exception:
                    pass
            
        except Exception as e:
            # Use LoggingHelper for consistent logging
            from services.logging_utils import LoggingHelper
            LoggingHelper.warning(f"Error extracting user/project IDs: {e}", logger_name="middleware")
        
        return user_id, project_id
    
    async def _extract_user_from_token(self, request: Request) -> Optional[str]:
        """Extract user_id from JWT token."""
        try:
            auth_header = request.headers.get("Authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.split(" ")[1]
            payload = decode_jwt_token(token)
            
            # Extract user identifier from token
            # This could be 'sub', 'user_id', 'email', etc. depending on your token structure
            return payload.get('sub') or payload.get('user_id') or payload.get('email')
            
        except Exception:
            # Token parsing failed, not necessarily an error
            return None
    
    async def _log_request_start(self, request: Request, user_id: Optional[str], 
                               project_id: Optional[str], req_id: str, logger: logging.Logger):
        """Log request start with context information."""
        
        request_data = {
            "request_id": req_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers),
            "client_ip": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
        }
        
        # Remove sensitive headers
        sensitive_headers = ["authorization", "cookie", "x-api-key"]
        for header in sensitive_headers:
            if header in request_data["headers"]:
                request_data["headers"][header] = "[REDACTED]"
        
        if user_id and project_id and config.PROJECT_USER_LOGGING_ENABLED:
            # Use structured logging for project+user logs
            log_structured(
                user_id=user_id,
                project_id=project_id,
                level="INFO",
                message=f"Request started: {request.method} {request.url.path}",
                extra_data=request_data,
                logger_name="middleware"
            )
        else:
            # Use LoggingHelper for consistent logging (will use system/general if root logger disabled)
            from services.logging_utils import LoggingHelper
            LoggingHelper.info(f"Request started: {request.method} {request.url.path} [ID: {req_id}]", logger_name="middleware")
    
    async def _log_request_success(self, request: Request, response: Response,
                                 user_id: Optional[str], project_id: Optional[str],
                                 req_id: str, duration: float, logger: logging.Logger):
        """Log successful request completion."""
        
        response_data = {
            "request_id": req_id,
            "status_code": response.status_code,
            "duration_seconds": round(duration, 3),
            "response_headers": dict(response.headers),
        }
        
        if user_id and project_id and config.PROJECT_USER_LOGGING_ENABLED:
            log_structured(
                user_id=user_id,
                project_id=project_id,
                level="INFO",
                message=f"Request completed: {request.method} {request.url.path} - {response.status_code}",
                extra_data=response_data,
                logger_name="middleware"
            )
        else:
            # Use LoggingHelper for consistent logging
            from services.logging_utils import LoggingHelper
            LoggingHelper.info(
                f"Request completed: {request.method} {request.url.path} - "
                f"{response.status_code} [{duration:.3f}s] [ID: {req_id}]", logger_name="middleware"
            )
    
    async def _log_request_error(self, request: Request, error: Exception,
                               user_id: Optional[str], project_id: Optional[str],
                               req_id: str, duration: float, logger: logging.Logger):
        """Log request error."""
        
        error_data = {
            "request_id": req_id,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "duration_seconds": round(duration, 3),
        }
        
        if user_id and project_id and config.PROJECT_USER_LOGGING_ENABLED:
            log_structured(
                user_id=user_id,
                project_id=project_id,
                level="ERROR",
                message=f"Request failed: {request.method} {request.url.path}",
                extra_data=error_data,
                logger_name="middleware"
            )
        else:
            # Use LoggingHelper for consistent logging
            from services.logging_utils import LoggingHelper
            LoggingHelper.error(
                f"Request failed: {request.method} {request.url.path} - "
                f"{type(error).__name__}: {error} [{duration:.3f}s] [ID: {req_id}]", logger_name="middleware"
            )


def get_current_user_id() -> Optional[str]:
    """Get current request's user_id from context."""
    return request_user_id.get()


def get_current_project_id() -> Optional[str]:
    """Get current request's project_id from context."""
    return request_project_id.get()


def get_current_request_id() -> Optional[str]:
    """Get current request's unique ID from context."""
    return request_id.get()


def get_current_logger(logger_name: str = None) -> logging.Logger:
    """
    Get logger for current request context.
    
    Args:
        logger_name: Optional logger name
        
    Returns:
        Project+user specific logger if context is available, otherwise main logger
    """
    user_id = get_current_user_id()
    project_id = get_current_project_id()
    
    if user_id and project_id and config.PROJECT_USER_LOGGING_ENABLED:
        return get_project_user_logger(user_id, project_id, logger_name)
    else:
        return logging.getLogger(logger_name or __name__)


def log_current_context(level: str, message: str, extra_data: Dict[str, Any] = None, 
                       logger_name: str = None):
    """
    Log with current request context.
    
    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        message: Log message
        extra_data: Additional structured data to log
        logger_name: Optional logger name
    """
    user_id = get_current_user_id()
    project_id = get_current_project_id()
    req_id = get_current_request_id()
    
    # Add request ID to extra data
    if extra_data is None:
        extra_data = {}
    extra_data["request_id"] = req_id
    
    if user_id and project_id and config.PROJECT_USER_LOGGING_ENABLED:
        log_structured(user_id, project_id, level, message, extra_data, logger_name)
    else:
        # Use LoggingHelper for consistent fallback behavior
        from services.logging_utils import LoggingHelper
        LoggingHelper.log(level, f"{message} [ID: {req_id}]", logger_name=logger_name)
