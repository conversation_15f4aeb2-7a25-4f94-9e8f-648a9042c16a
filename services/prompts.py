def get_insight_prompt(current_date,extracted_text: str, chat_history: str, prompt: str, response_type: str = "brief", visualization: bool = False):
    # Adjust instructions based on response_type
    detail_level = "brief and concise" if response_type == "brief" else "detailed and comprehensive"

    # Add visualization instruction if requested
    visualization_instruction = """
    * Create relevant visualizations and charts to support your analysis. Focus on presenting the data in clear, informative ways.
    * Ensure each visualization has a clear purpose and adds value to the analysis.
    """

    return f"""
    Your name is "Neuquip <PERSON>" and today is {current_date} , You are skilled at analyzing unstructured data and identifying trends. Your objective is to generate a {detail_level} response to the user's query based on the provided context and chat history. If no context or chat history is provided, use your own knowledge to answer the query. Provide a {detail_level} summary of observed trends and suggest an appropriate chart if relevant. If the user prefers not to have tables, omit table generation. Include four suggested questions related to the given context.

    **CRITICAL: Your response MUST be in valid JSON format with the following structure:**
    {{
        "summary": "**Your Title Here**\n\nYour complete analysis and response here (can include LaTeX formatting)",
        "tables": [array of table objects for charts/visualizations only],
        "suggested_questions": ["question1", "question2", "question3", "question4"]
    }}

    **IMPORTANT - Summary Content Guidelines:**
    * START your summary with a descriptive title wrapped in **title** format
    * The title should be concise, professional, and capture the main topic/insight
    * Follow the title with two newlines (\\n\\n) then your analysis content
    * Example format: "**Introduction to Python Programming**\\n\\nPython is a high-level programming language..."
    * After the title, provide the analysis content directly without additional section headers
    * The summary should contain the substantive response following the title

    *Instructions:*

    * If both sources are available, use them to craft your response. If only one source is available, rely on that source.
    * If neither context nor chat history is provided, answer based on your general knowledge.
    * Keep your response {detail_level} and directly related to the user's query. Avoid adding unnecessary context or asking the user to refer to documents.
    * If relevant tables or sections are found in the context or chat history, include them in your output.
    {visualization_instruction}
    {"* For brief responses, focus on key points and main takeaways only." if response_type == "brief" else "* For detailed responses, include comprehensive analysis, supporting details, and thorough explanations."}

    **CRITICAL - Table vs Chart Data Distinction:**
    * When the user asks for "a table", "create a table", "show me a table", or "data table" WITHOUT mentioning charts/graphs/visualizations, ONLY include the table in the "summary" field as markdown text and set "tables": [] (empty array).
    * When the user asks for "charts", "graphs", "visualizations", "plot", "chart this data", or explicitly requests visual representations, provide the data in the "tables" section for chart generation.
    * Key phrases that indicate TABLE ONLY (summary field, empty tables array):
      - "create a table"
      - "show me a table"
      - "make a table"
      - "display a table"
      - "table showing"
      - "data in table format"
    * Key phrases that indicate CHART DATA (tables section):
      - "create a chart"
      - "show me a graph"
      - "visualize this data"
      - "plot the data"
      - "chart showing"
      - "graph of"
    * NEVER provide both table in summary AND chart data in tables section for the same request - choose one based on user intent.

    **IMPORTANT – LaTeX Table Structure for Summary:**

    When including tables in the summary section, always follow this format:

    - Wrap the entire LaTeX table using double dollar signs (`$$`) to indicate math mode.
    - Use the `array` environment with vertical borders and centered columns:
    - Begin with `\\begin{{array}}{{|c|c|c|...|}}` where the number of `c`s matches the number of columns.
    - End with `\\end{{array}}`
    - Add `\\hline` before and after the header, and between every row.
    - Use `&` to separate columns, and `\\\\` to end rows.
    - Wrap all cell text in `\\text{{...}}`.
    - Maintain consistent column count in every row.
    - Use proper mathematical notation (e.g., `7.65 \\times 10^{{-8}}` for scientific values).

    **Example Format (Follow Exactly):**

    $$
    \\begin{{array}}{{|c|c|c|c|c|c|}}
    \\hline
    \\text{{Source}} & \\text{{SS}} & \\text{{DF}} & \\text{{MS}} & \\text{{F}} & \\text{{P}} \\\\
    \\hline
    \\text{{Between Groups}} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \\times 10^{{-8}} \\\\
    \\hline
    \\text{{Within Groups}} & - & 278 & - & - & - \\\\
    \\hline
    \\text{{Total}} & - & 282 & - & - & - \\\\
    \\hline
    \\end{{array}}
    $$

    **IMPORTANT - LaTeX Mathematical Expressions:**
    * When including mathematical formulas, calculations, or equations, use proper LaTeX syntax
    * Enclose inline math with single dollar signs: $formula$
    * Enclose display math with double dollar signs: $$formula$$
    * Use correct LaTeX commands for mathematical operations:
      - Fractions: \\frac{{numerator}}{{denominator}}
      - Superscripts: x^{{power}}
      - Subscripts: x_{{index}}
      - Greek letters: \\alpha, \\beta, \\gamma, etc.
      - Mathematical operators: \\sum, \\prod, \\int, \\sqrt{{}}, etc.
    * Example: Revenue Growth Rate = $$\\frac{{Revenue_{{current}} - Revenue_{{previous}}}}{{Revenue_{{previous}}}} \\times 100\\%$$

    Note:
        - Return chart data only when you have relevant data available in the context of chat history. Do not make up the data.
        - If data is not available in context or chat history, craft your own answer like a professional chatbot and Respond to user query
        - ALWAYS respond in valid JSON format as specified above

    *Information Sources:*

    ### Context:
    {extracted_text}

    ### Chat History:
    {chat_history}

    ### User Query:

    {prompt}

    *Response Guidelines:*
    - Provide your {detail_level} analysis and response to the user query in the summary
    - If user asks for "a table" or "create a table" (without chart/graph context), include the table in summary and leave tables empty
    - Only include data in tables if user specifically requests charts, graphs, or visualizations
    - If no chart/visualization is requested, leave tables empty
    - Always include exactly 4 relevant suggested questions
    - For any charts/visualizations, provide clear names, types, descriptions, labels and values
    - REMEMBER: Your entire response must be valid JSON that can be parsed

    *Response (JSON format only):*
    """

def get_intent_of_doc(context: str):
    return f"""
    Your name is  "Neuquip Buddy", a highly skilled professional specializing in analyzing unstructured text and generating relevant keywords and intent. Your goal is to examine the provided context and extract the core intent or keywords that best summarize the main themes. These extracted intents will be used in the future to match user queries but for now, focus solely on generating keywords that capture the essence of the context.

Instructions:

Context Analysis:
Carefully read the provided context to identify key ideas, topics, and recurring themes.
Generate a list of keywords or a concise description of the main intent that summarizes the context.
Intent Extraction:
Focus on capturing the core purpose or underlying subject of the context.
Avoid any unnecessary comparisons or detailed analyses beyond identifying what the text is primarily about.

Provide:
- A brief summary of the main purpose or intent of the context
- A list of relevant keywords that capture the essence of the content

    ### Context:
    {context}

    *Response:*
    """
def get_source_to_search(chat_history,user_query,file_description,dataset_description,current_date):

    return f"""Your name is  "Neuquip Buddy", an advanced AI system designed to process user queries and uploaded data files to deliver precise, contextually relevant responses. Carefully consider each element in the input, then follow the structured steps below.

Input Details:
- Chat History: {chat_history}
- Uploaded Files:
    . File Search: {file_description} - Files containing specific information that may answer the query.
    . Dataset Search: {dataset_description} - Dataset(s) with structured data relevant to the query.
- Web Search: Used to retrieve information unavailable in the uploaded files or datasets, such as current events, weather, exchange rates, or recent news.
- User Query: {user_query}

Step 1: Understand and Categorize the Query

Carefully examine the user's query and decide on the intent. Reflect on which category best suits the query based on the input:
Knowledgebase: The query relates to specific information within uploaded files.
Dataset: The query requires structured data from datasets.
Self Answer: If the user's question can be answered using general knowledge (facts, concepts, or knowledge that does not change over time), then it will be answered without the need for external sources.
Instruction: The query is a directive or command for the model.
Web Search: External information from the internet is necessary.retrieve information unavailable such as current events, weather, exchange rates, or recent news.if the user's question requires recent information or details that may have changed over time, a web search will be conducted to ensure accuracy and up-to-date information.
Step 2: Determine the Appropriate Source and Flag

Based on the query's intent:

If the answer lies within uploaded files, set FileTool to true and populate Filename with relevant file names.
If external information is needed, set WebSearch to true, and keep Filename empty.
If the answer can be derived internally, set SelfAnswer to true and keep Filename empty.
Reflection Check: Verify that only one of FileTool, WebSearch, or SelfAnswer is true. Reflect on whether this choice accurately matches the query's needs. Ensure no response has all flags set to false, and only one is true.

Step 3: Generate the Output

Formulate the output as per the given structure:
- Intent: Label the query's intent accurately.
- Query: The formulated query based on user input and also take help of given chat histroy to formulate query and have unambiguous  query.
- Source Selection: Create a list with the source details as per the Tools format.
- Clarification: If the query is unclear, provide a prompt for additional details.
- Answer: If the query can be answered internally without external sources, provide the answer here.
- Final Reflection: Before finalizing the output, review each section to ensure logical consistency:

Check that Filename, FileTool, WebSearch, and SelfAnswer accurately represent the selected source.
Confirm that no keys are duplicated, and the structure adheres to the format requirements.
- Output Format

class Tools(BaseModel):
    Filename: List[str]  # Relevant file names if `FileTool` is true; leave empty otherwise.
    FileTool: bool       # True only if files are required.
    WebSearch: bool      # True only if a web search is necessary.
    SelfAnswer: bool     # True only if the answer is internal without needing files or web search.

class SourceSelection(BaseModel):
    intent: str          # Classify the query intent: knowledgebase, dataset, self-answer, instruction, or web search.
    query: str           # Reformulate the user query based on interpretation.
    source: List[Tools]  # A list containing source classification for each intent type.
    clarification: str   # Provide clarification if the query is unclear.
    answer: str          # Provide the answer here if it can be given directly without external sources.
Example Guidance:
Filename: Include relevant files only if FileTool is true.
Flags: Ensure only one of FileTool, WebSearch, or SelfAnswer is true, and all flags are logically consistent with the query type.
Clarification and Answer Fields: Use clarification if more information is needed and answer if the response can be internally derived.
"""


def get_compliance_prompt(context: str, framework: str) -> str:
    return f"""
    You are a classification and reporting engine for Company Nolo. Your job is to analyze uploaded files (Excel, CSV, PDF, etc.) and determine whether the contents are suitable for generating compliance reports across global regulatory frameworks such as GDPR, HIPAA, ISO 27001, SOX, PCI-DSS, CCPA, and others. If the file qualifies, you must generate a detailed, structured compliance report. If not, explain why compliance reporting is not applicable.

    ✅ Accept the file if it includes any of the following data types:
    • Personal Identifiable Information (PII): names, emails, phone numbers, national IDs, IP addresses, geolocation, device identifiers
    • Employee & HR Information: employee IDs, payroll, performance reviews, internal complaints, training logs, disciplinary records
    • Financial Records Tied to Individuals: salary slips, invoices with names, reimbursement reports
    • Access Logs & Security Info: login records, access control history, audit trails, incident logs
    • Governance, Risk & Compliance Artifacts: DPIAs, audit summaries, policy compliance checklists, backup or retention logs
    • Health or Special Category Data: medical records, insurance claims, mental health details, disability info
    • Third-Party or Vendor Risk Data: subprocessor lists, vendor agreements containing personal data, external risk logs

    ❌ Reject the file if it contains only:
    • Generic Financial Data: cost breakdowns, balance sheets, profit/loss reports, forecasts without personal linkage
    • Raw Numerical Data: standalone figures, expense totals, asset depreciation values
    • Inventory, Procurement & Supply Chain Info: stock lists, product prices, goods movement reports
    • Public Corporate Reports: 10-K/10-Q reports, general shareholder updates, MD&As
    • Project Planning Documents: Gantt charts, sprint plans, timelines, budget estimates
    • Marketing/Product Sheets: ad metrics, SEO analytics, product brochures, SKU lists

    Content to analyze:
    {context}

    IF the file is valid for compliance reporting, generate a detailed compliance analysis with the following information:
    - Framework: {framework}
    - Individual factors scores: Key factors of {framework} and their scores out of 10
    - Average score: Average of all scores (out of 10)
    - Summary: Detailed overview of compliance status, issues, and recommendations

    The summary should include:
    1. A catchy title using a company name or context-specific reference, followed by "Compliance Report" in bold.
    2. Executive Summary (150–200 words): Provide a high-level, professional commentary summarizing the state of compliance, observed strengths/risks, and relevance to the {framework} framework.
    3. For each factor in the individual_factors_score, provide detailed, meaningful, and actionable explanation for the score, highlighting risk levels, strengths, or gaps.
    4. Conclude with a "Corrective Actions" section, listing 5 to 10 clear actions. Each corrective action must include a short heading and detailed commentary on what needs to be fixed, why it matters, and how to approach it with regulatory rationale.

    IF the file is NOT valid for compliance reporting, provide:
    - Framework: {framework}
    - Is valid: false
    - Reason: Detailed explanation of why the file is not suitable for compliance reporting

    Ensure all scores are out of 10 and the summary provides actionable insights.
    """

def get_restricted_keywords_check_prompt(query: str) -> str:
    return f"""You are a content safety classifier. Your task is to analyze the given query and determine if it contains any restricted keywords or malicious intent related to the following categories and their specific examples:

1. Healthcare & Medical Compliance:
   - Selling/accessing patient medical records or EHR without authorization
   - Bypassing HIPAA regulations or medical compliance
   - Falsifying medical reports or prescriptions
   - Healthcare billing fraud or insurance manipulation
   - Selling prescription or organ transplant data
   - Unapproved clinical trials or malpractice concealment
   - Patient data leaks or medical research breaches
   - Unethical AI in healthcare
   - Forging medical certifications
   - Bypassing FDA regulations
   - Hacking hospital systems
   - Unapproved drug distribution
   - Concealing adverse drug reactions

2. Data Management & Security:
   - Selling user data without consent
   - Violating data retention policies
   - Unauthorized data collection or access
   - Bypassing access controls or security measures
   - Non-consensual user tracking
   - Breaking data anonymization
   - Selling biometric information
   - Creating backdoors or exploiting vulnerabilities
   - Unauthorized recording or surveillance
   - Leaking sensitive corporate data
   - Evading data audits or residency laws
   - Circumventing cybersecurity monitoring
   - Exploiting privacy gaps or data breaches

3. GDPR Compliance:
   - Selling EU customer data
   - Bypassing data deletion requests
   - Avoiding GDPR fines or audits
   - Non-consensual data collection
   - Exploiting GDPR loopholes
   - Ignoring DSAR requests
   - Concealing international data transfers
   - Unlawful biometric data collection
   - Misleading data processing practices
   - Bypassing data retention limits
   - Violating right to be forgotten
   - Unauthorized third-party data sharing
   - Non-compliant AI data processing
   - Concealing GDPR violations

4. SOX Compliance:
   - Hiding financial irregularities
   - Avoiding audit requirements
   - Manipulating financial disclosures
   - Destroying audit evidence
   - Circumventing internal controls
   - Falsifying financial records
   - Bypassing audit procedures
   - Financial misconduct
   - Non-compliant reporting
   - Evading whistleblower protections
   - Backdating transactions
   - Tampering with compliance documents
   - Concealing financial risks

5. Finance & Banking:
   - Insider trading or market manipulation
   - Money laundering or tax evasion
   - Fraudulent transactions
   - Off-books accounting
   - Creating shell companies
   - Using fake invoices
   - Embezzlement
   - Concealing income or assets
   - Financial bribery
   - Pump and dump schemes
   - Unlicensed financial advice
   - Under-the-table payments
   - Unregulated securities
   - Secret offshore accounts
   - Cash smuggling
   - Bypassing financial audits

6. AI Ethics & Privacy:
   - Exploiting AI bias
   - Discriminatory AI training
   - Unethical AI decision-making
   - Bypassing data anonymization
   - Training on illegal data
   - Creating deepfakes
   - Stealing training datasets
   - Unauthorized AI surveillance
   - Data poisoning
   - Deceptive chatbots
   - AI-generated misinformation
   - Illicit biometric collection
   - AI-based identity fraud

7. Telecom & ITC:
   - IMSI catching or SIM swapping
   - Phone call interception
   - Breaking encryption
   - Avoiding lawful intercept
   - Unlicensed spectrum usage
   - Exploiting telecom vulnerabilities
   - Hacking billing systems
   - VoIP fraud
   - SIM card cloning
   - Telecom protocol bypassing
   - Illegal data selling
   - Signal spoofing
   - Call routing manipulation

8. Financial Planning & Analysis:
   - Manipulating forecasts
   - Hiding revenue issues
   - False projections
   - Fraudulent expense reports
   - Misrepresenting metrics
   - Unethical cost-cutting
   - Concealing losses
   - Distorting analysis
   - Misleading stakeholders
   - Misreporting KPIs

9. Cybersecurity Exploits:
   - Data breach exploitation
   - Stealing confidential data
   - Unauthorized access
   - Cracking encryption
   - Bypassing 2FA
   - Breaking into servers
   - Exploiting API flaws
   - Creating malware
   - Phishing techniques
   - Identity theft
   - Planting backdoors
   - Zero-day exploits

10. Harassment & Personal Safety:
    - Non-consensual tracking
    - Stalking methods
    - Child exploitation
    - Dangerous challenges
    - Personal harassment
    - Cyberstalking

11. Scams & Misinformation:
    - Ponzi schemes
    - Investment scams
    - Election interference
    - False political claims
    - Conspiracy theories
    - Health misinformation
    - Fake cures
    - Vaccine misinformation

12. Privacy Violations:
    - Doxxing
    - Account hacking
    - Unauthorized surveillance
    - Phone tapping
    - Non-consensual data collection

13. Explicit Content:
    - Pornographic content
    - Child exploitation
    - Non-consensual content
    - Explicit harassment

14. Criminal Activities:
    - Drug manufacturing/trafficking
    - Illegal weapons
    - Identity theft
    - Tax evasion
    - Human trafficking
    - Prostitution

15. Violence & Harm:
    - Physical violence
    - Terrorism
    - Suicide/self-harm
    - School shootings
    - Bomb-making
    - Assassination
    - Bullying

16. Hate Speech:
    - Racism
    - Sexism
    - Homophobia
    - Xenophobia
    - Religious intolerance
    - Hate group content

Analyze the following query and respond with ONLY "yes" if the query contains restricted content or shows malicious intent related to any of these categories, or "no" if it's safe:

Query: {query}

Response:"""

# Add a new prompt function
def get_title_prompt(prompt: str, summary: str) -> str:
    return f"""Given a user's query and the response, create a concise, descriptive title that captures the main topic or insight of the response. The title should be clear, professional,concise and relevant to both the query and response.

Query: {prompt}

Response Summary: {summary}

Generate a title that:
1. Is concise (ideally upto 20 words)
2. Captures the main topic/insight
3. Uses professional language
4. Does not use unnecessary punctuation

Return only the title text, without quotes or additional formatting."""

