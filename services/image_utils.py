import logging
from services.logging_utils import LoggingHelper
import os
from PIL import Image, ImageStat
import base64
import aiofiles
from io import BytesIO

logger = logging.getLogger(__name__)

# Vision model image processing settings
MAX_IMAGE_SIZE = (512, 512)  # Smaller max dimensions for better token efficiency
JPEG_QUALITY = 70  # Lower quality for smaller file sizes
MAX_IMAGE_TOKENS = 20000  # Lower token limit for safety
AGGRESSIVE_COMPRESSION_SIZE = (256, 256)  # Very small size for aggressive compression
AGGRESSIVE_JPEG_QUALITY = 50  # Very low quality for extreme compression

# Functions to be moved here:

def is_blank_image(image):
    """Check if an image is blank by analyzing pixel intensity variation."""
    stat = ImageStat.Stat(image)
    return sum(stat.stddev) < 10


def estimate_image_tokens(image_size_bytes):
    """
    Estimate token count for an image based on its size.
    This is a more conservative approximation for vision models.
    """
    # More conservative estimation based on actual base64 size impact
    # Base64 encoding increases size by ~33%, and each character ≈ 0.25 tokens
    base64_size = image_size_bytes * 4 // 3  # Base64 overhead
    estimated_tokens = base64_size // 4  # Rough token estimation

    # Add minimum baseline for image processing
    baseline_tokens = 1000
    total_tokens = baseline_tokens + estimated_tokens

    # Cap at reasonable limit
    return min(total_tokens, MAX_IMAGE_TOKENS)


async def resize_and_compress_image(image_path, aggressive=False):
    """
    Resize and compress an image for vision model processing.
    Returns the compressed image data as bytes.

    Args:
        image_path: Path to the image file
        aggressive: If True, use more aggressive compression
    """
    try:
        # Read image data
        async with aiofiles.open(image_path, "rb") as image_file:
            original_data = await image_file.read()

        # Choose compression settings
        target_size = AGGRESSIVE_COMPRESSION_SIZE if aggressive else MAX_IMAGE_SIZE
        quality = AGGRESSIVE_JPEG_QUALITY if aggressive else JPEG_QUALITY

        # Process image in memory
        def _process_image():
            with Image.open(BytesIO(original_data)) as img:
                # Convert to RGB if necessary (for JPEG compatibility)
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Calculate new size maintaining aspect ratio
                original_size = img.size
                img.thumbnail(target_size, Image.Resampling.LANCZOS)

                # Compress to JPEG
                output = BytesIO()
                img.save(output, format='JPEG', quality=quality, optimize=True)
                compressed_data = output.getvalue()

                compression_level = "aggressive" if aggressive else "standard"
                LoggingHelper.info(
                    f"Image processed ({compression_level}): {original_size} -> {img.size}, "
                    f"{len(original_data):,} bytes -> {len(compressed_data):,} bytes "
                    f"({(len(compressed_data)/len(original_data)*100):.1f}% of original)",
                    logger_name="image_utils"
                )

                return compressed_data

        # Run image processing in thread to avoid blocking
        import asyncio
        return await asyncio.to_thread(_process_image)

    except Exception as e:
        LoggingHelper.error(f"Error processing image {image_path}: {str(e)}", logger_name="image_utils")
        # Fallback: return original data
        async with aiofiles.open(image_path, "rb") as image_file:
            return await image_file.read()


async def encode_image(image_path, max_tokens=None):
    """
    Encode an image file to base64 string with intelligent compression.
    Automatically resizes and compresses large images for vision model compatibility.
    Uses progressive compression if needed.

    Args:
        image_path: Path to the image file
        max_tokens: Maximum allowed tokens (defaults to MAX_IMAGE_TOKENS)
    """
    if max_tokens is None:
        max_tokens = MAX_IMAGE_TOKENS

    try:
        # First, try standard compression
        processed_image_data = await resize_and_compress_image(image_path, aggressive=False)
        estimated_tokens = estimate_image_tokens(len(processed_image_data))

        # If still too large, try aggressive compression
        if estimated_tokens > max_tokens:
            LoggingHelper.warning(
                f"Image too large ({estimated_tokens:,} tokens), trying aggressive compression",
                logger_name="image_utils"
            )
            processed_image_data = await resize_and_compress_image(image_path, aggressive=True)
            estimated_tokens = estimate_image_tokens(len(processed_image_data))

        # Log final token estimation
        LoggingHelper.info(
            f"Image encoded: {len(processed_image_data):,} bytes, "
            f"estimated ~{estimated_tokens:,} tokens",
            logger_name="image_utils"
        )

        # Final warning if still very large
        if estimated_tokens > max_tokens:
            LoggingHelper.warning(
                f"Image still large after compression: {estimated_tokens:,} tokens "
                f"(max: {max_tokens:,}). May cause API failures.",
                logger_name="image_utils"
            )

        # Encode to base64
        return base64.b64encode(processed_image_data).decode('utf-8')

    except Exception as e:
        LoggingHelper.error(f"Error encoding image {image_path}: {str(e)}", logger_name="image_utils")
        # Fallback to original method
        async with aiofiles.open(image_path, "rb") as image_file:
            data = await image_file.read()
            return base64.b64encode(data).decode('utf-8')
