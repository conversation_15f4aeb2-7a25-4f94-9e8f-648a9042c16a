"""
Project+User wise logging service for NeuquipAI Backend.

This service provides isolated logging for each user+project combination,
enabling better debugging, monitoring, and issue tracking at a granular level.
Designed for high concurrency and performance with async support.
"""

import os
import logging
import asyncio
import aiofiles
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Tuple, Any
from logging.handlers import RotatingFileHandler
from pathlib import Path
import threading
from collections import defaultdict
import weakref
import time
import json

import config


class ProjectUserLogger:
    """
    Thread-safe, async-compatible logger for project+user specific logging.
    
    Features:
    - Isolated log files per user+project combination
    - Automatic log rotation and cleanup
    - High-performance async file I/O
    - Memory-efficient logger caching
    - Structured logging support
    - Concurrent access handling
    """
    
    _instance = None
    _lock = threading.Lock()
    _loggers_cache: Dict[str, logging.Logger] = {}
    _file_handlers_cache: Dict[str, RotatingFileHandler] = {}
    _last_cleanup = time.time()
    _access_times: Dict[str, float] = defaultdict(float)
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
            self.base_dir = Path(config.PROJECT_USER_LOG_BASE_DIR)
            self.max_concurrent_files = config.PROJECT_USER_LOG_MAX_CONCURRENT_FILES
            self.cleanup_interval = config.PROJECT_USER_LOG_CLEANUP_INTERVAL_HOURS * 3600
            self.retention_days = config.PROJECT_USER_LOG_RETENTION_DAYS
            
            # Ensure base directory exists
            self.base_dir.mkdir(parents=True, exist_ok=True)

            # Start background cleanup task (only if event loop is running)
            if config.PROJECT_USER_LOGGING_ENABLED:
                try:
                    asyncio.create_task(self._background_cleanup())
                except RuntimeError:
                    # No event loop running yet, cleanup will be started later
                    pass
    
    def _get_log_path(self, user_id: str, project_id: str) -> Path:
        """Generate log file path for user+project combination."""
        # Sanitize user_id and project_id for filesystem safety
        safe_user_id = self._sanitize_filename(user_id)
        safe_project_id = self._sanitize_filename(project_id)
        
        user_dir = self.base_dir / safe_user_id / "projects" / safe_project_id
        user_dir.mkdir(parents=True, exist_ok=True)
        
        return user_dir / "app.log"
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        # Replace invalid characters with underscores
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length and remove leading/trailing dots and spaces
        filename = filename.strip('. ')[:100]
        
        # Ensure it's not empty
        return filename if filename else 'unknown'
    
    def _get_logger_key(self, user_id: str, project_id: str) -> str:
        """Generate unique key for logger caching."""
        return f"{user_id}:{project_id}"
    
    def get_logger(self, user_id: str, project_id: str, logger_name: str = None) -> logging.Logger:
        """
        Get or create a logger for specific user+project combination.
        
        Args:
            user_id: User identifier
            project_id: Project identifier  
            logger_name: Optional logger name (defaults to module name)
            
        Returns:
            Configured logger instance
        """
        if not config.PROJECT_USER_LOGGING_ENABLED:
            return logging.getLogger(logger_name or __name__)
        
        logger_key = self._get_logger_key(user_id, project_id)
        
        # Update access time for cleanup
        self._access_times[logger_key] = time.time()
        
        # Check if logger already exists
        if logger_key in self._loggers_cache:
            return self._loggers_cache[logger_key]
        
        # Create new logger
        with self._lock:
            # Double-check after acquiring lock
            if logger_key in self._loggers_cache:
                return self._loggers_cache[logger_key]
            
            # Check if we need to cleanup old loggers
            if len(self._loggers_cache) >= self.max_concurrent_files:
                self._cleanup_old_loggers()
            
            # Create logger
            logger_full_name = f"project_user.{user_id}.{project_id}"
            if logger_name:
                logger_full_name += f".{logger_name}"
            
            logger = logging.getLogger(logger_full_name)
            logger.setLevel(config.log_level)
            
            # Prevent duplicate handlers
            if not logger.handlers:
                # Create file handler
                log_path = self._get_log_path(user_id, project_id)
                file_handler = RotatingFileHandler(
                    log_path,
                    maxBytes=config.PROJECT_USER_LOG_MAX_SIZE,
                    backupCount=config.PROJECT_USER_LOG_BACKUP_COUNT
                )

                # Set formatter for file handler
                formatter = logging.Formatter(config.PROJECT_USER_LOG_FORMAT)
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)

                # Add console handler if enabled (or forced)
                force_dual_output = getattr(config, 'PROJECT_USER_LOG_FORCE_DUAL_OUTPUT', False)
                console_output_enabled = getattr(config, 'PROJECT_USER_LOG_CONSOLE_OUTPUT', True)

                if console_output_enabled or force_dual_output:
                    console_handler = logging.StreamHandler()
                    # Use a clean, readable format for console
                    console_format = f'%(asctime)s - %(levelname)s - [U:{user_id[:8]}|P:{project_id[:8]}] - %(message)s'
                    console_formatter = logging.Formatter(console_format)
                    console_handler.setFormatter(console_formatter)
                    logger.addHandler(console_handler)

                logger.propagate = False  # Prevent duplicate logging to root logger

                # Cache the file handler for cleanup
                self._file_handlers_cache[logger_key] = file_handler
            
            # Cache the logger
            self._loggers_cache[logger_key] = logger
            
            return logger
    
    def _cleanup_old_loggers(self):
        """Remove least recently used loggers to free memory."""
        if len(self._loggers_cache) < self.max_concurrent_files:
            return
        
        # Sort by access time and remove oldest 25%
        sorted_keys = sorted(
            self._access_times.items(),
            key=lambda x: x[1]
        )
        
        keys_to_remove = [key for key, _ in sorted_keys[:len(sorted_keys) // 4]]
        
        for key in keys_to_remove:
            self._remove_logger(key)
    
    def _remove_logger(self, logger_key: str):
        """Remove logger and its handler from cache."""
        if logger_key in self._loggers_cache:
            # Close file handler
            if logger_key in self._file_handlers_cache:
                handler = self._file_handlers_cache[logger_key]
                handler.close()
                del self._file_handlers_cache[logger_key]
            
            # Remove from cache
            del self._loggers_cache[logger_key]
            
            # Remove access time
            if logger_key in self._access_times:
                del self._access_times[logger_key]
    
    async def _background_cleanup(self):
        """Background task for periodic cleanup of old log files."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_old_files()
            except Exception as e:
                # Log to main logger if cleanup fails
                logging.getLogger(__name__).error(f"Background cleanup failed: {e}")
    
    async def _cleanup_old_files(self):
        """Remove log files older than retention period."""
        if not self.base_dir.exists():
            return
        
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=self.retention_days)
        cutoff_timestamp = cutoff_time.timestamp()
        
        # Walk through all log files
        for log_file in self.base_dir.rglob("*.log*"):
            try:
                if log_file.stat().st_mtime < cutoff_timestamp:
                    log_file.unlink()
            except (OSError, FileNotFoundError):
                # File might have been deleted by another process
                pass
        
        # Remove empty directories
        for user_dir in self.base_dir.iterdir():
            if user_dir.is_dir():
                try:
                    # Remove empty project directories
                    projects_dir = user_dir / "projects"
                    if projects_dir.exists():
                        for project_dir in projects_dir.iterdir():
                            if project_dir.is_dir() and not any(project_dir.iterdir()):
                                project_dir.rmdir()
                        
                        # Remove empty projects directory
                        if not any(projects_dir.iterdir()):
                            projects_dir.rmdir()
                    
                    # Remove empty user directory
                    if not any(user_dir.iterdir()):
                        user_dir.rmdir()
                except OSError:
                    # Directory might not be empty or might be in use
                    pass
    
    def log_structured(self, user_id: str, project_id: str, level: str,
                      message: str, extra_data: Dict[str, Any] = None,
                      logger_name: str = None):
        """
        Log structured data with improved readability.

        Args:
            user_id: User identifier
            project_id: Project identifier
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            message: Log message
            extra_data: Additional structured data to log
            logger_name: Optional logger name
        """
        logger = self.get_logger(user_id, project_id, logger_name)

        # Check if simplified JSON structure is enabled
        use_simplified_format = getattr(config, 'SIMPLIFIED_JSON_STRUCTURE', True)
        add_spacing = getattr(config, 'READABLE_LOG_SPACING', True)

        if use_simplified_format:
            # Create clean, readable log message
            main_message = message

            # Add extra data as a clean, readable format if present
            if extra_data:
                # Format extra data in a clean, readable way
                extra_parts = []
                for key, value in extra_data.items():
                    # Convert value to string and truncate if too long
                    str_value = str(value)
                    if len(str_value) > 100:
                        str_value = str_value[:97] + "..."
                    extra_parts.append(f"{key}: {str_value}")

                if extra_parts:
                    main_message += f"\n  -> {', '.join(extra_parts)}"

            # Log at appropriate level
            log_level = getattr(logging, level.upper(), logging.INFO)
            logger.log(log_level, main_message)

            # Add a blank line after each log entry for better readability
            # Only add spacing for INFO level and above to avoid cluttering debug logs
            if add_spacing and log_level >= logging.INFO:
                logger.log(log_level, "")
        else:
            # Fall back to original structured JSON format
            log_entry = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "user_id": user_id,
                "project_id": project_id,
                "level": level,
                "message": message
            }

            if extra_data:
                log_entry["extra"] = extra_data

            # Log as JSON for structured parsing
            import json
            structured_message = f"STRUCTURED_LOG: {json.dumps(log_entry, default=str)}"

            # Log at appropriate level
            log_level = getattr(logging, level.upper(), logging.INFO)
            logger.log(log_level, structured_message)
    
    def get_log_stats(self) -> Dict[str, Any]:
        """Get statistics about current logging state."""
        return {
            "active_loggers": len(self._loggers_cache),
            "max_concurrent_files": self.max_concurrent_files,
            "base_directory": str(self.base_dir),
            "retention_days": self.retention_days,
            "cleanup_interval_hours": self.cleanup_interval / 3600,
            "last_cleanup": self._last_cleanup
        }


# Global instance
project_user_logger = ProjectUserLogger()


def get_project_user_logger(user_id: str, project_id: str, logger_name: str = None) -> logging.Logger:
    """
    Convenience function to get a project+user specific logger.
    
    Args:
        user_id: User identifier
        project_id: Project identifier
        logger_name: Optional logger name (defaults to calling module)
        
    Returns:
        Configured logger instance
    """
    return project_user_logger.get_logger(user_id, project_id, logger_name)


def log_structured(user_id: str, project_id: str, level: str, message: str, 
                  extra_data: Dict[str, Any] = None, logger_name: str = None):
    """
    Convenience function for structured logging.
    
    Args:
        user_id: User identifier
        project_id: Project identifier
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        message: Log message
        extra_data: Additional structured data to log
        logger_name: Optional logger name
    """
    project_user_logger.log_structured(user_id, project_id, level, message, extra_data, logger_name)
