from PyPDF2 import PdfReader
from io import By<PERSON><PERSON>
from typing import Optional, List
import time
import config
from services import prompts, utils
from models import insight_model
import json
import json as jp
from fastapi import UploadFile
import logging
import uuid
from datetime import datetime, timezone
import os
from typing import Union
from bson.objectid import ObjectId
from exceptions.api_exceptions import handle_general_error
from starlette.concurrency import run_in_threadpool
from services.logging_utils import LoggingHelper

# Configure logging - will use project+user specific logging
logger = logging.getLogger(__name__)
class PDFProcessor:
    start_time = time.time()

    def __init__(self, openai_client, mongo_manager):
        self.openai_client = openai_client
        self.mongo_manager = mongo_manager
        self.page_limit = config.page_limit  # Configurable page limit

    async def process_pdf(self, file: Optional[UploadFile], prompt: str, project_id: str, user_id: str, page_number: Optional[str],
                          file_name: Optional[str], file_type: Optional[str], title: Optional[str],extracted_text:str, page_numbers, extracted_text_per_page:str, valid_page_numbers,intent,previous_responses, previous_prompt,llm_call,insight_data, custom_prompt_name: Optional[str], response_type: str = "detailed", visualization: bool = False,complete_chat=Union[dict, str], token_logger=None, unified_logger=None, source_type: str = "file"):
        # Retrieve or initialize project data from MongoDB
        project_data,exists,_= await self.mongo_manager.get_or_initialize_project(project_id)


        saved_in_mongo = False

        if file and not exists :
            if exists==True:
                # print("7"*50)
                LoggingHelper.info("File exists but not project", user_id=user_id, project_id=project_id)
            file_exist=True

            # File provided, extract text from the file
            print("in file and not exits pline o 35")
            # extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = await self.extract_text_from_file(file, page_number)
            # print("--------------------VALID PAGES, file exists but not project---------------------")
            LoggingHelper.info(f"valid_page_numbers incase of file exists but not project: {valid_page_numbers}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"valid_page_numbers": valid_page_numbers, "file_name": file_name})
            # print("--------------------VALID PAGES, file exists but not project---------------------")

            previous_prompt = project_data.get("chat_prompt", "")
            previous_responses = project_data.get("chat_response", "")
            previous_complete_prompt = project_data.get("prompt", "")
            previous_complete_responses = project_data.get("response", "")
            chat_history = [{p: r} for p, r in zip(previous_prompt, previous_responses)]
            saved_in_mongo = True

        elif file and exists:
            if exists==True:
                # print("8"*50)
                LoggingHelper.info("File and project exists", user_id=user_id, project_id=project_id)
            file_exist=True

            # Use the non_data_analysis_dir that was already created in the route
            pkl_file_path = os.path.join(
                config.file_path,
                project_id,
                config.non_data_analysis_folder_name,
                file_name.split(".")[0] + ".pkl"
            )
            LoggingHelper.info(f"Saving pickle file to: {pkl_file_path}", user_id=user_id, project_id=project_id,
                              extra_data={"pkl_file_path": pkl_file_path, "file_name": file_name})

            await utils.put_pickle_file_context(pkl_file_path, {
                'texts': extracted_text_per_page,
                'total_pages': len(extracted_text_per_page)
            })

            # LoggingHelper.info("context------------------------------------------------ %s", extracted_text_per_page)
            # print("--------------------VALID PAGES, file and project  exists---------------------")

            LoggingHelper.info(f"valid_page_numbers incase of file and project exists: {valid_page_numbers}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"valid_page_numbers": valid_page_numbers, "file_name": file_name})
            previous_prompt = [] #project_data.get("chat_prompt", "")
            previous_responses =[]# project_data.get("chat_response", "")
            await self.mongo_manager.update_prompt_and_response_to_empty(project_id)
            previous_complete_prompt = project_data.get("prompt", "")
            previous_complete_responses = project_data.get("response", "")
            chat_history = [{p: r} for p, r in zip(previous_prompt, previous_responses)]
            saved_in_mongo = True

            # Use the intent passed from the route instead of regenerating it
            # This prevents duplicate LLM calls and ensures consistency
            LoggingHelper.info("Using intent passed from file processing route (avoiding duplicate generation)",
                              user_id=user_id, project_id=project_id)
            LoggingHelper.info("intent----------------------------context --------------------- %s",
                              user_id=user_id, project_id=project_id,
                              extra_data={"intent": str(intent)[:200]})
        else:
            LoggingHelper.info("No file provided, use existing data from MongoDB for pdf processing",
                              user_id=user_id, project_id=project_id)
            # No file provided, use existing data from MongoDB
            saved_in_mongo = True
            file_exist=False
            chat_history = [{p: r} for p, r in zip(previous_prompt, previous_responses)]
            valid_page_numbers = page_numbers

            LoggingHelper.info(f"file_context: {len(extracted_text)}", user_id=user_id, project_id=project_id,
                              extra_data={"extracted_text_length": len(extracted_text)})
            LoggingHelper.info(f"valid_page_numbers incase of no file provided in request: {valid_page_numbers}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"valid_page_numbers": valid_page_numbers})
            chat_history = [{p: r} for p, r in zip(previous_prompt, previous_responses)]

        original_prompt = prompt
        # modified_prompt=""
        if file and exists:
            # print("-----------------------------ADDING DEFAULT PROMPTp----------------------------")
            LoggingHelper.info("Adding default prompt to the prompt in pdf processor (file and project exists)",
                              user_id=user_id, project_id=project_id)
            add=utils.get_default_prompt()
            prompt=add+prompt

        if not exists and not file:
            # print("-----------------------------ADDING DEFAULT PROMPT for direct message----------------------------")
            LoggingHelper.info("Adding default prompt to the prompt in pdf processor (no file provided in request and no project exists)",
                              user_id=user_id, project_id=project_id)

            add=utils.get_default_prompt_for_direct_chat()
            prompt=add+prompt

        if not llm_call:
            LoggingHelper.info("Entered not llm call block in pdf processor", user_id=user_id, project_id=project_id)
            
            # DEDUPLICATION FIX: Check if insight_data is already available
            if insight_data and isinstance(insight_data, (dict, str)) and insight_data != "":
                LoggingHelper.info("Using existing insight_data - skipping LLM call to prevent duplication",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={
                                      "insight_data_available": True, 
                                      "source_type": source_type,
                                      "insight_data_type": type(insight_data).__name__,
                                      "has_summary": "summary" in insight_data if isinstance(insight_data, dict) else False
                                  })
                # insight_data is already available from previous processing (e.g., web search)
                # No need to call generate_insight() again - this prevents duplication
                pass
            elif not file:
                LoggingHelper.info("No existing insight_data available - calling generate_insight",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"insight_data_available": False})
                async for event in self.generate_insight(
                    saved_in_mongo=saved_in_mongo,
                    project_id=project_id,
                    file=file,
                    prompt=prompt,
                    extracted_text=extracted_text,
                    chat_history=chat_history,
                    page_numbers=page_numbers,
                    valid_page_numbers=valid_page_numbers,
                    response_type=response_type,
                    visualization=visualization,
                    token_logger=token_logger,
                    user_id=user_id,
                    unified_logger=unified_logger,
                    source_type=source_type
                ):
                    event_data = jp.loads(event)
                    if event_data["type"] == "final_response":
                        insight_data = event_data["content"]
                    else:
                        yield event
            else:
                if custom_prompt_name:
                    LoggingHelper.info("Using custom prompt in pdf processor", user_id=user_id, project_id=project_id,
                                      extra_data={"custom_prompt_name": custom_prompt_name})
                    insight_data = await utils.custom_llm_prompt_call(custom_prompt_name, extracted_text_per_page, project_id, self.mongo_manager, tables="",suggested_questions=[],page_no="",response_type="brief",source_type=source_type, images="", token_logger=token_logger, unified_logger=unified_logger)
                    # Set prompt to empty string when using custom prompt
                    prompt = ""
                else:
                    # Get token consumption from token logger for file upload response
                    if token_logger:
                        input_tokens, output_tokens = token_logger.get_total_tokens()
                        LoggingHelper.info(f"Using token consumption from token logger - Input: {input_tokens}, Output: {output_tokens}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={"input_tokens": input_tokens, "output_tokens": output_tokens})
                    else:
                        input_tokens, output_tokens = 0, 0
                        LoggingHelper.info("No token logger available, using 0 tokens", user_id=user_id, project_id=project_id)

                    insight_data = utils.sample_data(project_id,tables="",suggested_questions=[],page_no="",input_tokens_used=input_tokens,output_tokens_used=output_tokens,response_type="brief",source_type=source_type, images="")

        if file and not exists:
            complete_chat = []
            complete_chat = ({
                "filename": file_name or "",
                "query": prompt,
                "response": insight_data
            })

        # Process insights and tables
        insights_tables_data_for_response = insight_data["tables"]
        if insight_data["tables"]:
            LoggingHelper.info("Tables found in insight data, Preparing to save tables in mongo db tables collection",
                              user_id=user_id, project_id=project_id)
            # table_id=utils.get_unique_id()
            # LoggingHelper.info("Unique id for table: %s", table_id)
            # table_data={"tableId": table_id, "tables":insight_data["tables"]}
            table_data={"tables":insight_data["tables"]}
            LoggingHelper.info("Starting to save tables to tables collection", user_id=user_id, project_id=project_id)
            project_object_id = await self.mongo_manager.get_project_object_id(project_id)

            tables_collection_id = await self.mongo_manager.save_tables(project_object_id, table_data)
            LoggingHelper.info("Tables saved in mongo db tables collection with id: %s",
                              user_id=user_id, project_id=project_id,
                              extra_data={"tables_collection_id": str(tables_collection_id)})

            LoggingHelper.info("Replacing the tables in insight data with table id only for saving in insights collection",
                              user_id=user_id, project_id=project_id)
            insight_data["tables"] = tables_collection_id
        LoggingHelper.info("Putting images as '' in insights data as it will be empty in modes other than data analysis.",
                          user_id=user_id, project_id=project_id)
        insight_data["images"] = ""
        response_id = str(ObjectId())
        insight_data["response_id"] = response_id

        # Add token usage to insight_data BEFORE saving to MongoDB
        if token_logger:
            input_tokens, output_tokens = token_logger.get_total_tokens()
            insight_data["input_tokens_used"] = input_tokens
            insight_data["output_tokens_used"] = output_tokens
            insight_data["total_tokens_used"] = input_tokens + output_tokens

            # Link token logger with response_id for proper tracking
            token_logger.set_response_id(response_id)

            LoggingHelper.info(f"Added token usage to insight_data before saving - Input: {input_tokens}, Output: {output_tokens}, Total: {input_tokens + output_tokens}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"input_tokens": input_tokens, "output_tokens": output_tokens,
                                        "total_tokens": input_tokens + output_tokens, "response_id": response_id})
            LoggingHelper.info(f"Linked token events with response_id: {response_id}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"response_id": response_id})

        LoggingHelper.info("Saving project data in mongo db insights collection for pdf processor",
                          user_id=user_id, project_id=project_id)

        # Add response_type to insight_data
        # insight_data["response_type"] = response_type
        # Save project data and insights back to MongoDB
        new_chat = {
            "filename": file_name or "",
            "query": original_prompt,
            "response": insight_data
        }
        complete_chat.append(new_chat)

        # complete_chat.append({original_prompt: insight_data})
        await self.save_project_data(file_exist,project_id, original_prompt, user_id, page_numbers, file_name, file_type, title, insight_data, extracted_text_per_page, extracted_text,intent,complete_chat, custom_prompt_name)
        LoggingHelper.info("Saved project data in mongo db for pdf processor", user_id=user_id, project_id=project_id)

        LoggingHelper.info("Replacing the tables in insight data with actual table data for response sending",
                          user_id=user_id, project_id=project_id)
        if insight_data["tables"]:
            insight_data["tables"] = insights_tables_data_for_response

        project_object_id = await self.mongo_manager.get_project_object_id(project_id)
        insight_data["object_id"] = str(project_object_id)

        # Token usage was already added before saving to MongoDB
        if token_logger:
            input_tokens, output_tokens = token_logger.get_total_tokens()
            LoggingHelper.info(f"Token usage already included in saved data - Input: {input_tokens}, Output: {output_tokens}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"input_tokens": input_tokens, "output_tokens": output_tokens})

        # LoggingHelper.info(f"Appended object id to insight data:{insight_data["object_id"]} for response sending")

        # Yield final response event
        yield jp.dumps({
            "type": "final_response",
            "content": insight_data
        })


    def extract_text_from_existing_project(self, project_data: dict, page_number: Optional[str], project_id,file_name):
        # Note: This method doesn't have user_id context, using project_id only
        LoggingHelper.info("Extracting text from existing project for pdf processor",
                          user_id=None, project_id=project_id)

        full_text = project_data.get("full_text", [])
        LoggingHelper.info("Length of full text from MongoDB: %s", user_id=None, project_id=project_id,
                          extra_data={"full_text_length": len(full_text)})
        total_pages = len(full_text)
        LoggingHelper.info("Total pages from DB as per full text: %s", user_id=None, project_id=project_id,
                          extra_data={"total_pages": total_pages})

        if page_number:
            page_numbers = utils.expand_ranges(page_number)
            LoggingHelper.info("Page numbers after expanding ranges: %s", user_id=None, project_id=project_id,
                              extra_data={"page_numbers": page_numbers})
            valid_page_numbers = [page_num for page_num in page_numbers if 1 <= page_num <= total_pages]
            LoggingHelper.info("Valid page numbers: %s", user_id=None, project_id=project_id,
                              extra_data={"valid_page_numbers": valid_page_numbers})
        else:
            valid_page_numbers = list(range(1, total_pages + 1))
            LoggingHelper.info("Valid page numbers set to default range: %s", user_id=None, project_id=project_id,
                              extra_data={"valid_page_numbers": valid_page_numbers})
        if not valid_page_numbers:
            valid_page_numbers = list(range(1, total_pages + 1))
            LoggingHelper.info("Valid page numbers set to default range in case of no valid page numbers: %s",
                              user_id=None, project_id=project_id,
                              extra_data={"valid_page_numbers": valid_page_numbers})

        previous_responses = project_data.get("chat_response", "")
        LoggingHelper.info("Length of previous responses from MongoDB: %s", user_id=None, project_id=project_id,
                          extra_data={"previous_responses_length": len(previous_responses)})
        previous_prompt = project_data.get("chat_prompt", "")
        LoggingHelper.info("Length of previous prompt from MongoDB: %s", user_id=None, project_id=project_id,
                          extra_data={"previous_prompt_length": len(previous_prompt)})
        # previous_intent = project_data.get("intent", "")
        previous_intent = project_data.get("description", "")
        LoggingHelper.info("Length of previous intent from MongoDB: %s", user_id=None, project_id=project_id,
                          extra_data={"previous_intent_length": len(previous_intent)})
        return valid_page_numbers, previous_responses, previous_prompt,previous_intent



    def convert_page_numbers_to_string(self, page_numbers: List[int]):
        # Note: This utility method doesn't have context, using generic logging
        LoggingHelper.info("Converting page numbers to string for pdf processor", user_id=None, project_id=None)
        # Convert the list of page numbers to a string with ranges
        if not page_numbers:
            LoggingHelper.info("Page numbers list is empty. Returning empty string.", user_id=None, project_id=None)
            return ''

        ranges = []
        start = page_numbers[0]
        end = page_numbers[0]

        for i in range(1, len(page_numbers)):
            if page_numbers[i] == end + 1:
                LoggingHelper.info("Page number is consecutive(end+1). Updating end to: %s",
                                  user_id=None, project_id=None,
                                  extra_data={"new_end": page_numbers[i]})
                end = page_numbers[i]
            else:
                if start == end:
                    LoggingHelper.info("Page number is not consecutive(start==end). Appending single page number: %s",
                                      user_id=None, project_id=None,
                                      extra_data={"page_number": start})
                    ranges.append(f"{start}")
                else:
                    LoggingHelper.info("Page number is not consecutive(start!=end). Appending range: %s-%s",
                                      user_id=None, project_id=None,
                                      extra_data={"start": start, "end": end})
                    ranges.append(f"{start}-{end}")
                start = end = page_numbers[i]

        if start == end:
            LoggingHelper.info("Page number is not consecutive(start==end). Appending single page number: %s",
                              user_id=None, project_id=None,
                              extra_data={"page_number": start})
            ranges.append(f"{start}")
        else:
            LoggingHelper.info("Page number is not consecutive(start!=end). Appending range: %s-%s",
                              user_id=None, project_id=None,
                              extra_data={"start": start, "end": end})
            ranges.append(f"{start}-{end}")

        LoggingHelper.info("Page numbers converted to string: %s", user_id=None, project_id=None,
                          extra_data={"result": ",".join(ranges)})
        return ",".join(ranges)


    async def get_intent(self, context, project_id):
        try:
            # Get project model and use economy model from same provider
            project_model = await self.mongo_manager.get_model_for_project(project_id)
            from services.llm_service import get_provider_economy_model
            economy_model = get_provider_economy_model(project_model)

            LoggingHelper.info("Getting intent for pdf processor using economy model: %s (project model: %s)",
                              user_id=None, project_id=project_id,
                              extra_data={"economy_model": economy_model, "project_model": project_model})
            LoggingHelper.info("Length of context for getting intent: %s", user_id=None, project_id=project_id,
                              extra_data={"context_length": len(context)})
            insight_prompt = prompts.get_intent(context)

            # Use unified LLM service instead of direct OpenAI client
            from services.llm_service import create_llm_service
            llm_service = await create_llm_service(economy_model)

            messages = [{'role': 'system', 'content': insight_prompt}]

            # Use Pydantic model for structured output
            from models.insight_model import DocumentIntentExtraction
            response = await llm_service.structured_output(
                messages=messages,
                pydantic_model=DocumentIntentExtraction,
                temperature=0
            )

            # Convert Pydantic model to dict first, then to JSON string for backward compatibility
            if hasattr(response, 'model_dump'):
                response_dict = response.model_dump()
            elif hasattr(response, 'dict'):
                # Keep for backward compatibility with older Pydantic versions
                response_dict = response.dict()
            else:
                response_dict = response

            # Return JSON response as string for backward compatibility
            if isinstance(response_dict, dict):
                return json.dumps(response_dict)
            else:
                return response_dict

        except Exception as e:
            LoggingHelper.error(f"Error getting intent: {str(e)}", user_id=None, project_id=project_id,
                               extra_data={"error": str(e)})
            handle_general_error(e)
            # This line won't be reached if handle_general_error raises an exception as expected

    async def generate_insight(self, saved_in_mongo, project_id, file,
                             prompt: str, extracted_text: str,
                             chat_history: List[dict], page_numbers: str,
                             valid_page_numbers, response_type: str = "detailed",
                             visualization: bool = False, token_logger=None, user_id: str = None, unified_logger=None, source_type: str = "file"):
        try:
            index = prompt.find('|||')
            LoggingHelper.info("Index of ||| in prompt: %s", user_id=None, project_id=project_id,
                              extra_data={"index": index})

            model = await self.mongo_manager.get_model_for_project(project_id)
            LoggingHelper.info("Model for the project for insight generation: %s", user_id=None, project_id=project_id,
                              extra_data={"model": model})
            if len(chat_history) > 5:
                chat_history = chat_history[-5:]
                LoggingHelper.info("Chat history truncated to last 5 items: %s", user_id=None, project_id=project_id,
                                  extra_data={"chat_history_length": len(chat_history)})
            else:
                chat_history = chat_history
                LoggingHelper.info("Chat history not truncated", user_id=None, project_id=project_id)

            # Apply intelligent content truncation if extracted_text is too large
            from services.text_extraction_utils import truncate_content_intelligently
            from services.llm_service import get_model_context_window
            from services.token_utils import get_tokens

            # Calculate available tokens for content
            context_window = get_model_context_window(model)
            safety_margin = 5000

            # Estimate tokens for prompt components (rough estimation)
            prompt_overhead = get_tokens(prompt, model) + 2000  # Extra for system prompt and formatting
            chat_history_tokens = sum(get_tokens(str(item), model) for item in chat_history) if chat_history else 0
            available_for_content = context_window - safety_margin - prompt_overhead - chat_history_tokens

            # Check if extracted_text needs truncation
            content_tokens = get_tokens(extracted_text, model)
            if content_tokens > available_for_content:
                LoggingHelper.warning(
                    f"Content exceeds available tokens ({content_tokens} > {available_for_content}), applying intelligent truncation",
                    user_id=user_id, project_id=project_id,
                    extra_data={
                        "original_tokens": content_tokens,
                        "available_tokens": available_for_content,
                        "context_window": context_window,
                        "safety_margin": safety_margin
                    }
                )
                extracted_text = await truncate_content_intelligently(
                    extracted_text, available_for_content, model
                )
                LoggingHelper.info(
                    f"Content truncated from {content_tokens} to {get_tokens(extracted_text, model)} tokens",
                    user_id=user_id, project_id=project_id
                )

            current_date = datetime.now(timezone.utc).date()
            insight_prompt = prompts.get_insight_prompt(
                current_date,
                extracted_text=extracted_text,
                chat_history=chat_history,
                prompt=prompt,
                response_type=response_type,
                visualization=visualization
            )

            input_tokens_used = utils.get_tokens(insight_prompt)
            LoggingHelper.info("Length of input tokens for insight generation: %s", user_id=None, project_id=project_id,
                              extra_data={"input_tokens_used": input_tokens_used})

            try:
                # Log streaming start for PDF processor
                LoggingHelper.info("STREAMING_START - PDF processor streaming initiated",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={
                                      "stream_type": "pdf_processor",
                                      "prompt_length": len(prompt) if prompt else 0,
                                      "extracted_text_length": len(str(extracted_text)) if extracted_text else 0,
                                      "response_type": response_type,
                                      "visualization": visualization,
                                      "valid_page_numbers": valid_page_numbers
                                  })

                # Use the fake_llm_resonse function which already handles streaming with unified LLM service
                # This replaces the direct OpenAI client usage and provides DeepSeek compatibility
                async for event in utils.fake_llm_resonse(
                    prompt, project_id, self.mongo_manager, extracted_text,
                    chat_history, valid_page_numbers, response_type,
                    visualization, datetime.now(timezone.utc).date(),
                    source_type, token_logger, user_id, unified_logger
                ):
                    yield event
            except Exception as e:
                handle_general_error(e)
        except Exception as e:
            handle_general_error(e)

    async def save_project_data(self, file_exist, project_id: str, prompt: str, user_id: str, page_numbers: List[int], file_name: Optional[str], file_type: Optional[str], title: Optional[str], insight_data, extracted_text_per_page: List[str], extracted_text: str, intent: str, complete_chat, custom_prompt_name: Optional[str] = None):
        # Ensure title is never null
        if title is None:
            title = 'Untitled'
            LoggingHelper.info("Set default title to 'Untitled' in save_project_data", user_id=user_id, project_id=project_id)
        LoggingHelper.info("Saving project data in mongo db", user_id=user_id, project_id=project_id)
        # timestamp = int(time.utcnow().timestamp())
        # LoggingHelper.info("Timestamp for saving project data in mongo db: %s", timestamp)
        existing_project = await self.mongo_manager.get_project(project_id)

        # Process custom prompt if provided
        custom_prompt_content = None
        if custom_prompt_name:
            LoggingHelper.info(f"Processing custom prompt: {custom_prompt_name}", user_id=user_id, project_id=project_id,
                              extra_data={"custom_prompt_name": custom_prompt_name})
            # Store the original custom_prompt_name for later use
            original_custom_prompt_name = custom_prompt_name

            # Check if custom_prompt_name has the format "category|||subcategory|||prompt_text"
            if "|||" in custom_prompt_name:
                # Split the string by |||
                parts = custom_prompt_name.split("|||")

                # Extract category, subcategory and prompt text
                if len(parts) >= 3:
                    prompt_text = parts[-1].strip()  # The prompt text is the last part
                    custom_prompt_content = prompt_text
                elif len(parts) == 2:
                    prompt_text = parts[1].strip()
                    custom_prompt_content = prompt_text
            else:
                # Get the prompt text using the function from custom_prompts
                custom_prompt_content = utils.custom_prompts.get_prompt_by_name(custom_prompt_name)

            LoggingHelper.info(f"Extracted custom prompt content: {custom_prompt_content}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"custom_prompt_content": custom_prompt_content[:100] if custom_prompt_content else None})

        if existing_project:
            LoggingHelper.info("Project exists in mongo db", user_id=user_id, project_id=project_id)
            # Append new data to existing data


            existing_context = existing_project.get("context", [])
            existing_full_text = existing_project.get("full_text", [])
            existing_file_details = existing_project.get("file_details", [])
            # existing_complete_chat=existing_project.get("complete_chat",[])
            # existing_file_intent = existing_project.get("intent", [])

            if file_name is not None:
                # LoggingHelper.info("File name is not None")
                pkl_file_path = os.path.join(
                    config.file_path,
                    project_id,
                    config.non_data_analysis_folder_name,
                    file_name
                )
                LoggingHelper.info(f"Saving pickle file to-- : {pkl_file_path}", user_id=user_id, project_id=project_id,
                                  extra_data={"pkl_file_path": pkl_file_path, "file_name": file_name})
                # Update project data with new entries
            if file_exist and file_name is not None:
                LoggingHelper.info("File Exists", user_id=user_id, project_id=project_id)
                file_id = str(uuid.uuid4())
                LoggingHelper.info("File id for saving project data in mongo db: %s", user_id=user_id, project_id=project_id,
                                  extra_data={"file_id": file_id})
                LoggingHelper.info(f"------------------------494", user_id=user_id, project_id=project_id)

                project_data = {
                    "pages": page_numbers,
                    "user_id": user_id,  # user_id as string
                    "title": title or existing_project.get("title", 'Untitled'),
                    # "timestamp": timestamp,
                    "full_text": existing_full_text + list(extracted_text_per_page),
                    # "context": existing_context + [extracted_text],
                    # "description": existing_file_intent + [{file_name: intent}]
                }

                # Update file details with new structure
                new_file_details = existing_file_details.copy()
                # new_file_details=[]
                new_file_detail = {
                    "file_name": file_name,
                    "file_type": file_type,
                    "file_id": file_id,
                    "file_path": pkl_file_path,
                    "file_description": intent,
                    "custom_prompt": custom_prompt_name or "",
                    "custom_prompt_content": custom_prompt_content or ""
                }

                # Add custom prompt fields if available
                if custom_prompt_name:
                    new_file_detail["custom_prompt"] = custom_prompt_name
                    new_file_detail["custom_prompt_content"] = custom_prompt_content

                new_file_details.append(new_file_detail)
                project_data["file_details"] = new_file_details
                project_data["complete_chat"]=complete_chat
                LoggingHelper.info("Project data saved in mongo db when file exists", user_id=user_id, project_id=project_id)
            else:
                LoggingHelper.info("File does not exist", user_id=user_id, project_id=project_id)
                project_data = {
                    "pages": page_numbers,
                    "user_id": user_id,  # user_id as string
                    "title": title or existing_project.get("title", 'Untitled'),
                    # "timestamp": timestamp,
                    "full_text": existing_full_text,
                    # "context": existing_context,
                    # "description": existing_file_intent
                    "complete_chat":complete_chat
                }
            await self.mongo_manager.update_project(project_id, project_data)
            LoggingHelper.info("Project data saved in mongo db when file does not exist", user_id=user_id, project_id=project_id)
        else:
            # If project doesn't exist, create a new one
            file_id = str(uuid.uuid4())  # Generate a UUID for PDF files
            LoggingHelper.info("File id for saving project data in mongo db when project does not exist: %s",
                              user_id=user_id, project_id=project_id,
                              extra_data={"file_id": file_id})

            file_detail = None
            if file_exist and file_name is not None:
                file_detail = {
                    "file_name": file_name,
                    "file_type": file_type,
                    "file_id": file_id,
                    "file_path": os.path.join(config.file_path, project_id, config.non_data_analysis_folder_name, file_name),
                    "file_description": intent,
                    "custom_prompt": custom_prompt_name or "",
                    "custom_prompt_content": custom_prompt_content or ""
                }

                # Add custom prompt fields if available
                if custom_prompt_name:
                    file_detail["custom_prompt"] = custom_prompt_name
                    file_detail["custom_prompt_content"] = custom_prompt_content

            project_data = {
                "file_details": [file_detail] if file_detail else [],
                "pages": page_numbers,
                "user_id": user_id,  # user_id as string
                "title": title or 'Untitled',
                # "timestamp": timestamp,
                "full_text": extracted_text_per_page,
                "complete_chat":complete_chat,
                "isDeleted": False,  # Add isDeleted field set to False by default
                "is_templated_project": False  # Add is_templated_project field set to False by default
                # "context": [extracted_text],
                # "description": [{file_name: intent}] if file_exist else [],
            }

            # Insert new project data into MongoDB
            self.mongo_manager.insert_project(project_id, project_data)
            LoggingHelper.info("Project data saved in mongo db when project does not exist", user_id=user_id, project_id=project_id)
        # Append insight data to the project
        # If custom_prompt_name is provided, set prompt to empty string
        prompt_to_save = "" if custom_prompt_name else prompt

        await self.mongo_manager.append_to_project(project_id, {
            "response": insight_data,
            "prompt": prompt_to_save,
            "chat_response": insight_data,
            "chat_prompt": prompt_to_save,
        })

        # Print processing time
        end_time = time.time()
        elapsed_time = end_time - self.start_time
        # print(f"Processing time: {elapsed_time:.2f} seconds")





