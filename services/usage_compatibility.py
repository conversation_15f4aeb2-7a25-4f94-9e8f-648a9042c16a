"""
Backward Compatibility Layer for Usage Tracking
Provides wrapper classes that maintain the existing API while using the unified system underneath.
"""

from typing import Dict, List, Optional, Any
from services.unified_usage_tracking import UnifiedUsageLogger, create_unified_usage_logger


class TokenEventLoggerCompat:
    """
    Backward compatibility wrapper for TokenEventLogger.
    Uses UnifiedUsageLogger underneath but maintains the same API.
    """
    
    def __init__(self, project_id: str, user_id: str = None, query_id: str = None, mongo_manager=None):
        self._unified_logger = UnifiedUsageLogger(
            project_id=project_id,
            user_id=user_id,
            query_id=query_id,
            mongo_manager=mongo_manager
        )
        
        # Maintain compatibility properties
        self.project_id = self._unified_logger.project_id
        self.user_id = self._unified_logger.user_id
        self.query_id = self._unified_logger.query_id
        self.response_id = self._unified_logger.response_id
        self.events = []  # Will be populated from unified events
        
    @property
    def _total_input_tokens(self):
        """Backward compatibility property"""
        return sum(event.input_tokens for event in self._unified_logger.events if event.service_category == "llm")
    
    @property
    def _total_output_tokens(self):
        """Backward compatibility property"""
        return sum(event.output_tokens for event in self._unified_logger.events if event.service_category == "llm")
    
    @property
    def _total_operations(self):
        """Backward compatibility property"""
        return len([event for event in self._unified_logger.events if event.service_category == "llm"])

    def log_event(self, 
                  operation_type: str,
                  model_name: str,
                  input_tokens: int,
                  output_tokens: int,
                  operation_subtype: str = "general",
                  operation_details: Dict[str, Any] = None,
                  success: bool = True,
                  error_message: str = None) -> None:
        """Log LLM token event using unified logger"""
        self._unified_logger.log_llm_usage(
            model_name=model_name,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            operation_type=operation_type,
            operation_subtype=operation_subtype,
            operation_details=operation_details,
            success=success,
            error_message=error_message
        )
        
        # Update compatibility events list
        self._update_events_list()

    def _update_events_list(self):
        """Update the events list for backward compatibility"""
        # Filter only LLM events for backward compatibility
        llm_events = [event for event in self._unified_logger.events if event.service_category == "llm"]
        self.events = [
            {
                "timestamp": event.timestamp,
                "operation_type": event.operation_type,
                "operation_subtype": event.operation_subtype,
                "model_name": event.operation_details.get("model_name", "unknown"),
                "input_tokens": event.input_tokens,
                "output_tokens": event.output_tokens,
                "total_tokens": event.total_tokens,
                "operation_details": event.operation_details,
                "success": event.success,
                "error_message": event.error_message
            }
            for event in llm_events
        ]

    def get_total_consumption(self) -> tuple[int, int, int]:
        """Get total LLM consumption metrics"""
        return (
            self._total_operations,
            self._total_input_tokens,
            self._total_output_tokens
        )

    def get_total_tokens(self) -> tuple[int, int]:
        """Get total input and output tokens for backward compatibility with TokenEventLogger"""
        return self._total_input_tokens, self._total_output_tokens

    def log_step_summary(self, step_name: str, additional_info: str = "") -> None:
        """Log step summary for LLM operations"""
        self._unified_logger.log_step_summary(step_name, additional_info)

    async def save_to_mongodb(self) -> Optional[str]:
        """Save to MongoDB using unified logger"""
        return await self._unified_logger.save_to_mongodb()

    def set_response_id(self, response_id: str) -> None:
        """Set response ID"""
        self._unified_logger.set_response_id(response_id)
        self.response_id = response_id

    def get_events_summary(self) -> List[Dict[str, Any]]:
        """Get LLM events summary"""
        self._update_events_list()
        return self.events


class SearchEventLoggerCompat:
    """
    Backward compatibility wrapper for SearchEventLogger.
    Uses UnifiedUsageLogger underneath but maintains the same API.
    """
    
    def __init__(self, project_id: str, user_id: str = None, query_id: str = None, mongo_manager=None):
        self._unified_logger = UnifiedUsageLogger(
            project_id=project_id,
            user_id=user_id,
            query_id=query_id,
            mongo_manager=mongo_manager
        )
        
        # Maintain compatibility properties
        self.project_id = self._unified_logger.project_id
        self.user_id = self._unified_logger.user_id
        self.query_id = self._unified_logger.query_id
        self.response_id = self._unified_logger.response_id
        self.events = []  # Will be populated from unified events
        
    @property
    def _total_search_operations(self):
        """Backward compatibility property"""
        return len([event for event in self._unified_logger.events if event.service_category == "search"])
    
    @property
    def _total_query_tokens(self):
        """Backward compatibility property"""
        return sum(event.input_tokens for event in self._unified_logger.events if event.service_category == "search")
    
    @property
    def _total_response_tokens(self):
        """Backward compatibility property"""
        return sum(event.output_tokens for event in self._unified_logger.events if event.service_category == "search")

    def log_event(self, 
                  operation_type: str,
                  service_provider: str,
                  query: str,
                  response_text: str,
                  operation_subtype: str = "general",
                  operation_details: Dict[str, Any] = None,
                  success: bool = True,
                  error_message: str = None) -> None:
        """Log search event using unified logger"""
        self._unified_logger.log_search_usage(
            service_provider=service_provider,
            query=query,
            response_text=response_text,
            operation_subtype=operation_subtype,
            operation_details=operation_details,
            success=success,
            error_message=error_message
        )
        
        # Update compatibility events list
        self._update_events_list()

    def _update_events_list(self):
        """Update the events list for backward compatibility"""
        # Filter only search events for backward compatibility
        search_events = [event for event in self._unified_logger.events if event.service_category == "search"]
        self.events = [
            {
                "timestamp": event.timestamp,
                "operation_type": event.operation_type,
                "operation_subtype": event.operation_subtype,
                "service_provider": event.service_provider,
                "service_tier": event.service_tier,
                "query": event.operation_details.get("query", ""),
                "query_tokens": event.input_tokens,
                "response_text": event.operation_details.get("response_text", ""),
                "response_tokens": event.output_tokens,
                "total_tokens": event.total_tokens,
                "operation_details": event.operation_details,
                "success": event.success,
                "error_message": event.error_message
            }
            for event in search_events
        ]

    def log_duckduckgo_search(self, query: str, response_text: str, 
                             operation_details: Dict[str, Any] = None,
                             success: bool = True, error_message: str = None) -> None:
        """Log DuckDuckGo search usage"""
        self.log_event(
            operation_type="web_search",
            service_provider="duckduckgo",
            query=query,
            response_text=response_text,
            operation_subtype="duckduckgo_search",
            operation_details=operation_details,
            success=success,
            error_message=error_message
        )

    def log_serper_search(self, query: str, response_text: str,
                         operation_details: Dict[str, Any] = None,
                         success: bool = True, error_message: str = None) -> None:
        """Log Serper API search usage"""
        self.log_event(
            operation_type="web_search",
            service_provider="serper",
            query=query,
            response_text=response_text,
            operation_subtype="serper_api_search",
            operation_details=operation_details,
            success=success,
            error_message=error_message
        )

    def get_total_consumption(self) -> tuple[int, int, int]:
        """Get total search consumption metrics"""
        return (
            self._total_search_operations,
            self._total_query_tokens,
            self._total_response_tokens
        )

    def log_step_summary(self, step_name: str, additional_info: str = "") -> None:
        """Log step summary for search operations"""
        self._unified_logger.log_step_summary(step_name, additional_info)

    async def save_to_mongodb(self) -> Optional[str]:
        """Save to MongoDB using unified logger"""
        return await self._unified_logger.save_to_mongodb()

    def set_response_id(self, response_id: str) -> None:
        """Set response ID"""
        self._unified_logger.set_response_id(response_id)
        self.response_id = response_id

    def get_events_summary(self) -> List[Dict[str, Any]]:
        """Get search events summary"""
        self._update_events_list()
        return self.events


# Factory functions for backward compatibility
def create_token_logger_compat(project_id: str, user_id: str = None, mongo_manager=None) -> TokenEventLoggerCompat:
    """Factory function to create a backward compatible token logger"""
    return TokenEventLoggerCompat(
        project_id=project_id,
        user_id=user_id,
        mongo_manager=mongo_manager
    )

def create_search_logger_compat(project_id: str, user_id: str = None, mongo_manager=None) -> SearchEventLoggerCompat:
    """Factory function to create a backward compatible search logger"""
    return SearchEventLoggerCompat(
        project_id=project_id,
        user_id=user_id,
        mongo_manager=mongo_manager
    )
