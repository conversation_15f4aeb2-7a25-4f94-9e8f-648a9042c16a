"""
Asynchronous file system utilities.
This module provides asynchronous versions of common file system operations.
"""

import os
import asyncio
import logging
from typing import Optional
from services.logging_utils import <PERSON><PERSON><PERSON><PERSON><PERSON>

def _get_context_or_fallback():
    """Get user/project context with fallback to None for utility operations."""
    try:
        from services.logging_middleware import get_current_user_id, get_current_project_id
        return get_current_user_id(), get_current_project_id()
    except:
        return None, None

async def async_makedirs(path: str, exist_ok: bool = False, mode: int = 0o777) -> None:
    """
    Asynchronous version of os.makedirs.
    Creates directories recursively.

    Args:
        path: The directory path to create
        exist_ok: If False, an error is raised if the directory exists
        mode: The file mode to set for the new directories
    """
    # Run os.makedirs in a thread pool to avoid blocking the event loop
    await asyncio.to_thread(os.makedirs, path, exist_ok=exist_ok, mode=mode)
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.debug("Created directory", user_id=user_id, project_id=project_id,
                       extra_data={"path": path})

async def async_remove(path: str) -> None:
    """
    Asynchronous version of os.remove.
    Removes a file.

    Args:
        path: The file path to remove
    """
    # Run os.path.exists and os.remove in a thread pool to avoid blocking the event loop
    path_exists = await asyncio.to_thread(os.path.exists, path)
    if path_exists:
        await asyncio.to_thread(os.remove, path)
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.debug("Removed file", user_id=user_id, project_id=project_id,
                           extra_data={"path": path})
    else:
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.warning("File not found, cannot remove", user_id=user_id, project_id=project_id,
                             extra_data={"path": path})

async def async_path_exists(path: str) -> bool:
    """
    Asynchronous version of os.path.exists.
    Checks if a path exists.

    Args:
        path: The path to check

    Returns:
        True if the path exists, False otherwise
    """
    # Run os.path.exists in a thread pool to avoid blocking the event loop
    return await asyncio.to_thread(os.path.exists, path)

async def async_rmtree(path: str) -> None:
    """
    Asynchronous version of shutil.rmtree.
    Removes a directory tree recursively.

    Args:
        path: The directory path to remove
    """
    import shutil

    path_exists = await asyncio.to_thread(os.path.exists, path)
    if path_exists:
        await asyncio.to_thread(shutil.rmtree, path)
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.debug("Removed directory tree", user_id=user_id, project_id=project_id,
                           extra_data={"path": path})
    else:
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.warning("Directory not found, cannot remove", user_id=user_id, project_id=project_id,
                             extra_data={"path": path})
