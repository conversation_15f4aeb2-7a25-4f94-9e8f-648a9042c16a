import logging
from datetime import datetime, timezone
from typing import Optional, List
from bson import ObjectId
import config
import json

logger = logging.getLogger(__name__)

# Configuration variables
insights_collection = config.insights_collection

# Functions to be moved here:

def get_files_and_description(intent):
    dataset_descriptions = {}
    filesearch_descriptions={}
    file_list=[]
    # for entry in intent:
    #     # Extract the key dynamically (it will be the filename)
    #     file_name = next(iter(entry))

    #     # Access the 'context_intent' for the current file
    #     context_intent = entry[file_name]['context_intent']
    for file_name, description in intent.items():
        # Access 'context_intent' for the current file
        context_intent = description.get('context_intent', {})

        # Print or process the context intent as needed
        print(f"File: {file_name}")
        print("Context Intent:", context_intent)
        # Print out the details
        print(f"File: {file_name}")
        print("Intent Description:", context_intent['intent_description'])
        print("Keywords:", context_intent['keywords'])
        print()  # For better readability between entries
        if file_name.split(".")[1]=="xlsx" or file_name.split(".")[1]=="csv":
            dataset_descriptions[file_name]={
            'description': context_intent['intent_description'],
            'keywords': context_intent['keywords']
        }
            file_list.append(file_name)
        elif file_name.split(".")[1]=="pdf" or file_name.split(".")[1]=="doc" or file_name.split(".")[1]=="docx" or file_name.split('.')[1]=='txt':
            filesearch_descriptions[file_name]={
            'description': context_intent['intent_description'],
            'keywords': context_intent['keywords']
        }


            file_list.append(file_name)
    return filesearch_descriptions,dataset_descriptions

async def get_project_id_from_object_id(object_id: str, mongo_manager=None) -> Optional[str]:
    """
    Get the project_id from an insights collection document using its _id.

    Args:
        object_id (str): The _id of the document in insights collection
        mongo_manager: Optional MongoManager instance. If not provided, uses insights_collection directly.

    Returns:
        Optional[str]: The project_id if found, None otherwise
    """
    try:
        # Convert string to ObjectId
        obj_id = ObjectId(object_id)

        # Query the insights collection
        if mongo_manager:
            # Use the mongo_manager's collection
            project = await mongo_manager.collection.find_one({"_id": obj_id}, {"project_id": 1})
        else:
            # Use the global insights_collection
            project = await insights_collection.find_one(
                {"_id": obj_id},
                {"project_id": 1}  # Only retrieve the project_id field
            )

        return project.get("project_id") if project else None

    except Exception as e:
        logger.error(f"Error getting project_id from object_id {object_id}: {str(e)}")
        return None


async def get_all_file_names_from_db(mongo_manager, project_id):
    try:
        project_data, _, _ = await mongo_manager.get_or_initialize_project(project_id)
        logger.info(f"Retrieved project data for project {project_id}")
        file_names = [file_detail['file_name'] for file_detail in project_data.get('file_details', [])]
        return file_names
    except Exception as e:
        logger.error(f"Error in getting file names from DB: {e}")
        return []


def sample_data(project_id, tables, suggested_questions, page_no, input_tokens_used, output_tokens_used, response_type, source_type, images, prompt=None):
    default_data = {
        "summary": f"### {prompt}\n{config.DEFAULT_UPLOAD_MESSAGE}" if prompt else config.DEFAULT_UPLOAD_MESSAGE,
        "tables": tables if tables else [],  # Keep as empty list to match InsightExtraction model
        "suggested_questions": suggested_questions,
        "page_no": page_no,
        "images" : "",
        "input_tokens_used": input_tokens_used,
        "output_tokens_used": output_tokens_used,
        "total_tokens_used": input_tokens_used + output_tokens_used,
        "response_type": response_type,
        "source_type": source_type,
        "response_time": datetime.now(timezone.utc).isoformat()
    }
    return default_data
