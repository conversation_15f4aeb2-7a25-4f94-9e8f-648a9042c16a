"""
Token utility functions for counting and estimating tokens.
This module is separate to avoid circular imports.
"""

import tiktoken
import logging
from typing import List, Dict, Any, Union
from langchain_core.messages import BaseMessage
from services.logging_utils import LoggingHelper

logger = logging.getLogger(__name__)

def get_tokens(text: str, model: str = "gpt-3.5-turbo") -> int:
    """
    Count the number of tokens in a text string using tiktoken.
    
    Args:
        text: The text to count tokens for
        model: The model name to use for encoding (default: gpt-3.5-turbo)
        
    Returns:
        Number of tokens in the text
    """
    try:
        # Map model names to tiktoken encodings
        model_to_encoding = {
            "gpt-4": "cl100k_base",
            "gpt-4-turbo": "cl100k_base", 
            "gpt-4o": "cl100k_base",
            "gpt-4o-mini": "cl100k_base",
            "gpt-3.5-turbo": "cl100k_base",
            "text-embedding-ada-002": "cl100k_base",
            "claude-3-sonnet": "cl100k_base",  # Approximate using GPT encoding
            "claude-3-haiku": "cl100k_base",   # Approximate using GPT encoding
            "claude-3-opus": "cl100k_base",    # Approximate using GPT encoding
            "deepseek-chat": "cl100k_base",    # DeepSeek uses similar tokenization
            "deepseek-coder": "cl100k_base",   # DeepSeek Coder uses similar tokenization
        }
        
        # Get the encoding name, default to cl100k_base
        encoding_name = model_to_encoding.get(model, "cl100k_base")
        
        # Get the encoding
        encoding = tiktoken.get_encoding(encoding_name)
        
        # Count tokens
        tokens = encoding.encode(str(text))
        return len(tokens)
        
    except Exception as e:
        LoggingHelper.warning(
            f"Error counting tokens with tiktoken: {e}",
            extra_data={"model": model, "text_length": len(str(text)), "error": str(e)}
        )
        # Fallback: rough estimation (1 token ≈ 4 characters)
        return len(str(text)) // 4

def estimate_tokens_from_messages(messages: List[Union[Dict[str, str], BaseMessage]], model: str = "gpt-3.5-turbo") -> int:
    """
    Estimate token count from a list of messages, handling multimodal content properly.

    Args:
        messages: List of messages (either dict format or LangChain BaseMessage)
        model: Model name for token counting

    Returns:
        Estimated token count
    """
    total_tokens = 0

    try:
        for message in messages:
            if isinstance(message, BaseMessage):
                # LangChain message format - handle multimodal content
                content = message.content
                role = getattr(message, 'type', 'user')

                # Check if content is multimodal (list of content parts)
                if isinstance(content, list):
                    # Multimodal message - handle text and images separately
                    for content_part in content:
                        if isinstance(content_part, dict):
                            if content_part.get("type") == "text":
                                # Count text tokens normally
                                text_content = content_part.get("text", "")
                                total_tokens += get_tokens(text_content, model)
                            elif content_part.get("type") == "image_url":
                                # Estimate image tokens based on image size, not base64 length
                                image_url = content_part.get("image_url", {}).get("url", "")
                                if image_url.startswith("data:image/"):
                                    # Extract base64 data and estimate tokens properly
                                    try:
                                        base64_data = image_url.split(",")[1]
                                        # Decode base64 to get actual image size
                                        import base64
                                        image_bytes = base64.b64decode(base64_data)
                                        # Use proper image token estimation
                                        from services.image_utils import estimate_image_tokens
                                        image_tokens = estimate_image_tokens(len(image_bytes))
                                        total_tokens += image_tokens
                                    except Exception as e:
                                        LoggingHelper.warning(f"Failed to estimate image tokens: {e}")
                                        # Fallback: use a reasonable default for images
                                        total_tokens += 1000
                else:
                    # Regular text content
                    content_tokens = get_tokens(str(content), model)
                    total_tokens += content_tokens

            elif isinstance(message, dict):
                # Dict format
                content = message.get('content', '')
                role = message.get('role', 'user')
                content_tokens = get_tokens(str(content), model)
                total_tokens += content_tokens
            else:
                # String format
                content = str(message)
                role = 'user'
                content_tokens = get_tokens(content, model)
                total_tokens += content_tokens

            # Add overhead for message formatting (role, etc.)
            # This is an approximation based on OpenAI's token counting
            overhead_tokens = 4  # Approximate overhead per message
            total_tokens += overhead_tokens
            
    except Exception as e:
        LoggingHelper.warning(
            f"Error estimating tokens from messages: {e}",
            extra_data={"model": model, "message_count": len(messages), "error": str(e)}
        )
        # Fallback estimation
        total_text = ""
        for message in messages:
            if isinstance(message, BaseMessage):
                total_text += message.content
            elif isinstance(message, dict):
                total_text += message.get('content', '')
            else:
                total_text += str(message)

        total_tokens = get_tokens(total_text, model)
    
    return total_tokens

def estimate_output_tokens(text: str, model: str = "gpt-3.5-turbo") -> int:
    """
    Estimate output tokens from generated text.
    
    Args:
        text: The generated text
        model: Model name for token counting
        
    Returns:
        Estimated token count
    """
    return get_tokens(text, model)

async def extract_tokens_unified(
    response: Any = None,
    accumulated_content: str = None,
    messages: List = None,
    model_name: str = None,
    operation_type: str = "general"
) -> Dict[str, int]:
    """
    Unified token extraction method for all LLM operations.
    Prioritizes actual tokens from API response, falls back to estimation.
    
    Args:
        response: LLM response object (for actual token extraction)
        accumulated_content: Accumulated content for streaming operations
        messages: Input messages for estimation
        model_name: Model name for accurate estimation
        operation_type: Type of operation for logging
        
    Returns:
        Dict with input_tokens, output_tokens, total_tokens, source (actual/estimated)
    """
    result = {
        "input_tokens": 0,
        "output_tokens": 0,
        "total_tokens": 0,
        "source": "unknown"
    }
    
    try:
        # Try to extract actual tokens from response first
        if response and hasattr(response, 'response_metadata'):
            usage = response.response_metadata.get('token_usage', {})
            if usage:
                result["input_tokens"] = usage.get('prompt_tokens', 0)
                result["output_tokens"] = usage.get('completion_tokens', 0)
                result["total_tokens"] = usage.get('total_tokens', 0)
                result["source"] = "actual"
                
                # For vision models, add estimated image tokens to input
                from services.llm_service import get_model_tier
                if model_name and get_model_tier(model_name) == "vision" and messages:
                    image_tokens = estimate_image_tokens_from_messages(messages)
                    result["input_tokens"] += image_tokens
                    result["source"] = "actual_with_vision_estimate"
                
                return result
        
        # Fallback to estimation for streaming or when actual tokens unavailable
        if messages:
            result["input_tokens"] = estimate_tokens_from_messages(messages, model_name or "gpt-4")
            
        # Estimate output tokens from various sources
        if accumulated_content:
            result["output_tokens"] = estimate_output_tokens(accumulated_content, model_name or "gpt-4")
        elif response:
            # Try different ways to extract content from response
            content_to_estimate = None
            
            if hasattr(response, 'content'):
                content_to_estimate = response.content
            elif hasattr(response, 'model_dump'):
                # Pydantic model - convert to JSON string
                import json
                content_to_estimate = json.dumps(response.model_dump())
            elif hasattr(response, 'dict'):
                # Pydantic v1 model - convert to JSON string
                import json
                content_to_estimate = json.dumps(response.dict())
            elif isinstance(response, dict):
                # Dictionary response
                import json
                content_to_estimate = json.dumps(response)
            else:
                # Fallback - convert to string
                content_to_estimate = str(response)
            
            if content_to_estimate:
                result["output_tokens"] = estimate_output_tokens(str(content_to_estimate), model_name or "gpt-4")
            
        result["total_tokens"] = result["input_tokens"] + result["output_tokens"]
        result["source"] = "estimated"
        
        return result
        
    except Exception as e:
        LoggingHelper.warning(f"Error in unified token extraction: {e}", 
                             extra_data={"operation_type": operation_type, "error": str(e)})
        return result

def estimate_image_tokens_from_messages(messages: List) -> int:
    """Estimate image tokens from multimodal messages"""
    total_image_tokens = 0
    try:
        for message in messages:
            if hasattr(message, 'content') and isinstance(message.content, list):
                for content_part in message.content:
                    if isinstance(content_part, dict) and content_part.get("type") == "image_url":
                        image_url = content_part.get("image_url", {}).get("url", "")
                        if image_url.startswith("data:image/"):
                            try:
                                # Extract base64 data and estimate tokens properly
                                base64_data = image_url.split(",")[1]
                                import base64
                                image_bytes = base64.b64decode(base64_data)
                                # Use proper image token estimation
                                from services.image_utils import estimate_image_tokens
                                image_tokens = estimate_image_tokens(len(image_bytes))
                                total_image_tokens += image_tokens
                            except Exception:
                                # Fallback: use a reasonable default for images
                                total_image_tokens += 1000
        return total_image_tokens
    except Exception:
        return 0
