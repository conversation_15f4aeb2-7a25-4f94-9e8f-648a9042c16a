import logging
import json
import tiktoken
import re
import os
from datetime import datetime, timezone
from typing import Optional, List
from exceptions.api_exceptions import handle_llm_error
from services.token_tracking import Token<PERSON>vent<PERSON>ogger
from services import prompts, custom_prompts
from models import insight_model
import uuid
import config
from bson import ObjectId
from services.file_utils import extract_filetype, download_file, get_chart_bytes, extract_sandbox_links, get_filenames_from_response, create_file_mapping, replace_sandbox_links
from services.text_extraction_utils import convert_page_numbers_to_string
from starlette.concurrency import run_in_threadpool
from services.logging_utils import LoggingHelper

# Keep old logger for backward compatibility, but use LoggingHelper for new logs
logger = logging.getLogger(__name__)

# Configuration variables
client = config.openai_client
openai_client = config.openai_client
default_model_name = config.default_model_name

# Functions to be moved here:

async def get_assistant(assistant_id):
    try:
        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            return client.beta.assistants.retrieve(assistant_id)

        return await run_in_threadpool(_sync_openai_call)
    except Exception as e:
        handle_llm_error(e)


async def get_thread(thread_id):
    try:
        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            return client.beta.threads.retrieve(thread_id)

        return await run_in_threadpool(_sync_openai_call)
    except Exception as e:
        handle_llm_error(e)


async def initialize_assistant(name, instructions, model, token_logger: Optional[TokenEventLogger] = None, unified_logger=None):
    # print("-----------MODEL NAME-----------")
    # print(model)
    # print("-----------MODEL NAME-----------")
    try:
        # Estimate tokens for assistant creation (instructions are the main token consumer)
        from services.token_utils import get_tokens
        instruction_tokens = get_tokens(instructions)

        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            return client.beta.assistants.create(
                name=name,
                instructions=instructions,
                tools=[{"type": "code_interpreter"}],
                model=model
            )

        assistant = await run_in_threadpool(_sync_openai_call)

        # Log assistant creation - UNIFIED SYSTEM ONLY
        if unified_logger:
            unified_logger.log_llm_usage(
                model_name=model,
                input_tokens=instruction_tokens,
                output_tokens=0,
                operation_type="assistant_management",
                operation_subtype="assistant_creation",
                operation_details={
                    "assistant_id": assistant.id if hasattr(assistant, 'id') else "unknown",
                    "assistant_name": name,
                    "model": model,
                    "instructions_length": len(instructions)
                },
                success=True
            )
        else:
            LoggingHelper.warning("No unified usage logger provided for assistant creation", 
                                 extra_data={"operation": "assistant_creation", "model": model})

        return assistant
    except Exception as e:
        handle_llm_error(e)


async def set_assistant_model(mongo_manager, project_id, model):
    try:
        # Ensure we use an OpenAI model for Assistants API
        from services.llm_service import get_assistant_model
        assistant_model = get_assistant_model(model)

        assistant_id = await mongo_manager.get_assistant_id(project_id)

        # Update the assistant with the new model
        def _sync_openai_call():
            return client.beta.assistants.update(
                assistant_id=assistant_id,
                model=assistant_model
            )

        updated_assistant = await run_in_threadpool(_sync_openai_call)
        LoggingHelper.info(
            f"Updated assistant {assistant_id} model to {assistant_model}",
            extra_data={
                "assistant_id": assistant_id,
                "new_model": assistant_model,
                "operation": "assistant_model_update"
            }
        )
        return updated_assistant
    except Exception as e:
        handle_llm_error(e)



async def add_query(query, thread, unified_logger=None):
    try:
        # Estimate input tokens for the query
        from services.token_utils import get_tokens
        input_tokens = get_tokens(query)

        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            return client.beta.threads.messages.create(
                thread_id=thread.id,
                role="user",
                content=query
            )

        message = await run_in_threadpool(_sync_openai_call)

        # Log to unified usage logger if provided (NEW SYSTEM)
        if unified_logger:
            unified_logger.log_llm_usage(
                model_name="openai_assistant",
                input_tokens=input_tokens,
                output_tokens=0,  # Message creation doesn't generate output
                operation_type="assistant_message",
                operation_subtype="message_creation",
                operation_details={
                    "thread_id": thread.id,
                    "message_id": message.id if hasattr(message, 'id') else "unknown",
                    "query_length": len(query)
                },
                success=True
            )

        return message
    except Exception as e:
        handle_llm_error(e)

# get_tokens function moved to llm_utils.py

async def get_assistant_tokens(thread_id, run_id, token_logger: Optional[TokenEventLogger] = None, unified_logger=None):
    # Retrieve the run
    try:
        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            return client.beta.threads.runs.retrieve(
                thread_id=thread_id,
                run_id=run_id
            )

        run = await run_in_threadpool(_sync_openai_call)
        input_token_count = run.usage.prompt_tokens
        output_token_count = run.usage.completion_tokens

        # Log assistant token usage - UNIFIED SYSTEM ONLY
        if unified_logger:
            unified_logger.log_llm_usage(
                model_name=run.model if hasattr(run, 'model') else "openai_assistant",
                input_tokens=input_token_count,
                output_tokens=output_token_count,
                operation_type="assistant_query",
                operation_subtype="assistant_run",
                operation_details={
                    "thread_id": thread_id,
                    "run_id": run_id,
                    "assistant_model": run.model if hasattr(run, 'model') else "unknown",
                    "run_status": run.status if hasattr(run, 'status') else "unknown"
                },
                success=True
            )
        else:
            LoggingHelper.warning("No unified usage logger provided for assistant token tracking", 
                                 extra_data={"operation": "assistant_token_tracking", "thread_id": thread_id, "run_id": run_id})

        return {"input_token_count": input_token_count, "output_token_count": output_token_count}
    except Exception as e:
        handle_llm_error(e)

# get_unique_id function moved to llm_utils.py
# get_intent function moved to llm_utils.py


# call_llm function moved to llm_utils.py


# get_compliance_report function moved to llm_utils.py


def extract_json(data):
    # Find the JSON string within the triple backticks
    json_match = re.search(r'```json\n(.*?)\n```', data, re.DOTALL)

    if json_match:
        json_str = json_match.group(1)
        try:
            # Parse the JSON string
            json_data = json.loads(json_str)
            return json_data
        except json.JSONDecodeError as e:
            # print(f"Error decoding JSON: {e}")
            return None
    else:
        # print("No JSON data found in the string")
        return None


async def draft_response(messages, project_id, token_logger: Optional[TokenEventLogger] = None, unified_logger=None):
    LoggingHelper.info(
        f"Drafting response for project: {project_id}",
        extra_data={"project_id": project_id, "operation": "draft_response_start"}
    )

    response_data = {
        "project_id": project_id,
        "summary": "",
        "tables": [],  # Keep as empty list to match InsightExtraction model
        "page_number": "",
        "suggested_questions": [],
        "files": [],
        "images": [],
        "input_tokens_used": "",
        "output_tokens_used": "",
        "response_time": datetime.now(timezone.utc).isoformat()
    }
    LoggingHelper.info(
        "Response data initialized",
        extra_data={"project_id": project_id, "operation": "response_data_init"}
    )

    # Convert SyncCursorPage to list and get the first message
    messages_list = list(messages)
    if not messages_list:
        LoggingHelper.warning(
            "No messages found in response",
            extra_data={"project_id": project_id, "operation": "draft_response_no_messages"}
        )
        response_data["summary"] = "No response received from the assistant"
        return response_data

    LoggingHelper.info(
        "Setting latest messages to first message in list",
        extra_data={"project_id": project_id, "message_count": len(messages_list)}
    )
    latest_message = messages_list[0]
    LoggingHelper.info(
        f"Latest message set with role: {latest_message.role}",
        extra_data={"project_id": project_id, "message_role": latest_message.role}
    )

    if latest_message.role == "assistant":
        LoggingHelper.info(
            "Latest message is from assistant",
            extra_data={"project_id": project_id, "operation": "processing_assistant_message"}
        )
        for content_item in latest_message.content:
            if content_item.type == 'text':
                LoggingHelper.info(
                    "Content item type is text",
                    extra_data={"project_id": project_id, "content_type": "text"}
                )
                
                # CRITICAL FIX: Ensure title preservation for OpenAI Assistants API responses
                # The Assistant may generate structured JSON where title was streamed but not included in summary field
                raw_text_value = content_item.text.value
                processed_summary = _preserve_title_in_assistant_response(raw_text_value, project_id)
                response_data["summary"] = processed_summary
                
                LoggingHelper.info(
                    "Summary set from content item text value with title preservation",
                    extra_data={"project_id": project_id, "summary_length": len(processed_summary)}
                )
                LoggingHelper.info(
                    "Getting run id and thread id",
                    extra_data={"project_id": project_id, "operation": "extracting_run_thread_ids"}
                )
                run_id = latest_message.run_id
                thread_id = latest_message.thread_id
                LoggingHelper.info(
                    f"Run id: {run_id} and thread id: {thread_id} retrieved",
                    extra_data={"project_id": project_id, "run_id": run_id, "thread_id": thread_id}
                )
                LoggingHelper.info(
                    "Getting tokens used",
                    extra_data={"project_id": project_id, "operation": "fetching_token_usage"}
                )
                # Get token data for response object (without tracking - already tracked during streaming)
                token_data = await get_assistant_tokens(thread_id, run_id, None, None)
                LoggingHelper.info(
                    f"Tokens used: {token_data}",
                    extra_data={"project_id": project_id, "token_data": token_data}
                )
                LoggingHelper.info(
                    "Setting input and output tokens used in response",
                    extra_data={"project_id": project_id, "operation": "setting_token_data"}
                )
                response_data["input_tokens_used"] = token_data["input_token_count"]
                response_data["output_tokens_used"] = token_data["output_token_count"]
                LoggingHelper.info(
                    "Input and output tokens used set in response",
                    extra_data={
                        "project_id": project_id,
                        "input_tokens": token_data["input_token_count"],
                        "output_tokens": token_data["output_token_count"]
                    }
                )

                if content_item.text.annotations:
                    LoggingHelper.info(
                        "Content item has annotations",
                        extra_data={"project_id": project_id, "annotation_count": len(content_item.text.annotations)}
                    )
                    for annotation in content_item.text.annotations:
                        if annotation.type == 'file_path':
                            LoggingHelper.info(
                                "Annotation type is file_path",
                                extra_data={"project_id": project_id, "annotation_type": "file_path"}
                            )
                            file_id = annotation.file_path.file_id
                            LoggingHelper.info(
                                f"File id: {file_id} retrieved",
                                extra_data={"project_id": project_id, "file_id": file_id}
                            )
                            file_name = os.path.basename(annotation.text)
                            LoggingHelper.info(
                                f"File name: {file_name} retrieved",
                                extra_data={"project_id": project_id, "file_name": file_name}
                            )
                            file_extension = extract_filetype(file_name)
                            LoggingHelper.info(
                                f"File extension: {file_extension} retrieved",
                                extra_data={"project_id": project_id, "file_extension": file_extension}
                            )

                            # append files that have file extension other than png to files
                            if file_extension != "png":
                                LoggingHelper.info(
                                    "File extension is not png",
                                    extra_data={"project_id": project_id, "file_extension": file_extension, "file_type": "document"}
                                )
                                file_data= {"file_id": file_id,
                                            # "file_name" : file_name,
                                            "file_name" : f"{project_id}/{file_name}",
                                            "file_type" : file_extension
                                            }
                                response_data["files"].append(file_data)
                                LoggingHelper.info(
                                    f"File data appended to files field in response",
                                    extra_data={"project_id": project_id, "file_data": file_data}
                                )
                                # response_data["files"].append(f"{file_id}.{file_extension}")
                            else:
                                LoggingHelper.info(
                                    "File extension is png",
                                    extra_data={"project_id": project_id, "file_extension": "png", "file_type": "image"}
                                )
                                LoggingHelper.info(
                                    f"Appending file id: {file_id} to images field in response",
                                    extra_data={"project_id": project_id, "file_id": file_id, "field": "images"}
                                )
                                response_data["images"].append(file_id)

            elif content_item.type == 'image_file':
                LoggingHelper.info(
                    "Content item type is image_file",
                    extra_data={"project_id": project_id, "content_type": "image_file"}
                )
                file_id = content_item.image_file.file_id
                LoggingHelper.info(
                    f"File id: {file_id} retrieved",
                    extra_data={"project_id": project_id, "file_id": file_id, "content_type": "image_file"}
                )
                LoggingHelper.info(
                    f"Appending file id: {file_id} to images field in response",
                    extra_data={"project_id": project_id, "file_id": file_id, "field": "images"}
                )
                response_data["images"].append(file_id)

        if response_data["files"]:
            LoggingHelper.info(
                "Files field in response is not empty",
                extra_data={"project_id": project_id, "file_count": len(response_data["files"])}
            )
            LoggingHelper.info(
                f"Downloading files: {response_data['files']}",
                extra_data={"project_id": project_id, "files_to_download": response_data["files"]}
            )
            download_file(response_data["files"])
            LoggingHelper.info(
                "Files downloaded",
                extra_data={"project_id": project_id, "operation": "file_download_complete"}
            )
            sandbox_links = extract_sandbox_links(response_data["summary"])
            LoggingHelper.info(
                f"Sandbox links: {sandbox_links}",
                extra_data={"project_id": project_id, "sandbox_links": sandbox_links}
            )
            filenames = get_filenames_from_response(response_data)
            LoggingHelper.info(
                f"Filenames: {filenames}",
                extra_data={"project_id": project_id, "filenames": filenames}
            )
            file_mapping = create_file_mapping(sandbox_links, filenames)
            LoggingHelper.info(
                f"File mapping: {file_mapping}",
                extra_data={"project_id": project_id, "file_mapping": file_mapping}
            )
            response_data["summary"] = replace_sandbox_links(response_data["summary"], file_mapping)
            LoggingHelper.info(
                "Summary modified with file links",
                extra_data={"project_id": project_id, "operation": "sandbox_links_replaced"}
            )

    else:
        LoggingHelper.info(
            "Latest message is not from assistant",
            extra_data={"project_id": project_id, "message_role": latest_message.role}
        )
        response_data["summary"] = config.DEFAULT_NO_RESPONSE_MESSAGE
        LoggingHelper.info(
            f"Summary set to default message: {response_data['summary']}",
            extra_data={"project_id": project_id, "default_message": config.DEFAULT_NO_RESPONSE_MESSAGE}
        )

    LoggingHelper.info(
        "Returning drafted response data",
        extra_data={"project_id": project_id, "operation": "draft_response_complete"}
    )
    return response_data



def draft_response_for_file_upload(message: str):
    return {
        'summary': message,
        'tables': "",
        'page_number': '',
        'suggested_questions': [],
        'files': [],
        'images': "",
        'input_tokens_used': 0,  # Changed from empty string to 0
        'output_tokens_used': 0,  # Changed from empty string to 0
        'total_tokens_used': 0,  # Added total tokens
        'response_time': datetime.now(timezone.utc).isoformat()
    }


# fake_llm_resonse function moved to llm_utils.py


# custom_llm_prompt_call function moved to llm_utils.py


def get_default_prompt():
    return config.DEFAULT_SYSTEM_PROMPT

def get_default_prompt_for_direct_chat():
    return config.DEFAULT_DIRECT_CHAT_PROMPT

def truncate_prompt_having_pipes(prompt):
    return prompt.split("|||")[0].strip()


def _preserve_title_in_assistant_response(raw_text: str, project_id: str) -> str:
    """
    Preserve title in OpenAI Assistant responses by extracting from full response
    and ensuring it's included in the final summary.
    """
    try:
        # First, try to parse as JSON (Assistants often return structured JSON)
        try:
            import json
            parsed_json = json.loads(raw_text.strip())
            if isinstance(parsed_json, dict) and "summary" in parsed_json:
                summary_content = parsed_json["summary"]
                
                # Check if title is missing from the summary
                if not summary_content.startswith("**"):
                    # Look for title in the full raw response
                    title_start = raw_text.find('**')
                    if title_start != -1:
                        title_end = raw_text.find('**', title_start + 2)
                        if title_end != -1:
                            title_with_markers = raw_text[title_start:title_end + 2]
                            preserved_summary = f"{title_with_markers}\n\n{summary_content}"
                            
                            LoggingHelper.info("Title preserved in Assistant response from JSON parsing", 
                                             extra_data={"project_id": project_id, "title": title_with_markers, 
                                                       "original_summary_length": len(summary_content),
                                                       "preserved_summary_length": len(preserved_summary)})
                            return preserved_summary
                
                return summary_content
                
        except (json.JSONDecodeError, Exception):
            # Not JSON, continue with text processing
            pass
        
        # Handle plain text responses
        # Check if the response already starts with a title
        if raw_text.strip().startswith("**"):
            return raw_text
        
        # If we have what looks like a summary without title, but title exists in the content
        # Look for title patterns in the full text
        title_start = raw_text.find('**')
        if title_start != -1:
            title_end = raw_text.find('**', title_start + 2)
            if title_end != -1:
                title_with_markers = raw_text[title_start:title_end + 2]
                
                # Try to find where the actual content starts (after the title)
                content_after_title = raw_text[title_end + 2:].strip()
                
                # If there's content after the title that looks like it should be the summary
                if content_after_title and not content_after_title.startswith("**"):
                    # Skip any newlines and combine properly
                    content_after_title = content_after_title.lstrip('\n').strip()
                    preserved_summary = f"{title_with_markers}\n\n{content_after_title}"
                    
                    LoggingHelper.info("Title preserved in Assistant response from text parsing", 
                                     extra_data={"project_id": project_id, "title": title_with_markers,
                                               "content_length": len(content_after_title),
                                               "preserved_summary_length": len(preserved_summary)})
                    return preserved_summary
                else:
                    # Just return the title if that's all we have
                    return title_with_markers
        
        # Fallback: return original text if no title patterns found
        LoggingHelper.info("No title preservation needed for Assistant response", 
                         extra_data={"project_id": project_id, "response_length": len(raw_text)})
        return raw_text
        
    except Exception as e:
        LoggingHelper.warning(f"Error in title preservation for Assistant response: {str(e)}", 
                            extra_data={"project_id": project_id})
        # Fallback to original text on any error
        return raw_text

# generate_title function moved to llm_utils.py
# check_restricted_keywords function moved to llm_utils.py
