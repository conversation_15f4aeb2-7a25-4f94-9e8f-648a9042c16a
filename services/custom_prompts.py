CUSTOM_PROMPTS = {
    # Finance & Accounting prompts
    "Finance & Accounting_1": "Analyze the financial performance of this company.",
    "Finance & Accounting_2": "Compare year-over-year financial ratios.",
    "Finance & Accounting_3": "Identify financial risks in this report.",
    "Finance & Accounting_4": "Summarize key insights from this balance sheet.",
    "Finance & Accounting_5": "Calculate profitability and cash flow trends.",
    "Finance & Accounting_6": "Evaluate the valuation of this business based on the data.",
    "Finance & Accounting_7": "Check for inconsistencies or errors in this financial statement.",
    "Finance & Accounting_8": "Provide insights on revenue vs. cost trends.",
    "Finance & Accounting_9": "Assess financial health using key ratios.",
    "Finance & Accounting_10": "Identify trends in operating expenses.",
    "Finance & Accounting_11": "Compare this company's performance with its competitors.",
    "Finance & Accounting_12": "Break down revenue streams and their contributions.",
    "Finance & Accounting_13": "Analyze net profit margin fluctuations.",
    "Finance & Accounting_14": "Detect potential fraud or irregularities in these financials.",
    "Finance & Accounting_15": "Summarize this investment portfolio's performance.",
    "Finance & Accounting_16": "Explain the impact of leverage on financial stability.",
    "Finance & Accounting_17": "Highlight areas for cost-cutting without affecting revenue.",
    "Finance & Accounting_18": "Perform variance analysis on budget vs. actuals.",
    "Finance & Accounting_19": "Identify working capital efficiency trends.",
    "Finance & Accounting_20": "Assess the impact of exchange rates on financial performance.",
    "Finance & Accounting_21": "Evaluate credit risk based on this report.",
    "Finance & Accounting_22": "Compare capital expenditure trends over the last five years.",
    "Finance & Accounting_23": "Break down dividend distribution patterns.",
    "Finance & Accounting_24": "Analyze break-even points for different scenarios.",
    "Finance & Accounting_25": "Summarize the key takeaways for investors from this report.",

    # Academic Research prompts
    "Academic Research_1": "Summarize this research paper in 300 words.",
    "Academic Research_2": "Identify the key findings and limitations of this study.",
    "Academic Research_3": "Rewrite this section in an academic style.",
    "Academic Research_4": "Extract all citations and references from this document.",
    "Academic Research_5": "Check for plagiarism or paraphrase detection.",
    "Academic Research_6": "Suggest improvements to the methodology section.",
    "Academic Research_7": "Generate an abstract based on this research paper.",
    "Academic Research_8": "Identify gaps in the literature review.",
    "Academic Research_9": "Translate this academic paper into simpler terms.",
    "Academic Research_10": "Format citations in APA/MLA/Chicago style etc..",
    "Academic Research_11": "Summarize the introduction and conclusion.",
    "Academic Research_12": "Compare this study with similar existing research.",
    "Academic Research_13": "Extract statistical results and summarize them.",
    "Academic Research_14": "Analyze the reliability and validity of the study.",
    "Academic Research_15": "Identify the theoretical framework used in this paper.",
    "Academic Research_16": "Check for grammatical errors and improve readability.",
    "Academic Research_17": "List key research questions addressed in this paper.",
    "Academic Research_18": "Explain the significance of this study.",
    "Academic Research_19": "Summarize the methodology used in this research.",
    "Academic Research_20": "Identify practical implications of the findings.",
    "Academic Research_21": "Compare different sections to check for logical consistency.",
    "Academic Research_22": "Generate discussion points based on the results.",
    "Academic Research_23": "Identify ethical considerations discussed in the paper.",
    "Academic Research_24": "Extract numerical data and create a summary table.",
    "Academic Research_25": "Highlight potential areas for further research.",

    # Legal & Compliance prompts
    "Legal & Compliance_1": "Summarize the key clauses of this contract.",
    "Legal & Compliance_2": "Highlight any legal risks or red flags in this document.",
    "Legal & Compliance_3": "Compare this policy with industry compliance standards.",
    "Legal & Compliance_4": "Explain the terms of this agreement in simple language.",
    "Legal & Compliance_5": "Check if this document complies with GDPR/SOX/ISO 27001, etc.",
    "Legal & Compliance_6": "Identify potential loopholes in this legal contract.",
    "Legal & Compliance_7": "Extract all obligations and responsibilities of both parties.",
    "Legal & Compliance_8": "Draft a summary report based on this compliance document.",
    "Legal & Compliance_9": "List all regulatory requirements mentioned in this file.",
    "Legal & Compliance_10": "Suggest modifications to align with best legal practices.",
    "Legal & Compliance_11": "Analyze the penalties or breach consequences in this contract.",
    "Legal & Compliance_12": "Check for missing mandatory clauses in this document.",
    "Legal & Compliance_13": "Summarize dispute resolution mechanisms in this agreement.",
    "Legal & Compliance_14": "Highlight confidentiality clauses and data protection measures.",
    "Legal & Compliance_15": "Compare this contract with previous versions for changes.",
    "Legal & Compliance_16": "Identify ambiguous wording that may cause legal issues.",
    "Legal & Compliance_17": "Summarize the rights and obligations of employees.",
    "Legal & Compliance_18": "Identify jurisdictional issues in this contract.",
    "Legal & Compliance_19": "List the key legal definitions used in this document.",
    "Legal & Compliance_20": "Analyze the enforceability of non-compete clauses.",
    "Legal & Compliance_21": "Extract key timelines and deadlines from this agreement.",
    "Legal & Compliance_22": "Assess the risk of liability based on contract terms.",
    "Legal & Compliance_23": "Highlight regulatory changes that may affect this document.",
    "Legal & Compliance_24": "Compare terms in multiple contracts for consistency.",
    "Legal & Compliance_25": "Generate a compliance checklist based on this policy.",

    # Medical & Healthcare prompts
    "Medical & Healthcare_1": "Summarize this medical case study.",
    "Medical & Healthcare_2": "Extract key findings from this clinical trial report.",
    "Medical & Healthcare_3": "Explain the medical terminology in this document.",
    "Medical & Healthcare_4": "Identify potential ethical concerns in this study.",
    "Medical & Healthcare_5": "Analyze trends in the patient data provided.",
    "Medical & Healthcare_6": "Compare this research with existing medical guidelines.",
    "Medical & Healthcare_7": "Summarize the side effects mentioned in this report.",
    "Medical & Healthcare_8": "Provide a risk assessment based on this patient data.",
    "Medical & Healthcare_9": "Extract key statistics from this healthcare report.",
    "Medical & Healthcare_10": "Highlight any inconsistencies in this medical study.",
    "Medical & Healthcare_11": "Compare treatment efficacy based on the data provided.",
    "Medical & Healthcare_12": "Identify potential drug interactions from this report.",
    "Medical & Healthcare_13": "Analyze patient demographics and outcomes.",
    "Medical & Healthcare_14": "Extract recommended dosage and side effect patterns.",
    "Medical & Healthcare_15": "Identify gaps in clinical evidence supporting this treatment.",
    "Medical & Healthcare_16": "Summarize key findings related to disease prevention.",
    "Medical & Healthcare_17": "Check for missing critical data in this medical report.",
    "Medical & Healthcare_18": "Highlight key recommendations for practitioners.",
    "Medical & Healthcare_19": "Assess the impact of this research on healthcare policies.",
    "Medical & Healthcare_20": "Compare results with similar medical studies.",
    "Medical & Healthcare_21": "Identify key biomarkers used in the study.",
    "Medical & Healthcare_22": "Summarize data trends in patient recovery rates.",
    "Medical & Healthcare_23": "List regulatory approvals needed for this study.",
    "Medical & Healthcare_24": "Highlight any experimental methods used in the research.",
    "Medical & Healthcare_25": "Analyze the limitations and biases in this study.",

    # Business & Strategy prompts
    "Business & Strategy_1": "Summarize this business plan in key points.",
    "Business & Strategy_2": "Identify strengths and weaknesses in this SWOT analysis.",
    "Business & Strategy_3": "Provide competitive insights based on this market report.",
    "Business & Strategy_4": "Highlight key growth opportunities in this strategy document.",
    "Business & Strategy_5": "Extract financial projections from this business plan.",
    "Business & Strategy_6": "Summarize this feasibility study into a short report.",
    "Business & Strategy_7": "Compare this business strategy with industry best practices.",
    "Business & Strategy_8": "Analyze potential risks mentioned in this strategy report.",
    "Business & Strategy_9": "Identify customer pain points in this market analysis.",
    "Business & Strategy_10": "Suggest improvements based on this business case.",
    "Business & Strategy_11": "Break down revenue models and profitability factors.",
    "Business & Strategy_12": "Assess scalability potential in this business strategy.",
    "Business & Strategy_13": "Identify potential strategic partnerships based on this report.",
    "Business & Strategy_14": "Compare different revenue models outlined in the document.",
    "Business & Strategy_15": "Evaluate the financial sustainability of this business proposal.",
    "Business & Strategy_16": "Analyze market positioning based on competitor analysis.",
    "Business & Strategy_17": "Identify industry trends from this market research.",
    "Business & Strategy_18": "Extract key customer segmentation insights.",
    "Business & Strategy_19": "Highlight strategic initiatives for future business expansion.",
    "Business & Strategy_20": "Summarize funding requirements and potential investor insights.",

    # Technology & Software Development prompts
    "Technology & Software Development_1": "Summarize the key functionalities of this software.",
    "Technology & Software Development_2": "Identify potential security risks in this code.",
    "Technology & Software Development_3": "Optimize this code for better efficiency.",
    "Technology & Software Development_4": "Explain the logic behind this algorithm.",
    "Technology & Software Development_5": "Compare different programming approaches used in this project.",
    "Technology & Software Development_6": "Suggest improvements for system architecture.",
    "Technology & Software Development_7": "Identify bugs and potential fixes in this code.",
    "Technology & Software Development_8": "Assess software scalability and performance limitations.",
    "Technology & Software Development_9": "Summarize this API documentation for easier understanding.",
    "Technology & Software Development_10": "Evaluate code maintainability and best practices compliance.",
    "Technology & Software Development_11": "Identify integration points with other software tools.",
    "Technology & Software Development_12": "Compare this software framework with industry standards.",
    "Technology & Software Development_13": "Assess usability and user experience improvements.",
    "Technology & Software Development_14": "Identify redundant or unnecessary code segments.",
    "Technology & Software Development_15": "Generate automated testing scenarios for this application.",
    "Technology & Software Development_16": "Extract system requirements and dependencies.",
    "Technology & Software Development_17": "Highlight cybersecurity measures implemented in this project.",
    "Technology & Software Development_18": "Summarize the key objectives of this software development report.",
    "Technology & Software Development_19": "Evaluate database efficiency and suggest optimization techniques.",
    "Technology & Software Development_20": "Identify potential scalability issues in this cloud architecture.",

    # Engineering & Technical Reports prompts
    "Engineering & Technical Reports_1": "Summarize key findings in this technical report.",
    "Engineering & Technical Reports_2": "Identify potential risks in this engineering project.",
    "Engineering & Technical Reports_3": "Compare this technical design with industry standards.",
    "Engineering & Technical Reports_4": "Extract material specifications from this document.",
    "Engineering & Technical Reports_5": "Explain the calculations used in this report.",
    "Engineering & Technical Reports_6": "Check for missing elements in this technical analysis.",
    "Engineering & Technical Reports_7": "Highlight key safety measures mentioned in this report.",
    "Engineering & Technical Reports_8": "Summarize performance metrics from this test report.",
    "Engineering & Technical Reports_9": "Analyze failure points in this engineering document.",
    "Engineering & Technical Reports_10": "Suggest improvements for this technical proposal.",
    "Engineering & Technical Reports_11": "Extract key regulatory compliance details from this document.",
    "Engineering & Technical Reports_12": "Compare different methodologies used in this project.",
    "Engineering & Technical Reports_13": "Identify cost-saving opportunities in this engineering plan.",
    "Engineering & Technical Reports_14": "Summarize environmental impact considerations.",
    "Engineering & Technical Reports_15": "Analyze system efficiency and suggest optimizations.",
    "Engineering & Technical Reports_16": "Identify potential design flaws in this blueprint.",
    "Engineering & Technical Reports_17": "Evaluate feasibility based on technical constraints.",
    "Engineering & Technical Reports_18": "Summarize test results and quality control findings.",
    "Engineering & Technical Reports_19": "Extract key structural and load-bearing data.",
    "Engineering & Technical Reports_20": "Highlight critical components in this engineering process.",

    # Marketing & Data Analytics prompts
    "Marketing & Data Analytics_1": "Summarize key insights from this marketing report.",
    "Marketing & Data Analytics_2": "Analyze trends in this customer data file.",
    "Marketing & Data Analytics_3": "Identify top-performing campaigns from this dataset.",
    "Marketing & Data Analytics_4": "Compare the effectiveness of different marketing strategies.",
    "Marketing & Data Analytics_5": "Extract customer pain points from this survey data.",
    "Marketing & Data Analytics_6": "Summarize this SEO performance report.",
    "Marketing & Data Analytics_7": "Identify high-value customer segments in this dataset.",
    "Marketing & Data Analytics_8": "Generate a competitor analysis from this market research.",
    "Marketing & Data Analytics_9": "Check for inconsistencies in this social media analytics file.",
    "Marketing & Data Analytics_10": "Suggest improvements based on this campaign performance report.",
    "Marketing & Data Analytics_11": "Analyze engagement metrics from this digital marketing report.",
    "Marketing & Data Analytics_12": "Compare conversion rates for different customer segments.",
    "Marketing & Data Analytics_13": "Identify trends in consumer behavior over time.",
    "Marketing & Data Analytics_14": "Summarize brand perception insights from this data.",
    "Marketing & Data Analytics_15": "Highlight key areas for marketing budget allocation.",
    "Marketing & Data Analytics_16": "Extract influencer marketing effectiveness metrics.",
    "Marketing & Data Analytics_17": "Analyze sales funnel performance from this dataset.",
    "Marketing & Data Analytics_18": "Identify seasonal trends in this sales data.",
    "Marketing & Data Analytics_19": "Summarize content marketing impact based on engagement.",
    "Marketing & Data Analytics_20": "Compare click-through rates across different advertising channels.",

    # Human Resources & Management prompts
    "Human Resources & Management_1": "Summarize key HR policies in this document.",
    "Human Resources & Management_2": "Analyze employee performance trends in this report.",
    "Human Resources & Management_3": "Extract key skills required for this job role.",
    "Human Resources & Management_4": "Compare this HR policy with industry standards.",
    "Human Resources & Management_5": "Identify gaps in this training and development plan.",
    "Human Resources & Management_6": "Summarize employee engagement survey results.",
    "Human Resources & Management_7": "Highlight potential compliance issues in this document.",
    "Human Resources & Management_8": "Generate a summary of exit interview feedback.",
    "Human Resources & Management_9": "Analyze workforce diversity data from this report.",
    "Human Resources & Management_10": "Suggest improvements for this recruitment strategy.",
    "Human Resources & Management_11": "Compare retention rates across different departments.",
    "Human Resources & Management_12": "Identify leadership development needs based on this data.",
    "Human Resources & Management_13": "Summarize employee satisfaction trends over time.",
    "Human Resources & Management_14": "Extract key points from this compensation analysis.",
    "Human Resources & Management_15": "Analyze workplace productivity trends.",
    "Human Resources & Management_16": "Identify high-turnover risk factors in this report.",
    "Human Resources & Management_17": "Compare employee benefits packages with industry benchmarks.",
    "Human Resources & Management_18": "Summarize key takeaways from this performance review dataset.",
    "Human Resources & Management_19": "Assess effectiveness of remote work policies.",
    "Human Resources & Management_20": "Identify skills gaps for succession planning.",

    # Government & Public Policy prompts
    "Government & Public Policy_1": "Summarize this policy document in simple terms.",
    "Government & Public Policy_2": "Identify key economic impacts in this report.",
    "Government & Public Policy_3": "Compare this policy with international regulations.",
    "Government & Public Policy_4": "Highlight the main objectives of this government plan.",
    "Government & Public Policy_5": "Analyze public sentiment based on this survey data.",
    "Government & Public Policy_6": "Extract key recommendations from this policy paper.",
    "Government & Public Policy_7": "Identify potential risks in this legislative proposal.",
    "Government & Public Policy_8": "Summarize this economic development report.",
    "Government & Public Policy_9": "Analyze funding allocations in this budget report.",
    "Government & Public Policy_10": "Compare this policy with previous versions for changes.",
    "Government & Public Policy_11": "Identify social impact measures in this public policy report.",
    "Government & Public Policy_12": "Extract regulatory compliance requirements from this document.",
    "Government & Public Policy_13": "Summarize key trends in government expenditure.",
    "Government & Public Policy_14": "Highlight effectiveness metrics for this government initiative.",
    "Government & Public Policy_15": "Analyze gaps in infrastructure planning based on this report.",
    "Government & Public Policy_16": "Identify policy loopholes and areas for improvement.",
    "Government & Public Policy_17": "Summarize key legislative priorities for this fiscal year.",
    "Government & Public Policy_18": "Compare effectiveness of different policy approaches.",
    "Government & Public Policy_19": "Highlight environmental impact considerations in this report.",
    "Government & Public Policy_20": "Identify long-term economic forecasts based on this policy analysis.",

    # Featured

   "Featured_1": 'Summarize the document',
   "Featured_2": 'Extract the main points and key takeaways',
   "Featured_3": 'Analyze the data in this file and provide key insights',
   "Featured_4": 'Search for specific information in the document',
   "Featured_5": 'Extract the key points and main takeaways',
   "Featured_6": 'Summarize the document',
   "Featured_7": 'Analyze the data in this file and provide key insights',
   "Featured_8": 'Search for specific information in the document',
   "Featured_9": 'Analyze the data in this file and provide key insights',
    "Featured_10": 'Summarize the document',
    "Featured_11": 'Extract the main points and key takeaways',
    "Featured_12": 'Search for specific information in the document',
    "Featured_13": 'Search for specific information in the document',
    "Featured_14": 'Summarize the document',
    "Featured_15": 'Extract the main points and key takeaways',
    "Featured_16": 'Analyze the data in this file and provide key insights'
}




# Helper function to get prompt by category and ID
def get_prompt(category: str, prompt_id: int) -> str:
    """
    Get a specific prompt by category and ID.
    
    Args:
        category (str): The category name (e.g., "Finance & Accounting")
        prompt_id (int): The prompt ID number (1-25)
        
    Returns:
        str: The prompt text or None if not found
    """
    key = f"{category}_{prompt_id}"
    return CUSTOM_PROMPTS.get(key)

# Helper function to get all prompts in a category
def get_category_prompts(category: str) -> dict:
    """
    Get all prompts in a specific category.
    
    Args:
        category (str): The category name (e.g., "Finance & Accounting")
        
    Returns:
        dict: Dictionary of prompt IDs and their texts for the category
    """
    return {k: v for k, v in CUSTOM_PROMPTS.items() if k.startswith(category)}

# Helper function to get all categories
def get_categories() -> list:
    """
    Get list of all unique categories.
    
    Returns:
        list: List of category names
    """
    return list(set(k.split('_')[0] for k in CUSTOM_PROMPTS.keys()))

# Add this new function
def get_prompt_by_name(custom_prompt_name: str) -> str:
    """
    Get a prompt by its full name (category_id format).
    
    Args:
        custom_prompt_name (str): The full prompt name (e.g., "Government & Public Policy_16")
        
    Returns:
        str: The prompt text or None if not found
    """
    return CUSTOM_PROMPTS.get(custom_prompt_name) 