import logging
from datetime import datetime, timezone
from fastapi import HTTP<PERSON>x<PERSON>
from jose import jwt
import config

# Note: Cannot import Logging<PERSON>elper here due to circular import with logging_middleware
# Using standard logger for now - this will still be captured by the root logger system
logger = logging.getLogger(__name__)

# Get JWT secret key from config
JWT_SECRET_KEY = config.JWT_SECRET_KEY

# Functions to be moved here:

def decode_jwt_token(token: str) -> dict:
    """
    Decode a JWT token and return its payload.

    Args:
        token (str): The JWT token string

    Returns:
        dict: The decoded token payload

    Raises:
        HTTPException: If token is invalid or expired
    """


    # Step 1: Decode token first
    try:
        payload = jwt.decode(token, key=JWT_SECRET_KEY, algorithms=["HS256"], options={"verify_signature": False})
        logger.info("Successfully decoded JWT token payload")
    except jwt.JWTError as e:
        logger.error(f"JWT Error: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")

    # Step 2: Check token expiration
    if 'exp' in payload:
        current_timestamp = datetime.now(timezone.utc).timestamp()
        if current_timestamp > payload['exp']:
            logger.error("Token has expired")
            raise HTTPException(status_code=401, detail="Token has expired")

    # Step 3: Return valid payload
    return payload
