import logging
import asyncio
from duckduckgo_search import DDGS
from langchain_community.utilities import GoogleSerperAPIWrapper
import config
from datetime import datetime, timezone
from services import prompts
from services.llm_utils import call_llm
from services.logging_utils import LoggingHelper
from services.unified_usage_tracking import UnifiedUsageLogger

logger = logging.getLogger(__name__)

# Configuration variables
gsearch = GoogleSerperAPIWrapper(serper_api_key=config.serper_api_key)

# Functions to be moved here:

async def perform_search(query, search_logger: UnifiedUsageLogger = None, unified_logger: UnifiedUsageLogger = None, user_id: str = None, project_id: str = None):
    """
    Perform an asynchronous search using Google Serper API.

    Args:
        query (str): The search query.
        search_logger (UnifiedUsageLogger, optional): Logger for tracking search consumption (legacy parameter).
        unified_logger (UnifiedUsageLogger, optional): Unified logger for tracking search consumption.
        user_id (str, optional): User ID for logging.
        project_id (str, optional): Project ID for logging.

    Returns:
        str: The primary result of the search.
    """
    try:
        # Fetch the primary result using asyncio.to_thread to avoid blocking
        result = await asyncio.to_thread(gsearch.run, query)

        # Log search consumption using unified logger (prioritize unified_logger, fallback to search_logger)
        logger_to_use = unified_logger or search_logger
        if logger_to_use:
            logger_to_use.log_search_usage(
                service_provider="serper",
                query=query,
                response_text=result,
                operation_subtype="serper_api_search",
                operation_details={
                    "service": "serper_api",
                    "query_length": len(query),
                    "response_length": len(result) if result else 0
                },
                success=True
            )

        return result
    except Exception as e:
        # Log error using unified logger (prioritize unified_logger, fallback to search_logger)
        logger_to_use = unified_logger or search_logger
        if logger_to_use:
            logger_to_use.log_search_usage(
                service_provider="serper",
                query=query,
                response_text="",
                operation_subtype="serper_api_search",
                operation_details={
                    "service": "serper_api",
                    "query_length": len(query),
                    "error": str(e)
                },
                success=False,
                error_message=str(e)
            )

        # Fallback logging
        LoggingHelper.error(f"Error in Serper search: {str(e)}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "query": query[:100]})
        return ""

async def duckduckgo_query(query, search_logger: UnifiedUsageLogger = None, unified_logger: UnifiedUsageLogger = None, user_id: str = None, project_id: str = None):
    """
    Perform an asynchronous DuckDuckGo search.

    Args:
        query (str): The search query.
        search_logger (UnifiedUsageLogger, optional): Logger for tracking search consumption (legacy parameter).
        unified_logger (UnifiedUsageLogger, optional): Unified logger for tracking search consumption.
        user_id (str, optional): User ID for logging.
        project_id (str, optional): Project ID for logging.

    Returns:
        str: Combined search results.
    """
    try:
        response = False

        def _search():
            with DDGS() as ddgs:
                results = ddgs.text(query)
                response_text = ""
                for result in results:
                    response_text += result['body']
                return response_text

        # Run the search in a thread pool to avoid blocking
        response = await asyncio.to_thread(_search)

        # Log search consumption using unified logger (prioritize unified_logger, fallback to search_logger)
        logger_to_use = unified_logger or search_logger
        if logger_to_use:
            logger_to_use.log_search_usage(
                service_provider="duckduckgo",
                query=query,
                response_text=response,
                operation_subtype="duckduckgo_search",
                operation_details={
                    "service": "duckduckgo",
                    "query_length": len(query),
                    "response_length": len(response) if response else 0
                },
                success=True
            )

        return response
    except Exception as e:
        # Log error using unified logger (prioritize unified_logger, fallback to search_logger)
        logger_to_use = unified_logger or search_logger
        if logger_to_use:
            logger_to_use.log_search_usage(
                service_provider="duckduckgo",
                query=query,
                response_text="",
                operation_subtype="duckduckgo_search",
                operation_details={
                    "service": "duckduckgo",
                    "query_length": len(query),
                    "error": str(e)
                },
                success=False,
                error_message=str(e)
            )

        # Fallback logging
        LoggingHelper.error(f"Error in DuckDuckGo search: {str(e)}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "query": query[:100]})
        return response if response else ""

async def get_source(chat_history,user_query,filesearch_descriptions,dataset_descriptions,project_id,mongo_manager,token_logger=None,unified_logger=None):
    current_date = datetime.now(timezone.utc).date()
    prompt=prompts.get_source_to_search(chat_history,user_query,filesearch_descriptions,dataset_descriptions,current_date)
    # print("final prompt first 100 characters   - ", prompt[:100])
    response=await call_llm(prompt,project_id,mongo_manager,token_logger,unified_logger)
    return response