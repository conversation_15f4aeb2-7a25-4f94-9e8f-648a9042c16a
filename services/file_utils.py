import os
import pickle
import pandas as pd
import logging
import re
import urllib.parse
import tempfile
import numpy as np
import base64
import uuid
import asyncio
from typing import Optional, List
from fastapi import UploadFile, HTTPException
import shutil
from io import BytesIO
from PyPDF2 import Pdf<PERSON><PERSON>er
from docx import Document as DocxDocument
import config
import aiofiles
from exceptions.api_exceptions import handle_llm_error
from services.async_fs_utils import async_makedirs, async_remove, async_path_exists
from services.logging_utils import LoggingHelper
from starlette.concurrency import run_in_threadpool

logger = logging.getLogger(__name__)

# Configuration variables
client = config.openai_client

# Functions to be moved here:

async def put_pickle_file_context(filePath, context):
    """Save context to a pickle file. Directory must already exist."""
    LoggingHelper.info("Saving context to: {filePath}", logger_name="file_utils")

    # Directory should already exist, just verify
    directory = os.path.dirname(filePath)
    directory_exists = await async_path_exists(directory)
    if not directory_exists:
        LoggingHelper.error("Directory '{directory}' does not exist. This should have been created earlier.", logger_name="file_utils")
        raise ValueError(f"Directory {directory} does not exist")

    # Wrap pickle.dumps in run_in_threadpool to avoid blocking
    def _pickle_dumps():
        return pickle.dumps(context)

    pickled_data = await run_in_threadpool(_pickle_dumps)

    # Save or update the file
    async with aiofiles.open(filePath, 'wb') as f:
        await f.write(pickled_data)
    LoggingHelper.info("Context saved to '{filePath}' successfully.", logger_name="file_utils")


async def get_pickle_file_context(filePath, valid_page_numbers):
    LoggingHelper.info("Attempting to load content from: {filePath}", logger_name="file_utils")

    try:
        file_exists = await async_path_exists(filePath)
        if not file_exists:
            LoggingHelper.error("Pickle file not found: {filePath}", logger_name="file_utils")
            return None

        async with aiofiles.open(filePath, 'rb') as f:
            data_bytes = await f.read()

        # Wrap pickle.loads in run_in_threadpool to avoid blocking
        def _pickle_loads():
            return pickle.loads(data_bytes)

        data_from_pickle = await run_in_threadpool(_pickle_loads)

        retrieved_texts = data_from_pickle.get('texts', [])
        retrieved_total_pages = data_from_pickle.get('total_pages', 0)

        if not retrieved_texts or not retrieved_total_pages:
            LoggingHelper.error("Invalid pickle file format: missing texts or total_pages", logger_name="file_utils")
            return None

        page_texts = []
        for page_num in valid_page_numbers:
            if 1 <= page_num <= retrieved_total_pages:
                page_texts.append(f"Page {page_num}: {retrieved_texts[page_num - 1]}")
            else:
                LoggingHelper.warning("Invalid page number requested: {page_num}", logger_name="file_utils")
                page_texts.append(f"Page {page_num}: Invalid page number")

        return page_texts

    except Exception as e:
        LoggingHelper.error("Error reading pickle file: {e}", logger_name="file_utils")
        return None


async def put_numpy_file_context(filePath, context):
    # Use async version of os.path.exists
    file_exists = await async_path_exists(filePath)
    if file_exists:
        LoggingHelper.info("File '{filePath}' already exists. skipping the saving operation...", logger_name="file_utils")
        # Load the existing NumPy array
        # loaded_text_array = np.load(filePath)
    else:
        text_array = np.array(context, dtype=object)

        # Save the NumPy array to a file
        # np.save doesn't have a direct async equivalent, so we'll use a workaround
        np_bytes = BytesIO()
        np.save(np_bytes, text_array)
        np_bytes.seek(0)

        async with aiofiles.open(filePath, 'wb') as f:
            await f.write(np_bytes.read())


async def get_numpy_file_context(filePath):
    # Use async version of os.path.exists
    file_exists = await async_path_exists(filePath)
    if file_exists:
        LoggingHelper.info("File '{filePath}' file exists. Loading the content...", logger_name="file_utils")
        # Load the existing NumPy array
        async with aiofiles.open(filePath, 'rb') as f:
            data = await f.read()
            loaded_text_array = np.load(BytesIO(data), allow_pickle=True)
        return loaded_text_array
    return None


async def read_doc_file(content: bytes) -> str:
    """Read text from a .docx file."""
    print("YOU ARE IN TEXTRACT")
    # Create a temporary file path using async operation
    def _get_temp_dir():
        return tempfile.gettempdir()

    temp_dir = await asyncio.to_thread(_get_temp_dir)
    temp_file_path = os.path.join(temp_dir, f"temp_doc_{uuid.uuid4()}.docx")

    try:
        # Write content to temporary file asynchronously
        async with aiofiles.open(temp_file_path, "wb") as temp_file:
            await temp_file.write(content)

        # Process the document asynchronously using asyncio.to_thread
        def _process_doc():
            doc = DocxDocument(temp_file_path)
            return "\n".join([para.text for para in doc.paragraphs])

        text = await asyncio.to_thread(_process_doc)
        return text
    finally:
        # Clean up the temporary file
        await async_remove(temp_file_path)


async def read_txt_file(content: bytes) -> str:
    """Directly read .txt content."""
    return content.decode("utf-8")


async def read_data_file(file_path: str) -> str:
    """Read content from CSV or Excel files"""
    ext = file_path.split(".")[-1].lower()

    try:
        if ext in config.DATA_ANALYSIS_FILE_TYPES:
            # Read file content asynchronously first
            async with aiofiles.open(file_path, 'rb') as f:
                file_content = await f.read()

            # Wrap pandas operations in run_in_threadpool to avoid blocking
            def _process_data():
                import pandas as pd
                if ext == "csv":
                    df = pd.read_csv(BytesIO(file_content))
                else:  # xlsx
                    df = pd.read_excel(BytesIO(file_content))
                return df.to_string()

            return await run_in_threadpool(_process_data)
        else:
            raise ValueError(f"Unsupported file type: {ext}")
    except Exception as e:
        LoggingHelper.error("Error reading data file {file_path}: {e}", logger_name="file_utils")
        return ""


async def read_pickle_file_for_compliance(pkl_file_path: str) -> str:
    """Read content from pickle file without page filtering"""
    try:
        async with aiofiles.open(pkl_file_path, 'rb') as f:
            data = await f.read()

        # Wrap pickle.loads in run_in_threadpool to avoid blocking
        def _pickle_loads():
            return pickle.loads(data)

        content = await run_in_threadpool(_pickle_loads)

        if isinstance(content, list):
            return "\n".join(content)
        return str(content)
    except Exception as e:
        LoggingHelper.error("Error reading pickle file {pkl_file_path}: {e}", logger_name="file_utils")
        return ""


def extract_filetype(path):
    # Split the path by '/' and take the last part
    filename = path.split('/')[-1]
    # Split the filename by '.' and take the last part
    return filename.split('.')[-1] if '.' in filename else ''


async def download_file(files_data):
    try:
        file_ids = await get_file_ids()
        for file_data in files_data:
            if file_data['file_id'] not in file_ids:
                logging.error(f"No file found with ID: {file_data['file_id']}")
                continue

            # Wrap synchronous OpenAI call in run_in_threadpool
            def _sync_openai_call():
                file_content = client.files.content(file_data['file_id'])
                return file_content.read()

            file_content_bytes = await run_in_threadpool(_sync_openai_call)

            # Create directory if it doesn't exist
            directory = os.path.dirname(file_data["file_name"])
            if directory:
                await async_makedirs(directory, exist_ok=True)

            # Write the file, replacing if it already exists
            async with aiofiles.open(file_data["file_name"], "wb") as file:
                await file.write(file_content_bytes)

            logging.info(f"File downloaded and saved: {file_data['file_name']}")
    except Exception as e:
        handle_llm_error(e)


async def upload_files(file_path):
    try:
        uploaded_files_data = []

        file_name = os.path.basename(file_path)

        # Read file content asynchronously
        async with aiofiles.open(file_path, "rb") as f:
            file_content = await f.read()

        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            return client.files.create(
                file=file_content, purpose="assistants"
            )

        message_file = await run_in_threadpool(_sync_openai_call)

        uploaded_files_data.append({
            "file_id": message_file.id,
            "file_name": file_name
        })
        # print(f"Uploaded file: {file_name}")

        return uploaded_files_data
    except Exception as e:
        handle_llm_error(e)


async def get_file_ids():
    # Extracting IDs
    try:
        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            return [file_obj.id for file_obj in client.files.list()]

        file_ids = await run_in_threadpool(_sync_openai_call)
        return file_ids
    except Exception as e:
        handle_llm_error(e)

async def get_user_file_ids(messages):
    user_file_ids=[]

    for msg in messages:
        if msg.role=="user":
            for attachment in msg.attachments:
                user_file_ids.append(attachment.file_id)

    return user_file_ids


async def get_chart_bytes(file_id):
    try:
        file_ids = await get_file_ids()
        if file_id not in file_ids:
            # print(f"No file found with id {file_id}")
            return
        else:
            # Wrap synchronous OpenAI call in run_in_threadpool
            def _sync_openai_call():
                file_content = client.files.content(file_id)
                return file_content.read()

            file_content_bytes = await run_in_threadpool(_sync_openai_call)

            return base64.b64encode(file_content_bytes).decode('utf-8')
    except Exception as e:
        handle_llm_error(e)


async def delete_all_files():
    # Extracting IDs
    try:
        file_ids = await get_file_ids()
        for id in file_ids:
            # Wrap synchronous OpenAI call in run_in_threadpool
            def _sync_openai_call():
                return client.files.delete(id)

            await run_in_threadpool(_sync_openai_call)
            # print(f"Deleted file with id {id}")
    except Exception as e:
        handle_llm_error(e)

def get_file_ids_from_file_details(file_details, filenames):
    # Convert string to list if item is a string
    filenames = [filenames] if isinstance(filenames, str) else filenames
    file_ids = []

    for filename in filenames:
        for file_detail in file_details:
            if file_detail.get("file_name") == filename:
                file_ids.append(file_detail.get("file_id"))
                break
    return file_ids

def get_filenames_from_response(data):
    """
    Extract filenames from the 'files' list in the given data dictionary.
    """
    filenames = []
    if 'files' in data and isinstance(data['files'], list):
        for file_info in data['files']:
            if 'file_name' in file_info:
                filenames.append(f"/{file_info['file_name']}")
    return filenames


def extract_sandbox_links(response: str) -> list:
    """
    Extracts all sandbox links from the given response string.

    Args:
    response (str): The response string containing sandbox links.

    Returns:
    list: A list of sandbox paths.
    """
    # Regular expression pattern to match sandbox links
    pattern = r'\[.*?\]\(sandbox:(.*?)\)'

    # Find all matches in the response string
    matches = re.findall(pattern, response)

    # Return the list of sandbox paths
    return matches

def create_file_mapping(sandbox_links: list, filenames: list) -> dict:
    """
    Creates a mapping between sandbox links and filenames based on matching filenames.

    Args:
    sandbox_links (list): A list of sandbox links.
    filenames (list): A list of filenames.

    Returns:
    dict: A dictionary mapping sandbox links to filenames.
    """
    mapping = {}

    for link in sandbox_links:
        link_filename = os.path.basename(link)
        for filename in filenames:
            if link_filename == os.path.basename(filename):
                mapping[link] = filename
                break

    return mapping

def replace_sandbox_links(response: str, file_mapping: dict) -> str:
    """
    Replace sandbox links in the response with actual API download links based on the file mapping.

    Args:
        response (str): The original response containing sandbox links
        file_mapping (dict): Dictionary mapping sandbox paths to actual filenames

    Returns:
        str: Response with replaced links
    """
    def replace_link(match):
        full_link = match.group(0)
        link_text = match.group(1)
        sandbox_path = match.group(2)

        if sandbox_path in file_mapping:
            file_name = file_mapping[sandbox_path]
            # URL encode the file name to handle special characters
            encoded_file_name = urllib.parse.quote(file_name)
            # Use the correct base URL and path structure
            download_url = f"/chat/download-file{encoded_file_name} "
            return f"[{link_text}]({download_url})"
        return full_link

    pattern = r'\[(.*?)\]\(sandbox:(.*?)\)'
    return re.sub(pattern, replace_link, response)
