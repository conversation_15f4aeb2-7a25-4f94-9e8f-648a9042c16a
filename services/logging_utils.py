"""
Utility functions for project+user logging system.

This module provides helper functions for easy migration from existing logging
to the new project+user specific logging system, along with utilities for
log management and analysis.
"""

import logging
import os
import json
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from datetime import datetime, timezone
import asyncio
import aiofiles

from services.project_user_logger import get_project_user_logger, log_structured
from services.logging_middleware import (
    get_current_user_id, 
    get_current_project_id, 
    get_current_logger,
    log_current_context
)
import config


class LoggingHelper:
    """Helper class for easy migration to project+user logging."""
    
    @staticmethod
    def get_logger(user_id: str = None, project_id: str = None, logger_name: str = None) -> logging.Logger:
        """
        Get appropriate logger based on available context.

        Args:
            user_id: Optional user ID (uses current context if not provided)
            project_id: Optional project ID (uses current context if not provided)
            logger_name: Optional logger name

        Returns:
            Project+user specific logger or fallback logger
        """
        # Use provided IDs or fall back to current context
        user_id = user_id or get_current_user_id()
        project_id = project_id or get_current_project_id()

        if user_id and project_id and config.PROJECT_USER_LOGGING_ENABLED:
            return get_project_user_logger(user_id, project_id, logger_name)
        else:
            # If root logger is disabled, use a default user/project for system logs
            if getattr(config, 'DISABLE_ROOT_LOGGER', False):
                return get_project_user_logger("system", "general", logger_name or "fallback")
            else:
                return logging.getLogger(logger_name or __name__)
    
    @staticmethod
    def log(level: str, message: str, user_id: str = None, project_id: str = None,
            extra_data: Dict[str, Any] = None, logger_name: str = None):
        """
        Log message with automatic context detection and improved formatting.

        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            message: Log message
            user_id: Optional user ID (uses current context if not provided)
            project_id: Optional project ID (uses current context if not provided)
            extra_data: Additional structured data
            logger_name: Optional logger name
        """
        # Use provided IDs or fall back to current context
        user_id = user_id or get_current_user_id()
        project_id = project_id or get_current_project_id()

        if user_id and project_id and config.PROJECT_USER_LOGGING_ENABLED:
            log_structured(user_id, project_id, level, message, extra_data, logger_name)
        else:
            # If root logger is disabled, use system/general for fallback logs
            if getattr(config, 'DISABLE_ROOT_LOGGER', False):
                log_structured("system", "general", level, message, extra_data, logger_name or "fallback")
            else:
                logger = logging.getLogger(logger_name or __name__)
                log_level = getattr(logging, level.upper(), logging.INFO)

                # Format message with extra data if present
                formatted_message = message
                if extra_data and getattr(config, 'SIMPLIFIED_JSON_STRUCTURE', True):
                    extra_parts = []
                    for key, value in extra_data.items():
                        str_value = str(value)
                        if len(str_value) > 100:
                            str_value = str_value[:97] + "..."
                        extra_parts.append(f"{key}: {str_value}")

                    if extra_parts:
                        formatted_message += f"\n  -> {', '.join(extra_parts)}"

                logger.log(log_level, formatted_message)

                # Add spacing if enabled
                if getattr(config, 'READABLE_LOG_SPACING', True) and log_level >= logging.INFO:
                    logger.log(log_level, "")
    
    @staticmethod
    def info(message: str, user_id: str = None, project_id: str = None,
             extra_data: Dict[str, Any] = None, logger_name: str = None):
        """Log INFO level message."""
        LoggingHelper.log("INFO", message, user_id, project_id, extra_data, logger_name)
    
    @staticmethod
    def debug(message: str, user_id: str = None, project_id: str = None,
              extra_data: Dict[str, Any] = None, logger_name: str = None):
        """Log DEBUG level message."""
        LoggingHelper.log("DEBUG", message, user_id, project_id, extra_data, logger_name)
    
    @staticmethod
    def warning(message: str, user_id: str = None, project_id: str = None,
                extra_data: Dict[str, Any] = None, logger_name: str = None):
        """Log WARNING level message."""
        LoggingHelper.log("WARNING", message, user_id, project_id, extra_data, logger_name)
    
    @staticmethod
    def error(message: str, user_id: str = None, project_id: str = None,
              extra_data: Dict[str, Any] = None, logger_name: str = None):
        """Log ERROR level message."""
        LoggingHelper.log("ERROR", message, user_id, project_id, extra_data, logger_name)
    
    @staticmethod
    def critical(message: str, user_id: str = None, project_id: str = None,
                 extra_data: Dict[str, Any] = None, logger_name: str = None):
        """Log CRITICAL level message."""
        LoggingHelper.log("CRITICAL", message, user_id, project_id, extra_data, logger_name)


class LogAnalyzer:
    """Utility class for analyzing project+user logs."""
    
    def __init__(self):
        self.base_dir = Path(config.PROJECT_USER_LOG_BASE_DIR)
    
    def get_user_projects(self, user_id: str) -> List[str]:
        """Get list of projects for a specific user."""
        user_dir = self.base_dir / user_id / "projects"
        if not user_dir.exists():
            return []
        
        return [p.name for p in user_dir.iterdir() if p.is_dir()]
    
    def get_all_users(self) -> List[str]:
        """Get list of all users with logs."""
        if not self.base_dir.exists():
            return []
        
        return [u.name for u in self.base_dir.iterdir() if u.is_dir()]
    
    def get_log_files(self, user_id: str, project_id: str) -> List[Path]:
        """Get all log files for a specific user+project."""
        project_dir = self.base_dir / user_id / "projects" / project_id
        if not project_dir.exists():
            return []
        
        return list(project_dir.glob("*.log*"))
    
    async def read_recent_logs(self, user_id: str, project_id: str, 
                              lines: int = 100) -> List[str]:
        """Read recent log entries for a user+project."""
        log_files = self.get_log_files(user_id, project_id)
        if not log_files:
            return []
        
        # Sort by modification time, newest first
        log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        recent_lines = []
        for log_file in log_files:
            try:
                async with aiofiles.open(log_file, 'r') as f:
                    content = await f.read()
                    file_lines = content.strip().split('\n')
                    recent_lines.extend(file_lines)
                    
                    if len(recent_lines) >= lines:
                        break
            except Exception as e:
                logging.getLogger(__name__).warning(f"Error reading log file {log_file}: {e}")
        
        return recent_lines[-lines:] if recent_lines else []
    
    async def search_logs(self, user_id: str, project_id: str, 
                         search_term: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """Search for specific terms in user+project logs."""
        log_files = self.get_log_files(user_id, project_id)
        if not log_files:
            return []
        
        results = []
        for log_file in log_files:
            try:
                async with aiofiles.open(log_file, 'r') as f:
                    content = await f.read()
                    lines = content.strip().split('\n')
                    
                    for line_num, line in enumerate(lines, 1):
                        if search_term.lower() in line.lower():
                            results.append({
                                "file": str(log_file),
                                "line_number": line_num,
                                "content": line.strip(),
                                "timestamp": self._extract_timestamp(line)
                            })
                            
                            if len(results) >= max_results:
                                return results
            except Exception as e:
                # Use LoggingHelper for consistent logging
                LoggingHelper.warning(f"Error searching log file {log_file}: {e}", logger_name="log_analyzer")
        
        return results
    
    def _extract_timestamp(self, log_line: str) -> Optional[str]:
        """Extract timestamp from log line."""
        try:
            # Assuming standard format: "YYYY-MM-DD HH:MM:SS,mmm - ..."
            if " - " in log_line:
                timestamp_part = log_line.split(" - ")[0]
                return timestamp_part
        except Exception:
            pass
        return None
    
    async def get_log_statistics(self, user_id: str, project_id: str) -> Dict[str, Any]:
        """Get statistics for user+project logs."""
        log_files = self.get_log_files(user_id, project_id)
        if not log_files:
            return {"error": "No log files found"}
        
        stats = {
            "total_files": len(log_files),
            "total_size_bytes": 0,
            "oldest_log": None,
            "newest_log": None,
            "log_levels": {"DEBUG": 0, "INFO": 0, "WARNING": 0, "ERROR": 0, "CRITICAL": 0},
            "total_lines": 0
        }
        
        oldest_time = float('inf')
        newest_time = 0
        
        for log_file in log_files:
            try:
                file_stat = log_file.stat()
                stats["total_size_bytes"] += file_stat.st_size
                
                if file_stat.st_mtime < oldest_time:
                    oldest_time = file_stat.st_mtime
                    stats["oldest_log"] = str(log_file)
                
                if file_stat.st_mtime > newest_time:
                    newest_time = file_stat.st_mtime
                    stats["newest_log"] = str(log_file)
                
                # Count lines and log levels
                async with aiofiles.open(log_file, 'r') as f:
                    content = await f.read()
                    lines = content.strip().split('\n')
                    stats["total_lines"] += len(lines)
                    
                    for line in lines:
                        for level in stats["log_levels"]:
                            if f" - {level} - " in line:
                                stats["log_levels"][level] += 1
                                break
                                
            except Exception as e:
                logging.getLogger(__name__).warning(f"Error analyzing log file {log_file}: {e}")
        
        # Convert timestamps to readable format
        if oldest_time != float('inf'):
            stats["oldest_log_time"] = datetime.fromtimestamp(oldest_time, timezone.utc).isoformat()
        if newest_time > 0:
            stats["newest_log_time"] = datetime.fromtimestamp(newest_time, timezone.utc).isoformat()
        
        return stats


class LogMigrationHelper:
    """Helper for migrating existing code to use project+user logging."""
    
    @staticmethod
    def create_migration_guide() -> str:
        """Generate migration guide for developers."""
        guide = """
# Migration Guide: Project+User Logging

## Quick Migration Steps:

1. **Replace existing logger creation:**
   ```python
   # OLD:
   logger = logging.getLogger(__name__)
   
   # NEW:
   from services.logging_utils import LoggingHelper
   logger = LoggingHelper.get_logger()  # Auto-detects context
   # OR
   logger = LoggingHelper.get_logger(user_id, project_id)  # Explicit
   ```

2. **Replace logging calls:**
   ```python
   # OLD:
   logger.info("Processing file")
   
   # NEW:
   LoggingHelper.info("Processing file")  # Auto-detects context
   # OR
   LoggingHelper.info("Processing file", user_id, project_id)  # Explicit
   ```

3. **For structured logging:**
   ```python
   LoggingHelper.info(
       "File processed successfully",
       extra_data={"file_name": "test.pdf", "processing_time": 1.23}
   )
   ```

## Benefits:
- Isolated logs per user+project
- Better debugging and issue tracking
- Automatic context detection
- Structured logging support
- Backward compatibility maintained
"""
        return guide


# Global instances for easy access
log_helper = LoggingHelper()
log_analyzer = LogAnalyzer()
migration_helper = LogMigrationHelper()

# Convenience functions for backward compatibility
def get_logger(user_id: str = None, project_id: str = None, logger_name: str = None) -> logging.Logger:
    """Get appropriate logger (convenience function)."""
    return log_helper.get_logger(user_id, project_id, logger_name)

def log_info(message: str, user_id: str = None, project_id: str = None, 
             extra_data: Dict[str, Any] = None):
    """Log info message (convenience function)."""
    log_helper.info(message, user_id, project_id, extra_data)

def log_error(message: str, user_id: str = None, project_id: str = None, 
              extra_data: Dict[str, Any] = None):
    """Log error message (convenience function)."""
    log_helper.error(message, user_id, project_id, extra_data)
