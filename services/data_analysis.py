from fastapi import HTTPException
from services.data_analyst_agent import data_analyst_agent
from services.utils import initialize_assistant, draft_response, draft_response_for_file_upload, custom_llm_prompt_call
from services.mongo_manager import MongoManager
import config
from typing import List, Optional, Union
from models import insight_model
from services.utils import upload_files, get_assistant, set_assistant_model, get_thread, get_file_ids_from_file_details, get_chart_bytes
import time
import logging
from services.pdf_processor import PDFProcessor
# import jsonpatch
import json
import pandas as pd
from bson.objectid import ObjectId
from sse_starlette.sse import EventSourceResponse
import asyncio
from config import openai_client
from datetime import datetime, timezone
from exceptions.api_exceptions import handle_openai_error
from services import custom_prompts
from starlette.concurrency import run_in_threadpool
from services.logging_utils import LoggingHelper

# Configure logging
logger = logging.getLogger(__name__)


mongo_manager = MongoManager(config.insights_collection, config.images_collection, config.tables_collection, config.compliance_collection)

async def process_data_analysis(
    file_path: Optional[str] = None,
    file_type: Optional[str] = None,
    file_name: Optional[str] = None,
    prompt: str = "",
    project_id: str = "",
    user_id: str = "",
    title: str = "",
    response_type: str = "",
    intent: Union[dict, str] = {},
    item: Union[List[str], str] = "",
    visualization: bool = False,
    custom_prompt_name: Optional[str] = None,
    token_logger = None,
    unified_logger = None
):
    try:
        LoggingHelper.info("Entered data analysis mode", user_id=user_id, project_id=project_id,
                          extra_data={"custom_prompt_name": custom_prompt_name, "item": str(item)[:200] if item else None})

        # Log token tracking step
        if unified_logger:
            unified_logger.log_step_summary("DATA_ANALYSIS_START", "Entered data analysis mode")
        else:
            LoggingHelper.warning("No unified usage logger provided for data analysis", 
                                 user_id=user_id, project_id=project_id,
                                 extra_data={"operation": "data_analysis_start"})

        # Get or initialize the project
        project, exists, created = await mongo_manager.get_or_initialize_project(project_id)

        # Get previous chat history
        previous_prompt = project.get("chat_prompt", [])
        previous_responses = project.get("chat_response", [])
        previous_complete_prompt = project.get("prompt", "")
        previous_complete_responses = project.get("response", "")
        if created:
            complete_chat = []
        else:
            complete_chat = project.get("complete_chat", [])

        chat_history = [{p: r} for p, r in zip(previous_prompt, previous_responses)]

        # Prepare update data
        update_data = {
            "timestamp": int(datetime.now(timezone.utc).timestamp()) if not exists else project.get("timestamp"),
            "user_id": user_id,
            "title": title if not exists else project.get("title", 'Untitled')
        }
        await mongo_manager.update_project(project_id, update_data)

        # map the assistant instructions as per the response level
        if response_type == "brief":
            assistant_instructions = config.assistant_instructions_for_brief_summaries
            LoggingHelper.info("Using brief response assistant instructions", user_id=user_id, project_id=project_id,
                              extra_data={"response_type": response_type})
        else:
            assistant_instructions = config.assistant_instructions_for_detailed_summaries
            LoggingHelper.info("Using detailed response assistant instructions", user_id=user_id, project_id=project_id,
                              extra_data={"response_type": response_type})

        # Check if an assistant ID already exists for this project
        existing_assistant_id = await mongo_manager.get_assistant_id(project_id)
        LoggingHelper.info("Retrieved existing assistant ID", user_id=user_id, project_id=project_id,
                          extra_data={"existing_assistant_id": existing_assistant_id})
        existing_thread_id = await mongo_manager.get_thread_id(project_id)
        LoggingHelper.info("Retrieved existing thread ID", user_id=user_id, project_id=project_id,
                          extra_data={"existing_thread_id": existing_thread_id})

        if existing_assistant_id and existing_thread_id:
            # Use the existing assistant
            assistant = await get_assistant(existing_assistant_id)
            LoggingHelper.info("Retrieved existing assistant", user_id=user_id, project_id=project_id,
                              extra_data={"assistant_id": existing_assistant_id})
            # set the instructions for existing assistant
            assistant.instructions = assistant_instructions
            thread = await get_thread(existing_thread_id)
            LoggingHelper.info("Retrieved existing thread", user_id=user_id, project_id=project_id,
                              extra_data={"thread_id": existing_thread_id})
        else:
            # Initialize a new assistant
            LoggingHelper.info("Initializing new assistant", user_id=user_id, project_id=project_id)
            requested_model = await mongo_manager.get_model_for_project(project_id)
            LoggingHelper.info("Retrieved model for project", user_id=user_id, project_id=project_id,
                              extra_data={"requested_model": requested_model})

            # Ensure we use an OpenAI model for Assistants API
            from services.llm_service import get_assistant_model
            assistant_model = get_assistant_model(requested_model)
            LoggingHelper.info("Using assistant model", user_id=user_id, project_id=project_id,
                              extra_data={"assistant_model": assistant_model})

            assistant = await initialize_assistant(config.assistant_name, assistant_instructions, assistant_model, token_logger, unified_logger)
            LoggingHelper.info("Initialized new assistant", user_id=user_id, project_id=project_id,
                              extra_data={"assistant_id": assistant.id})

            # Log assistant initialization step
            if unified_logger:
                unified_logger.log_step_summary("ASSISTANT_INITIALIZATION", f"Initialized assistant with model: {assistant_model}")
            
            # Create a new thread
            try:
                # Wrap synchronous OpenAI call in run_in_threadpool
                def _sync_openai_call():
                    return config.openai_client.beta.threads.create()

                thread = await run_in_threadpool(_sync_openai_call)

                # Log thread creation (minimal token usage) - UNIFIED SYSTEM ONLY
                if unified_logger:
                    unified_logger.log_llm_usage(
                        model_name="openai_assistant",
                        input_tokens=0,  # Thread creation doesn't consume input tokens
                        output_tokens=0,  # Thread creation doesn't generate output tokens
                        operation_type="assistant_management",
                        operation_subtype="thread_creation",
                        operation_details={
                            "thread_id": thread.id if hasattr(thread, 'id') else "unknown",
                            "assistant_id": assistant.id if hasattr(assistant, 'id') else "unknown"
                        },
                        success=True
                    )

                LoggingHelper.info("Created new thread", user_id=user_id, project_id=project_id,
                                  extra_data={"thread_id": thread.id})
            except Exception as e:
                handle_openai_error(e)

            # Save the new assistant ID and thread ID
            await mongo_manager.update_project(project_id, {
                "assistant_id": assistant.id,
                "thread_id": thread.id
            })
            LoggingHelper.info("Saved new assistant and thread IDs", user_id=user_id, project_id=project_id,
                              extra_data={"assistant_id": assistant.id, "thread_id": thread.id})

        # check if assistants model and the model selected by user are same or not
        users_selected_model = await mongo_manager.get_model_for_project(project_id)
        LoggingHelper.info("Retrieved model to be used for project", user_id=user_id, project_id=project_id,
                          extra_data={"users_selected_model": users_selected_model})

        # Get the appropriate assistant model for the user's selected model
        from services.llm_service import get_assistant_model
        appropriate_assistant_model = get_assistant_model(users_selected_model)

        if assistant.model != appropriate_assistant_model:
            LoggingHelper.info("Changing assistant model", user_id=user_id, project_id=project_id,
                              extra_data={"from_model": assistant.model, "to_model": appropriate_assistant_model})
            await set_assistant_model(mongo_manager, project_id, appropriate_assistant_model)
            LoggingHelper.info("Changed assistant model successfully", user_id=user_id, project_id=project_id,
                              extra_data={"from_model": assistant.model, "to_model": appropriate_assistant_model})

        if file_path:
            LoggingHelper.info("Processing file upload", user_id=user_id, project_id=project_id,
                              extra_data={"file_path": file_path, "file_name": file_name})

            files_uploaded = await upload_files(file_path)
            LoggingHelper.info("Files uploaded successfully", user_id=user_id, project_id=project_id,
                              extra_data={"files_uploaded": files_uploaded})

            new_file_id = files_uploaded[0]['file_id']
            LoggingHelper.info("Retrieved new file ID", user_id=user_id, project_id=project_id,
                              extra_data={"new_file_id": new_file_id})

            # Get existing file details
            existing_file_details = await mongo_manager.get_file_details(project_id, user_id)

            # Process custom prompt if provided
            custom_prompt_content = None
            if custom_prompt_name:
                LoggingHelper.info("Processing custom prompt", user_id=user_id, project_id=project_id,
                                  extra_data={"custom_prompt_name": custom_prompt_name})
                # Store the original custom_prompt_name
                original_custom_prompt_name = custom_prompt_name

                # Check if custom_prompt_name has the format "category|||subcategory|||prompt_text"
                if "|||" in custom_prompt_name:
                    # Split the string by |||
                    parts = custom_prompt_name.split("|||")

                    # Extract category, subcategory and prompt text
                    if len(parts) >= 3:
                        prompt_text = parts[-1].strip()  # The prompt text is the last part
                        custom_prompt_content = prompt_text
                    elif len(parts) == 2:
                        prompt_text = parts[1].strip()
                        custom_prompt_content = prompt_text
                else:
                    # Get the prompt text using the function from custom_prompts
                    custom_prompt_content = custom_prompts.get_prompt_by_name(custom_prompt_name)

                LoggingHelper.info("Extracted custom prompt content", user_id=user_id, project_id=project_id,
                                  extra_data={"custom_prompt_content": custom_prompt_content[:100] if custom_prompt_content else None})

            # Add new file details with updated structure
            new_file_details = {
                "file_name": file_name,
                "file_type": file_type,
                "file_id": new_file_id,
                "file_path": file_path,
                "file_description": intent,
                "custom_prompt": custom_prompt_name or "",
                "custom_prompt_content": custom_prompt_content or ""
            }

            # Append new file details to the existing list
            existing_file_details.append(new_file_details)

            # Update project with new file data
            await mongo_manager.append_to_project(project_id, {"file_details": existing_file_details})
            LoggingHelper.info("Appended new file data to project", user_id=user_id, project_id=project_id)

            LoggingHelper.info("File uploaded, drafting response for file upload", user_id=user_id, project_id=project_id)
            if custom_prompt_name:
                LoggingHelper.info("Using custom prompt for file processing", user_id=user_id, project_id=project_id,
                                  extra_data={"custom_prompt_name": custom_prompt_name})
                # Read the file content based on file type asynchronously
                def _read_file():
                    if file_type.lower() == 'csv':
                        df = pd.read_csv(file_path)
                        return df.to_string()
                    elif file_type.lower() == 'xlsx':
                        df = pd.read_excel(file_path)
                        return df.to_string()
                    return ""

                file_content = await asyncio.to_thread(_read_file)

                LoggingHelper.info("Successfully read file content", user_id=user_id, project_id=project_id,
                                  extra_data={"file_type": file_type, "content_length": len(file_content) if file_content else 0})
                response = await custom_llm_prompt_call(custom_prompt_name, file_content, project_id, mongo_manager, tables="", suggested_questions=[], page_no="", response_type="brief", source_type="", images="", token_logger=token_logger, unified_logger=unified_logger)
            else:
                response = draft_response_for_file_upload(config.DEFAULT_UPLOAD_MESSAGE)

            response["response_type"] = response_type
            response["source_type"] = "dataset"
            response["response_id"] = str(ObjectId())
            response["response_time"] = datetime.now(timezone.utc).isoformat()

            # Get token consumption from unified logger and include in response
            if unified_logger:
                input_tokens, output_tokens = unified_logger.get_total_tokens()
                response["input_tokens_used"] = input_tokens
                response["output_tokens_used"] = output_tokens
                response["total_tokens_used"] = input_tokens + output_tokens

                # Link unified logger with response_id for proper tracking
                unified_logger.set_response_id(response["response_id"])
                LoggingHelper.info("Linked token events with response_id", user_id=user_id, project_id=project_id,
                                  extra_data={"response_id": response["response_id"]})
                LoggingHelper.info("Added token usage to response", user_id=user_id, project_id=project_id,
                                  extra_data={"input_tokens": input_tokens, "output_tokens": output_tokens, "total_tokens": input_tokens + output_tokens})
            else:
                # Fallback to 0 if no unified logger
                response["input_tokens_used"] = 0
                response["output_tokens_used"] = 0
                response["total_tokens_used"] = 0

            LoggingHelper.info("File upload response prepared", user_id=user_id, project_id=project_id,
                              extra_data={"source_type": response['source_type'], "response_type": response.get("response_type")})

            new_chat = {
                "filename": file_name or "",
                "query": prompt,
                "response": response
            }

            complete_chat.append(new_chat)
            # If custom_prompt_name is provided, set prompt to empty string
            prompt_to_save = "" if custom_prompt_name else prompt
            await mongo_manager.save_response_and_prompt(project_id, prompt_to_save, response, complete_chat)
            LoggingHelper.info("Saved response and prompt to mongo", user_id=user_id, project_id=project_id,
                              extra_data={"using_empty_prompt_for_custom": custom_prompt_name is not None})

            project_object_id = await mongo_manager.get_project_object_id(project_id)
            response["object_id"] = str(project_object_id)

            # Log final token tracking step
            if unified_logger:
                unified_logger.log_step_summary("DATA_ANALYSIS_COMPLETE", "Data analysis processing completed successfully")

            # Return the response here
            return response

        else:
            LoggingHelper.info("No file uploaded, using existing files from intent", user_id=user_id, project_id=project_id,
                              extra_data={"item": str(item), "item_type": str(type(item))})
            existing_file_details = await mongo_manager.get_file_details(project_id, user_id)
            LoggingHelper.info("Retrieved existing file details", user_id=user_id, project_id=project_id,
                              extra_data={"file_details_count": len(existing_file_details)})
            file_ids = get_file_ids_from_file_details(existing_file_details, item)
            LoggingHelper.info("Retrieved file IDs for data analysis", user_id=user_id, project_id=project_id,
                              extra_data={"file_ids": file_ids})

            file_path_for_assistant = []
            for file_id in file_ids:
                file_path_for_assistant.append(f"{config.data_mount_path}{file_id}")

            LoggingHelper.info("Drafted file path for assistant", user_id=user_id, project_id=project_id,
                              extra_data={"file_path_for_assistant": file_path_for_assistant})

            file_type = []
            for file_name in item:
                for file_detail in existing_file_details:
                    if file_detail["file_name"] == file_name:
                        file_type.append(file_detail["file_type"])
                        break

            LoggingHelper.info("File types", user_id=user_id, project_id=project_id,
                              extra_data={"file_type": file_type})

            response_detail = "brief" if response_type == "brief" else "detailed"
            LoggingHelper.info("Response type", user_id=user_id, project_id=project_id,
                              extra_data={"response_detail": response_detail})

            if len(chat_history) > 5:
                chat_history = chat_history[-5:]
                LoggingHelper.info("Chat history truncated to last 5 items: %s", chat_history)
            else:
                LoggingHelper.info("Chat history not truncated as its length is less than 5.", user_id=user_id, project_id=project_id)

            detail_instruction = "as brief as possible" if response_type == "brief" else "as detailed as possible, covering all the details, observations, analysis you can offer related to the query"

            new_prompt = f"{prompt} .The filetypes of the files to be used are {file_type}. The list of filepaths to use for reading the file is {file_path_for_assistant}. During writing code for reading the files, use these filepaths explicitly and don't try to overwrite these filepaths. If charts or visualizations are requested, the charts should have proper chart title, legends, labels, a brief chart description at the bottom of the chart, ensuring the chart description is placed at the bottom below the chart itself and not at the top. Do not provide the chart or visualisation links in your response, as we will be using ids of the generated charts and visualisations to refer to them. Ensure that all mathematical portions or equations in your response are formatted using LaTeX. \n Your textual response has to be {detail_instruction}"

            # if not visualization:
            #     LoggingHelper.info("Adding instruction to not create any visualisations or charts.", user_id=user_id, project_id=project_id)
            #     new_prompt = f"{new_prompt} \n Do not create any visualisations or charts. Provide response without the charts or visaulizations"

            if len(chat_history) > 0:
                context_prompt = f"""You can refer to the chat history to use relevant context for your response. chat history: {chat_history}"""
                new_prompt = f"{new_prompt} \n {context_prompt}"
                LoggingHelper.info("Added chat history to the prompt", user_id=user_id, project_id=project_id,
                                  extra_data={"chat_history_length": len(chat_history)})

            async def event_generator():
                response_id_for_tokens = None  # Track response_id for token linking
                try:
                    # Log streaming start for data analysis
                    LoggingHelper.info("STREAMING_START - Data analysis streaming initiated",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={
                                          "stream_type": "data_analysis",
                                          "assistant_id": assistant.id if assistant else None,
                                          "thread_id": thread.id if thread else None,
                                          "file_count": len(file_ids) if file_ids else 0,
                                          "prompt_length": len(new_prompt) if new_prompt else 0
                                      })

                    async for event in data_analyst_agent(assistant, thread, file_ids, new_prompt, token_logger, unified_logger):
                        event_data = json.loads(event)

                        if event_data["type"] == "thread_id":
                            thread_id = event_data["content"]
                            LoggingHelper.info("Got thread Id for drafting final response", user_id=user_id, project_id=project_id,
                                              extra_data={"thread_id": thread_id})
                            try:
                                # Wrap synchronous OpenAI call in run_in_threadpool
                                def _sync_openai_call():
                                    return openai_client.beta.threads.messages.list(thread_id=thread_id)

                                messages = await run_in_threadpool(_sync_openai_call)
                                # Process final response using existing logic
                                response = await draft_response(messages, project_id, token_logger, unified_logger)
                                response["response_type"] = response_type
                                response["source_type"] = "dataset"
                                response["response_id"] = str(ObjectId())
                                response["response_time"] = datetime.now(timezone.utc).isoformat()

                                # Store response_id for token linking in finally block
                                response_id_for_tokens = response["response_id"]

                                # Get token consumption from unified logger and include in response
                                if unified_logger:
                                    input_tokens, output_tokens = unified_logger.get_total_tokens()
                                    response["input_tokens_used"] = input_tokens
                                    response["output_tokens_used"] = output_tokens
                                    response["total_tokens_used"] = input_tokens + output_tokens

                                    # Link unified logger with response_id for proper tracking
                                    unified_logger.set_response_id(response["response_id"])
                                    LoggingHelper.info("Linked token events with response_id", user_id=user_id, project_id=project_id,
                                                      extra_data={"response_id": response["response_id"]})
                                    LoggingHelper.info("Added token usage to response", user_id=user_id, project_id=project_id,
                                                      extra_data={"input_tokens": input_tokens, "output_tokens": output_tokens, "total_tokens": input_tokens + output_tokens})
                                else:
                                    # Fallback to 0 if no unified logger
                                    response["input_tokens_used"] = 0
                                    response["output_tokens_used"] = 0
                                    response["total_tokens_used"] = 0
                            except Exception as e:
                                handle_openai_error(e)

                            # Process images
                            image_data_for_images_collection = []
                            if response["images"]:
                                LoggingHelper.info("Processing images... as images are found in response", user_id=user_id, project_id=project_id)
                                for image in response.get("images", []):
                                    chart_prefix = "data:image/png;base64,"
                                    image_bytes = await get_chart_bytes(image)
                                    image_bytes = chart_prefix + image_bytes
                                    image_data_for_images_collection.append({"imageId": image, "image": image_bytes})

                                project_object_id = await mongo_manager.get_project_object_id(project_id)
                                if not project_object_id:
                                    raise ValueError(f"Could not find project with ID: {project_id}")
                                LoggingHelper.info("preparing to save image data in images collection", user_id=user_id, project_id=project_id)
                                images_collection_id = await mongo_manager.save_images(project_object_id, image_data_for_images_collection)
                                response["images"] = images_collection_id
                                LoggingHelper.info("Saved image data in images collection", user_id=user_id, project_id=project_id)
                            else:
                                LoggingHelper.info("No images found in response", user_id=user_id, project_id=project_id)
                                response["images"] = ""

                            # Save to database
                            try:
                                pdf_processor = PDFProcessor(config.openai_client, mongo_manager)
                                new_chat = {
                                    "filename": file_name or "",
                                    "query": prompt,
                                    "response": response
                                }
                                complete_chat.append(new_chat)

                                # If custom_prompt_name is provided, set prompt to empty string
                                prompt_to_save = "" if custom_prompt_name else prompt

                                await pdf_processor.save_project_data(
                                    file_exist=bool(file_path),
                                    project_id=project_id,
                                    prompt=prompt_to_save,
                                    user_id=user_id,
                                    page_numbers=[],
                                    file_name=file_name,
                                    file_type=file_type,
                                    title=title,
                                    insight_data=response,
                                    extracted_text_per_page=[],
                                    extracted_text="",
                                    intent="",
                                    complete_chat=complete_chat,
                                    custom_prompt_name=custom_prompt_name
                                )
                            except Exception as e:
                                from exceptions.api_exceptions import handle_general_error
                                handle_general_error(e)

                            # Prepare final response
                            if image_data_for_images_collection != []:
                                response["images"] = image_data_for_images_collection
                            else:
                                response["images"] = ""

                            project_object_id = await mongo_manager.get_project_object_id(project_id)
                            response["object_id"] = str(project_object_id)

                            # Yield the final processed response
                            yield json.dumps({"type": "final_response", "content": response})
                        else:
                            # Pass through other events (code_input, code_output, message)
                            yield event

                except Exception as e:
                    # Instead of raising exception, yield error event to frontend
                    try:
                        handle_openai_error(e)
                    except Exception as api_error:
                        # Yield error event that frontend can handle
                        yield json.dumps({
                            "type": "error",
                            "content": str(api_error)
                        })
                        return  # Stop the generator
                finally:
                    # Save usage events to MongoDB after streaming completes
                    if unified_logger:
                        # Set response_id if we have one (ensures proper linking)
                        if response_id_for_tokens and not unified_logger.response_id:
                            unified_logger.set_response_id(response_id_for_tokens)
                        
                        # Log completion of streaming operation
                        unified_logger.log_step_summary("DATA_ANALYSIS_QUERY_COMPLETE", "Data analysis query processing completed successfully")
                        
                        # Save events to MongoDB
                        await unified_logger.save_to_mongodb()

            return EventSourceResponse(event_generator())
    except Exception as e:
        LoggingHelper.error("Error in process_data_analysis", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "error_type": type(e).__name__})
        from exceptions.api_exceptions import handle_general_error
        handle_general_error(e)




