from openai import OpenAI, Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>
from typing_extensions import override
import time
from config import openai_client
import logging
import json
from exceptions.api_exceptions import handle_openai_error
from starlette.concurrency import run_in_threadpool
from services.unified_usage_tracking import UnifiedUsageLogger
from services.logging_utils import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Optional
from services.token_tracking import TokenEventLogger

logger = logging.getLogger(__name__)

def _get_context_or_fallback():
    """Get user/project context with fallback to None for error handling."""
    try:
        from services.logging_middleware import get_current_user_id, get_current_project_id
        return get_current_user_id(), get_current_project_id()
    except:
        return None, None

client = openai_client

class EventHandler(AssistantEventHandler):
    @override
    def on_text_created(self, text) -> None:
        # print(f"\nassistant > ", end="", flush=True)
        pass

    @override
    def on_text_delta(self, delta, snapshot):
        # print(delta.value, end="", flush=True)
        pass

    def on_tool_call_created(self, tool_call):
        # print(f"\nassistant > {tool_call.type}\n", flush=True)
        pass

    def on_tool_call_delta(self, delta, snapshot):
        if delta.type == 'code_interpreter':
            if delta.code_interpreter.input:
                print(delta.code_interpreter.input, end="", flush=True)
            if delta.code_interpreter.outputs:
                print(f"\n\noutput >", flush=True)
                for output in delta.code_interpreter.outputs:
                    if output.type == "logs":
                        print(f"\n{output.logs}", flush=True)

async def run(thread_id, assistant_id):
    try:
        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            with client.beta.threads.runs.stream(
                thread_id=thread_id,
                assistant_id=assistant_id,
                event_handler=EventHandler(),
            ) as stream:
                stream.until_done()

        await run_in_threadpool(_sync_openai_call)
    except Exception as e:
        handle_openai_error(e)

async def add_query(query, thread, files_to_use, token_logger: Optional[TokenEventLogger] = None, unified_logger=None):
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.info("Adding query to thread", user_id=user_id, project_id=project_id,
                      extra_data={"query": str(query)[:200]})
    try:
        if type(files_to_use) == str:
            files_to_use = [files_to_use]
        LoggingHelper.info("files_to_use", user_id=user_id, project_id=project_id,
                          extra_data={"files_to_use": files_to_use})
        LoggingHelper.info("type of files_to_use", user_id=user_id, project_id=project_id,
                          extra_data={"type": str(type(files_to_use))})

        # Estimate input tokens for the query
        from services.token_utils import get_tokens
        input_tokens = get_tokens(query)

        # Wrap synchronous OpenAI call in run_in_threadpool
        def _sync_openai_call():
            return client.beta.threads.messages.create(
                thread_id=thread.id,
                role="user",
                attachments=[{"file_id": file, "tools": [{"type": "code_interpreter"}]} for file in files_to_use],
                content=query
            )

        message = await run_in_threadpool(_sync_openai_call)

        # Log the message creation (minimal token usage for message creation itself) - UNIFIED SYSTEM ONLY
        if unified_logger:
            unified_logger.log_llm_usage(
                model_name="openai_assistant",
                input_tokens=input_tokens,
                output_tokens=0,  # Message creation doesn't generate output
                operation_type="assistant_message",
                operation_subtype="message_creation",
                operation_details={
                    "thread_id": thread.id,
                    "message_id": message.id if hasattr(message, 'id') else "unknown",
                    "files_attached": len(files_to_use),
                    "query_length": len(query)
                },
                success=True
            )
        else:
            # Log warning if no unified logger provided
            LoggingHelper.warning("No unified usage logger provided for assistant message creation", 
                                 user_id=user_id, project_id=project_id,
                                 extra_data={"operation": "assistant_message_creation"})

        LoggingHelper.info("Added query to thread", user_id=user_id, project_id=project_id)
        return message
    except Exception as e:
        handle_openai_error(e)

async def data_analyst_agent(assistant, thread, files_to_use, user_query, token_logger: Optional[TokenEventLogger] = None, unified_logger=None):
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.info("Data analyst agent called", user_id=user_id, project_id=project_id)

    try:
        # Add query to thread with token tracking
        await add_query(user_query, thread, files_to_use, token_logger, unified_logger)

        # Create a sync generator function to handle the streaming
        def _sync_stream_generator():
            run_id = None
            accumulated_output = ""

            # Log streaming start for OpenAI assistant
            LoggingHelper.info("STREAMING_START - OpenAI Assistant streaming initiated",
                              user_id=user_id, project_id=project_id,
                              extra_data={
                                  "stream_type": "openai_assistant",
                                  "assistant_id": assistant.id,
                                  "thread_id": thread.id,
                                  "files_count": len(files_to_use) if files_to_use else 0,
                                  "query_length": len(user_query) if user_query else 0
                              })

            with client.beta.threads.runs.stream(
                thread_id=thread.id,
                assistant_id=assistant.id,
                event_handler=EventHandler(),
            ) as stream:
                LoggingHelper.info("Streaming events started", user_id=user_id, project_id=project_id)
                for event in stream:
                    event_type = type(event).__name__
                    event_data = {}

                    # Capture run ID for token tracking
                    if event_type == "ThreadRunCreated" and hasattr(event, 'data') and hasattr(event.data, 'id'):
                        run_id = event.data.id
                        LoggingHelper.info("Assistant run started with ID", user_id=user_id, project_id=project_id,
                                          extra_data={"run_id": run_id})

                    if event_type == "ThreadRunStepDelta":
                        if event.data.delta.step_details and event.data.delta.step_details.tool_calls:
                            for tool_call in event.data.delta.step_details.tool_calls:
                                if hasattr(tool_call, 'code_interpreter'):
                                    if tool_call.code_interpreter.input:
                                        event_data = {
                                            "type": "code_input",
                                            "content": tool_call.code_interpreter.input
                                        }
                                    if tool_call.code_interpreter.outputs:
                                        for output in tool_call.code_interpreter.outputs:
                                            if hasattr(output, 'logs'):
                                                event_data = {
                                                    "type": "code_output",
                                                    "content": output.logs
                                                }

                    elif event_type == "ThreadMessageDelta":
                        if event.data.delta.content:
                            for content in event.data.delta.content:
                                if hasattr(content, 'text'):
                                    accumulated_output += content.text.value
                                    event_data = {
                                        "type": "message",
                                        "content": content.text.value
                                    }

                    if event_data:
                        yield json.dumps(event_data)

                # Stream is complete, now track tokens
                if run_id and (token_logger or unified_logger):
                    try:
                        # Get token usage from the completed run
                        from services.openai_utils import get_assistant_tokens
                        import asyncio

                        # We need to run this in the async context
                        def get_tokens_sync():
                            return asyncio.run(get_assistant_tokens(thread.id, run_id, token_logger, unified_logger))

                        token_usage = get_tokens_sync()
                        LoggingHelper.info("Assistant run completed", user_id=user_id, project_id=project_id,
                                          extra_data={"token_usage": token_usage})
                    except Exception as e:
                        LoggingHelper.error("Failed to track assistant tokens", user_id=user_id, project_id=project_id,
                                           extra_data={"error": str(e)})

                # Stream is complete, yield final response
                yield json.dumps({"type": "complete"})
                LoggingHelper.info("Stream is complete, yielding final response", user_id=user_id, project_id=project_id)
                # LoggingHelper.info("Fetching messages from thread", user_id=user_id, project_id=project_id)
                # messages = client.beta.threads.messages.list(thread_id=thread.id)
                yield json.dumps({"type": "thread_id", "content": thread.id})

        # Use asyncio.Queue to bridge sync generator to async generator for real-time streaming
        async def async_generator():
            import asyncio
            import threading

            # Create a queue to bridge sync and async
            queue = asyncio.Queue(maxsize=10)  # Limit queue size to prevent memory issues
            exception_holder = [None]  # Use list to allow modification in nested function
            loop = asyncio.get_event_loop()

            def run_sync_generator():
                """Run the sync generator in a separate thread and put items in queue"""
                try:
                    LoggingHelper.info("STREAMING_DEBUG - Sync generator thread started",
                                      user_id=user_id, project_id=project_id)
                    item_count = 0
                    for item in _sync_stream_generator():
                        item_count += 1
                        # Put item in queue (thread-safe) with timeout to prevent blocking
                        future = asyncio.run_coroutine_threadsafe(queue.put(item), loop)
                        try:
                            future.result(timeout=5.0)  # 5 second timeout
                            LoggingHelper.info(f"STREAMING_DEBUG - Item {item_count} queued",
                                              user_id=user_id, project_id=project_id,
                                              extra_data={"item_preview": str(item)[:100]})
                        except Exception as e:
                            LoggingHelper.error(f"STREAMING_DEBUG - Failed to queue item {item_count}: {str(e)}",
                                               user_id=user_id, project_id=project_id)
                            break

                    # Signal completion
                    future = asyncio.run_coroutine_threadsafe(queue.put(None), loop)
                    future.result(timeout=5.0)
                    LoggingHelper.info(f"STREAMING_DEBUG - Sync generator completed, total items: {item_count}",
                                      user_id=user_id, project_id=project_id)
                except Exception as e:
                    LoggingHelper.error(f"STREAMING_DEBUG - Error in sync generator: {str(e)}",
                                       user_id=user_id, project_id=project_id)
                    # Store exception to re-raise in async context
                    exception_holder[0] = e
                    try:
                        future = asyncio.run_coroutine_threadsafe(queue.put(None), loop)
                        future.result(timeout=1.0)
                    except:
                        pass  # Ignore errors when signaling completion after exception

            # Start the sync generator in a separate thread
            thread = threading.Thread(target=run_sync_generator)
            thread.daemon = True
            thread.start()

            try:
                LoggingHelper.info("STREAMING_DEBUG - Async generator started, waiting for items",
                                  user_id=user_id, project_id=project_id)
                yielded_count = 0
                while True:
                    try:
                        # Get item from queue with timeout to prevent hanging
                        item = await asyncio.wait_for(queue.get(), timeout=30.0)

                        # Check if we're done (None signals completion)
                        if item is None:
                            LoggingHelper.info(f"STREAMING_DEBUG - Async generator completed, total yielded: {yielded_count}",
                                              user_id=user_id, project_id=project_id)
                            break

                        # Check for exceptions
                        if exception_holder[0]:
                            raise exception_holder[0]

                        yielded_count += 1
                        LoggingHelper.info(f"STREAMING_DEBUG - Yielding item {yielded_count}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={"item_preview": str(item)[:100]})
                        yield item

                    except asyncio.TimeoutError:
                        LoggingHelper.warning("STREAMING_DEBUG - Timeout waiting for queue item",
                                             user_id=user_id, project_id=project_id)
                        break

            finally:
                # Ensure thread cleanup
                if thread.is_alive():
                    LoggingHelper.info("STREAMING_DEBUG - Waiting for thread to complete",
                                      user_id=user_id, project_id=project_id)
                    thread.join(timeout=2.0)
                    if thread.is_alive():
                        LoggingHelper.warning("STREAMING_DEBUG - Thread did not complete in time",
                                             user_id=user_id, project_id=project_id)

        # Return the async generator
        async for item in async_generator():
            yield item

    except Exception as e:
        LoggingHelper.error("Error in data_analyst_agent", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "error_type": type(e).__name__})
        handle_openai_error(e)
        # This line won't be reached if handle_openai_error raises as expected
