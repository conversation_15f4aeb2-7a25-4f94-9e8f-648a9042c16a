"""
Backward compatibility module for utils.py.
This file imports all functions from utils_main.py to maintain backward compatibility.
"""

# Import all functions from utils_main
from services.utils_main import *
from services.logging_utils import LoggingHelper
from services import custom_prompts  # Import custom_prompts module

# This file is kept for backward compatibility with existing code.
# New code should import directly from the specialized utility modules.

# Import any additional functions that are still in this file but not in the specialized modules
import logging
import config
from exceptions.api_exceptions import handle_general_error

logger = logging.getLogger(__name__)
openai_client = config.openai_client


