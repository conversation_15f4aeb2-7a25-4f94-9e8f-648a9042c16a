"""
Main utils module that imports and re-exports all utility functions.
This allows for backward compatibility with existing code that imports from utils.py
"""

# Import all functions from specialized utility modules
from services.file_utils import *
from services.auth_utils import *
from services.text_extraction_utils import *
from services.search_utils import *
from services.data_processing_utils import *
from services.image_utils import *
from services.crypto_utils import *

# Import LangChain-based LLM functions (replaces most OpenAI functions)
from services.llm_utils import (
    get_intent,
    call_llm,
    get_compliance_report,
    generate_title,
    check_restricted_keywords,
    custom_llm_prompt_call,
    fake_llm_resonse,
    get_tokens,
    get_unique_id
)

# Import OpenAI Assistant functions (keep these unchanged)
from services.openai_utils import (
    get_assistant,
    get_thread,
    initialize_assistant,
    set_assistant_model,
    add_query,
    get_assistant_tokens,
    get_default_prompt,
    get_default_prompt_for_direct_chat,
    truncate_prompt_having_pipes,
    draft_response,
    draft_response_for_file_upload,
    extract_json
)

# Initialize any shared resources or variables
import logging
logger = logging.getLogger(__name__)

# Re-export any constants or variables that were in the original utils.py
# (These would be moved here from the original utils.py file)
