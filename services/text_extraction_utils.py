import os
import logging
import re
import tempfile
import asyncio
from typing import Optional, List
from fastapi import UploadFile, HTTPException
import shutil
from io import BytesIO
from PyPDF2 import PdfReader
from PIL import Image, ImageStat
import pdfplumber
import fitz  # PyMuPDF
import pytesseract  # For OCR
import base64
from docx import Document as DocxDocument
import config
import aiofiles
from exceptions.api_exceptions import handle_openai_error
from services.image_utils import is_blank_image, encode_image
from services.logging_utils import LoggingHelper
from services.async_fs_utils import async_makedirs, async_remove, async_path_exists
import numpy as np
from starlette.concurrency import run_in_threadpool

logger = logging.getLogger(__name__)

# Configuration variables
page_limit = config.page_limit
ocr = config.ocr
vision = config.vision
client = config.openai_client
text_extraction_prompt = config.text_extraction_prompt

# Functions to be moved here:

async def extract_text_from_file(file: UploadFile, page_number: Optional[str],
                                user_prompt: str = "", project_id: str = "",
                                mongo_manager=None, model: str = ""):
        # Read the file and extract text from specified pages
        file_data = await file.read()

        # Wrap PDF processing in asyncio.to_thread to avoid blocking
        def _process_pdf():
            pdf_file = BytesIO(file_data)
            reader = PdfReader(pdf_file)
            total_pages = len(reader.pages)

            # Extract text per page
            extracted_text_per_page = [reader.pages[i].extract_text() for i in range(total_pages)]
            return reader, total_pages, extracted_text_per_page

        reader, total_pages, extracted_text_per_page = await asyncio.to_thread(_process_pdf)

        # Handle page number input and convert it into a list of integers
        if page_number:
            page_numbers = expand_ranges(page_number)
            valid_page_numbers = [page_num for page_num in page_numbers if 1 <= page_num <= total_pages]
        else:
            valid_page_numbers = []

        if not valid_page_numbers:
            # valid_page_numbers = list(range(1, min(self.page_limit + 1, total_pages + 1)))
            valid_page_numbers = list(range(1, min(page_limit + 1, total_pages + 1)))

        # Apply token-aware extraction if parameters are provided
        if user_prompt and project_id and mongo_manager and model:
            extracted_text, included_pages, token_info = await extract_text_with_token_limit(
                valid_page_numbers, extracted_text_per_page, user_prompt,
                project_id, mongo_manager, model, page_number
            )
            return extracted_text, included_pages, extracted_text_per_page, valid_page_numbers, token_info
        else:
            # Fallback to original behavior for backward compatibility
            def _extract_text():
                return "\n".join([reader.pages[page_num - 1].extract_text() for page_num in valid_page_numbers])

            extracted_text = await asyncio.to_thread(_extract_text)
            return extracted_text, valid_page_numbers, extracted_text_per_page, valid_page_numbers


async def calculate_prompt_components_tokens(
    user_prompt: str,
    project_id: str,
    mongo_manager,
    model: str
) -> dict:
    """Calculate tokens for all prompt components except page content"""
    from services.token_utils import get_tokens
    from datetime import datetime, timezone

    # Get chat history
    project_data, exists, _ = await mongo_manager.get_or_initialize_project(project_id)
    previous_prompt = project_data.get("chat_prompt", [])
    previous_responses = project_data.get("chat_response", [])

    # Process chat history exactly like current system
    chat_history = [{p: r} for p, r in zip(previous_prompt, previous_responses)]
    if len(chat_history) > 5:
        chat_history = chat_history[-5:]

    # Calculate tokens for each component
    components = {
        "user_prompt": get_tokens(user_prompt, model),
        "chat_history": get_tokens(str(chat_history), model),
        "system_template": get_tokens(get_system_prompt_template(), model),
        "response_format": get_tokens(get_response_format_json(), model),
        "formatting_overhead": 100,  # For JSON formatting, newlines, etc.
    }

    components["total_fixed"] = sum(components.values())

    return components


def get_system_prompt_template() -> str:
    """Get the system prompt template without extracted_text"""
    from datetime import datetime, timezone
    current_date = datetime.now(timezone.utc).date()

    return f"""
    Your name is "Neuquip Buddy" and today is {current_date} , You are skilled at analyzing unstructured data and identifying trends. Your objective is to generate a detailed response to the user's query based on the provided context and chat history. If no context or chat history is provided, use your own knowledge to answer the query. Provide a detailed summary of observed trends and suggest an appropriate chart if relevant. If the user prefers not to have tables, omit table generation. Include four suggested questions related to the given context.

    *Instructions:*
    * Analyze the provided context thoroughly and extract meaningful insights.
    * Focus on trends, patterns, and key findings that directly address the user's query.
    * Create relevant visualizations and charts to support your analysis. Focus on presenting the data in clear, informative ways.
    * Ensure each visualization has a clear purpose and adds value to the analysis.
    * Provide actionable insights and recommendations where applicable.
    * If specific data points are mentioned, include them in your analysis.
    * Maintain a professional yet accessible tone throughout your response.
    * If the context is insufficient to fully answer the query, clearly state what additional information would be helpful.

    *Information Sources:*

    ### Context:
    [CONTEXT_PLACEHOLDER]

    ### Chat History:
    [CHAT_HISTORY_PLACEHOLDER]

    ### User Query:
    [USER_QUERY_PLACEHOLDER]
    """


def get_response_format_json() -> str:
    """Get the response guidelines template (now using Pydantic model instead of JSON format)"""
    return """
    *Response Guidelines:*
    - Provide your analysis and response to the user query in the summary
    - If user asks for "a table" or "create a table" (without chart/graph context), include the table in summary and leave tables empty
    - Only include data in tables if user specifically requests charts, graphs, or visualizations
    - If no chart/visualization is requested, leave tables empty
    - Always include exactly 4 relevant suggested questions
    - For any charts/visualizations, provide clear names, types, descriptions, labels and values
    """


async def truncate_content_intelligently(
    content,  # Accept any type, we'll convert to string
    max_tokens: int,
    model: str = "deepseek-chat",
    preserve_start: int = 2000,
    preserve_end: int = 1000
) -> str:
    """
    Intelligently truncate content to fit within token limits while preserving
    important information from the beginning and end.

    Args:
        content: The content to truncate (string or list)
        max_tokens: Maximum tokens allowed
        model: Model name for token counting
        preserve_start: Tokens to preserve from start
        preserve_end: Tokens to preserve from end

    Returns:
        Truncated content that fits within token limits
    """
    from services.token_utils import get_tokens

    # Handle different content types
    if isinstance(content, list):
        # If content is a list (e.g., from MongoDB full_text), join it
        content = "\n".join(str(item) for item in content)
    elif not isinstance(content, str):
        # Convert any other type to string
        content = str(content)

    current_tokens = get_tokens(content, model)

    if current_tokens <= max_tokens:
        return content

    # Calculate how much we can keep
    available_tokens = max_tokens - 100  # Reserve for truncation message
    start_tokens = min(preserve_start, available_tokens // 2)
    end_tokens = min(preserve_end, available_tokens - start_tokens)

    # Estimate character positions based on token counts
    # Rough approximation: 1 token ≈ 4 characters
    chars_per_token = len(content) / current_tokens if current_tokens > 0 else 4

    start_chars = int(start_tokens * chars_per_token)
    end_chars = int(end_tokens * chars_per_token)

    # Extract start and end portions
    start_portion = content[:start_chars]
    end_portion = content[-end_chars:] if end_chars > 0 else ""

    # Create truncated content with clear indication
    truncation_message = f"\n\n[CONTENT TRUNCATED - Original: {current_tokens:,} tokens, Showing: ~{start_tokens + end_tokens:,} tokens]\n\n"

    if end_portion:
        truncated_content = start_portion + truncation_message + end_portion
    else:
        truncated_content = start_portion + truncation_message

    return truncated_content


async def extract_text_with_token_limit(
    valid_page_numbers: List[int],
    extracted_text_per_page: List[str],
    user_prompt: str,
    project_id: str,
    mongo_manager,
    model: str,
    page_number: Optional[str] = None
) -> tuple:
    """Extract text with token limit awareness and 5000 token safety margin"""
    from services.llm_service import get_model_context_window
    from services.token_utils import get_tokens

    # Calculate fixed token components
    token_components = await calculate_prompt_components_tokens(
        user_prompt, project_id, mongo_manager, model
    )

    # Calculate available tokens for page content with 5000 token safety margin
    context_window = get_model_context_window(model)
    safety_margin = 5000
    available_for_pages = context_window - token_components["total_fixed"] - safety_margin

    # Apply incremental page addition
    extracted_text, included_pages, page_tokens = add_pages_with_token_limit(
        valid_page_numbers, extracted_text_per_page,
        available_for_pages, model, page_number
    )

    # Prepare comprehensive token information
    token_info = {
        "token_breakdown": token_components,
        "available_for_pages": available_for_pages,
        "pages_token_used": page_tokens,
        "total_estimated_tokens": token_components["total_fixed"] + page_tokens,
        "context_window": context_window,
        "utilization_percentage": ((token_components["total_fixed"] + page_tokens) / context_window) * 100,
        "pages_included": len(included_pages),
        "pages_total": len(valid_page_numbers),
        "safety_margin": safety_margin,
        "included_page_numbers": included_pages,
        "excluded_page_numbers": [p for p in valid_page_numbers if p not in included_pages]
    }

    # Log comprehensive token information
    LoggingHelper.info(
        "Token-aware text extraction completed",
        project_id=project_id,
        extra_data=token_info
    )

    return extracted_text, included_pages, token_info


def add_pages_with_token_limit(
    valid_page_numbers: List[int],
    extracted_text_per_page: List[str],
    available_tokens: int,
    model: str,
    page_number: Optional[str] = None
) -> tuple:
    """Add pages incrementally until token limit is reached with reference page priority"""
    from services.token_utils import get_tokens

    # Determine page priority order
    if page_number:
        # User specified pages have highest priority (reference pages)
        user_specified = expand_ranges(page_number)
        reference_pages = [p for p in user_specified if p in valid_page_numbers]
        other_pages = [p for p in valid_page_numbers if p not in reference_pages]
        priority_order = reference_pages + other_pages

        LoggingHelper.info(
            f"Reference pages detected: {reference_pages}, Other pages: {other_pages}",
            extra_data={"reference_pages": reference_pages, "other_pages": other_pages}
        )
    else:
        priority_order = valid_page_numbers

    # Incrementally add pages
    extracted_text = ""
    current_tokens = 0
    included_pages = []

    for page_num in priority_order:
        page_content = extracted_text_per_page[page_num - 1]
        page_tokens = get_tokens(page_content, model)

        if current_tokens + page_tokens <= available_tokens:
            extracted_text += page_content + "\n"
            current_tokens += page_tokens
            included_pages.append(page_num)
        else:
            # Check if this is a reference page that we must include
            if page_number and page_num in expand_ranges(page_number):
                LoggingHelper.warning(
                    f"Reference page {page_num} exceeds token limit but is user-specified",
                    extra_data={
                        "page_num": page_num,
                        "page_tokens": page_tokens,
                        "available_tokens": available_tokens,
                        "current_tokens": current_tokens
                    }
                )
                # Include it anyway (user's explicit request) - reference pages have priority
                extracted_text += page_content + "\n"
                current_tokens += page_tokens
                included_pages.append(page_num)
            else:
                # Try to fit partial content if there's some space left
                remaining_tokens = available_tokens - current_tokens
                if remaining_tokens > 500:  # Only try if we have meaningful space
                    LoggingHelper.info(
                        f"Attempting to include partial content from page {page_num}",
                        extra_data={
                            "page_num": page_num,
                            "remaining_tokens": remaining_tokens,
                            "page_tokens": page_tokens
                        }
                    )
                    # Use intelligent truncation for this page
                    import asyncio
                    truncated_content = asyncio.run(truncate_content_intelligently(
                        page_content, remaining_tokens, model
                    ))
                    extracted_text += truncated_content + "\n"
                    current_tokens += get_tokens(truncated_content, model)
                    included_pages.append(page_num)

                # Stop adding pages - we've reached the limit
                LoggingHelper.info(
                    f"Stopped adding pages at page {page_num} due to token limit",
                    extra_data={
                        "page_num": page_num,
                        "page_tokens": page_tokens,
                        "available_tokens": available_tokens,
                        "current_tokens": current_tokens
                    }
                )
                break

    # Log final page inclusion summary
    excluded_pages = [p for p in valid_page_numbers if p not in included_pages]
    if excluded_pages:
        LoggingHelper.warning(
            f"Pages excluded due to token limits: {excluded_pages}",
            extra_data={
                "excluded_pages": excluded_pages,
                "included_pages": included_pages,
                "total_pages_available": len(valid_page_numbers),
                "pages_included": len(included_pages),
                "final_token_count": current_tokens,
                "available_tokens": available_tokens,
                "token_utilization": f"{(current_tokens/available_tokens)*100:.1f}%" if available_tokens > 0 else "N/A"
            }
        )

    return extracted_text, included_pages, current_tokens


async def extract_text_from_existing_project(project_data: dict, page_number: Optional[str], project_id, file_name,
                                           user_prompt: str = "", mongo_manager=None, model: str = ""):
        # Extract text from the MongoDB data
        # file_context=utils.get_numpy_file_context(config.pdf_file_path+"\\"+file_name.split(".")[0]+".npy")

        full_text = project_data.get("full_text", [])
        total_pages = len(full_text)
        # print("Total pages from DB",total_pages)

        if page_number:
            page_numbers = expand_ranges(page_number)
            valid_page_numbers = [page_num for page_num in page_numbers if 1 <= page_num <= total_pages]
        else:
            valid_page_numbers = list(range(1, total_pages + 1))

        if not valid_page_numbers:
            valid_page_numbers = list(range(1, total_pages + 1))

        # Apply token-aware extraction if parameters are provided
        if user_prompt and mongo_manager and model and full_text:
            try:
                # Use full_text as extracted_text_per_page for existing projects
                extracted_text, included_pages, token_info = await extract_text_with_token_limit(
                    valid_page_numbers, full_text, user_prompt,
                    project_id, mongo_manager, model, page_number
                )

                # Get other project data
                previous_responses = project_data.get("chat_response", "")
                previous_prompt = project_data.get("chat_prompt", "")
                previous_intent = project_data.get("intent", "")
                previous_responses = project_data.get("response", "")
                previous_prompt = project_data.get("prompt", "")
                previous_intent = project_data.get("file_detail", "")
                previous_intent = {
                    file_detail['file_name']: file_detail.get('file_description', {})
                    for file_detail in project_data.get('file_details', [])}

                return included_pages, previous_responses, previous_prompt, previous_intent, token_info
            except Exception as e:
                LoggingHelper.warning(
                    f"Token-aware extraction failed for existing project, falling back to original method: {str(e)}",
                    project_id=project_id,
                    extra_data={"error": str(e), "file_name": file_name}
                )
                # Fall back to original behavior

        # Original behavior for backward compatibility
        previous_responses = project_data.get("chat_response", "")
        previous_prompt = project_data.get("chat_prompt", "")

        previous_intent = project_data.get("intent", "")
        previous_responses = project_data.get("response", "")
        previous_prompt = project_data.get("prompt", "")
        previous_intent = project_data.get("file_detail", "")
        previous_intent = {
        file_detail['file_name']: file_detail.get('file_description', {})
        for file_detail in project_data.get('file_details', [])}
        return valid_page_numbers, previous_responses, previous_prompt, previous_intent


async def extract_text_from_image(image_path):
    """Extract text from an image using OCR (Tesseract)."""
    try:
        # PIL's Image.open doesn't have an async version, but we can read the file async first
        async with aiofiles.open(image_path, "rb") as f:
            image_data = await f.read()

        def _extract_text():
            with Image.open(BytesIO(image_data)) as img:
                if is_blank_image(img):
                    return ""  # Skip blank images
                return pytesseract.image_to_string(img).strip()

        # Run OCR in a thread pool to avoid blocking
        return await asyncio.to_thread(_extract_text)
    except Exception as e:
        return f"Error processing image: {e}"


async def extract_text_from_image_vision(image_path, project_model=None, token_logger=None, user_id=None, project_id=None):
    """
    Extract text from an image using vision models with proper token tracking.
    Now uses the unified LLM service for consistency and token tracking.
    Includes proactive token management for large images.
    """
    try:
        # Get provider-appropriate vision model first
        if project_model:
            from services.llm_service import get_provider_vision_model, create_llm_service, get_model_context_window
            vision_model = get_provider_vision_model(project_model)
            LoggingHelper.info(f"Using provider vision model: {vision_model} (project model: {project_model})",
                              user_id=user_id, project_id=project_id, logger_name="text_extraction")
        else:
            from services.llm_service import create_llm_service, get_model_context_window
            vision_model = config.DEFAULT_VISION_MODEL
            LoggingHelper.info(f"Using default vision model: {vision_model}",
                              user_id=user_id, project_id=project_id, logger_name="text_extraction")

        # Get vision model context window and calculate safe limits
        context_window = get_model_context_window(vision_model)
        safety_margin = 5000
        safe_limit = context_window - safety_margin

        # Calculate available tokens for image
        from services.token_utils import get_tokens
        text_prompt_tokens = get_tokens(text_extraction_prompt, vision_model)
        available_image_tokens = safe_limit - text_prompt_tokens - 1000  # Extra safety buffer

        # Encode image with token-aware compression
        base64_image = await encode_image(image_path, max_tokens=available_image_tokens)

        # Get more accurate token estimation based on actual base64 size
        from services.image_utils import estimate_image_tokens
        estimated_image_tokens = estimate_image_tokens(len(base64_image) * 3 // 4)

        total_estimated_tokens = text_prompt_tokens + estimated_image_tokens

        # Final check with more accurate estimation
        if total_estimated_tokens > safe_limit:
            LoggingHelper.warning(
                f"Vision model request still exceeds safe limits after compression "
                f"({total_estimated_tokens:,} > {safe_limit:,}), falling back to OCR",
                user_id=user_id, project_id=project_id, logger_name="text_extraction",
                extra_data={
                    "estimated_tokens": total_estimated_tokens,
                    "safe_limit": safe_limit,
                    "context_window": context_window,
                    "image_tokens": estimated_image_tokens,
                    "text_tokens": text_prompt_tokens,
                    "image_path": image_path,
                    "base64_size": len(base64_image)
                }
            )
            # Fallback to OCR instead of failing
            return await extract_text_from_image(image_path)

        # Log successful token estimation
        LoggingHelper.info(
            f"Vision model request within safe limits: {total_estimated_tokens:,} tokens "
            f"(limit: {safe_limit:,})",
            user_id=user_id, project_id=project_id, logger_name="text_extraction",
            extra_data={
                "estimated_tokens": total_estimated_tokens,
                "image_tokens": estimated_image_tokens,
                "text_tokens": text_prompt_tokens
            }
        )

        # Create LLM service instance for vision model
        llm_service = await create_llm_service(vision_model)

        # Set token logger if provided (for backward compatibility)
        if token_logger:
            llm_service.set_token_logger(token_logger)
            # Also set as unified logger if it's a UnifiedUsageLogger instance
            if hasattr(token_logger, 'log_vision_usage'):
                llm_service.set_unified_logger(token_logger)

        # Create multimodal message using LangChain format
        from langchain_core.messages import HumanMessage

        # Create multimodal content for LangChain using correct format
        message_content = [
            {
                "type": "text",
                "text": text_extraction_prompt
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                }
            }
        ]

        # Create HumanMessage with multimodal content
        message = HumanMessage(content=message_content)

        # Make the LLM call with proper token tracking
        response = await llm_service.chat_completion(
            messages=[message],
            operation_type="image_text_extraction",
            operation_subtype="vision_model",
            operation_details={
                "image_path": image_path,
                "vision_model": vision_model,
                "project_model": project_model
            }
        )

        LoggingHelper.info(f"Successfully extracted text from image using vision model",
                          user_id=user_id, project_id=project_id, logger_name="text_extraction",
                          extra_data={"vision_model": vision_model, "text_length": len(response)})

        return response
    except Exception as e:
        LoggingHelper.error(f"Error extracting text from image using vision model: {str(e)}",
                           user_id=user_id, project_id=project_id, logger_name="text_extraction")
        handle_openai_error(e)


async def extract_images(doc, page_number, TEMP_DIR):
    """Extract images from a PDF page."""
    # print("__________________extrcated images__________________function call called______")
    LoggingHelper.info("Extracting images from page, extracting_images function called", logger_name="text_extraction")
    images = []
    page = doc[page_number - 1]
    LoggingHelper.info("Extracting images from page {page}", logger_name="text_extraction")

    # Wrap image extraction in asyncio.to_thread to avoid blocking
    def _extract_images_sync():
        import uuid
        session_id = uuid.uuid4().hex[:8]  # Short unique session ID
        extracted_images = []
        for img_index, img in enumerate(page.get_images(full=True), start=1):
            xref = img[0]
            base_image = doc.extract_image(xref)
            image_bytes = base_image["image"]
            # Use unique filename to prevent collisions
            image_path = os.path.join(TEMP_DIR, f"page_{page_number}_img_{img_index}_{session_id}.png")
            extracted_images.append((image_path, image_bytes))
        return extracted_images

    extracted_images = await asyncio.to_thread(_extract_images_sync)

    # Write images asynchronously
    for image_path, image_bytes in extracted_images:
        async with aiofiles.open(image_path, "wb") as img_file:
            await img_file.write(image_bytes)
        images.append(image_path)

    return images

async def process_pdf_page(page, doc, page_number, TEMP_DIR, project_model=None, token_logger=None, user_id=None, project_id=None):
    """Process a single PDF page for text, tables, and images."""
    LoggingHelper.info("Inside process_pdf_page function", user_id=user_id, project_id=project_id, logger_name="text_extraction")
    page_content = []
    # print("calling my new funtion.................................")

    # Extract text asynchronously
    def _extract_text():
        return page.extract_text()

    text = await asyncio.to_thread(_extract_text)
    if text:
        page_content.append(text)

    # Extract images
    if ocr:
        LoggingHelper.info("Extracting images from page, ocr is active", user_id=user_id, project_id=project_id, logger_name="text_extraction")
        # print("OCR IS ACTIVE AND RUNNING")
        images = await extract_images(doc, page_number, TEMP_DIR)
        LoggingHelper.info(f"Extracted {len(images)} images for page {page_number}", user_id=user_id, project_id=project_id, logger_name="text_extraction")

        for img_index, image_path in enumerate(images, start=1):
            if vision:
                LoggingHelper.info("Extracting images from page, vision is active", user_id=user_id, project_id=project_id, logger_name="text_extraction")
                # print("VISION IS ACTIVE")
                image_text = await extract_text_from_image_vision(
                    image_path,
                    project_model=project_model,
                    token_logger=token_logger,
                    user_id=user_id,
                    project_id=project_id
                )
            else:
                LoggingHelper.info("Extracting images from page, vision is inactive", user_id=user_id, project_id=project_id, logger_name="text_extraction")
                # print("VISION IS INACTIVE")
                image_text = await extract_text_from_image(image_path)
            if image_text:
                LoggingHelper.info(f"Extracted images from page, first 100 characters of image text is : {image_text[:100]}", user_id=user_id, project_id=project_id, logger_name="text_extraction")
                # print(f"___________here is the image text === :: {image_text}")
                page_content.append(f"\n[Image {img_index} on Page {page_number}]\n{image_text}\n")
    # print(f"page_content  {page_content}")
    LoggingHelper.info(f"First 100 characters of page_content is : {str(page_content)[:100]}", user_id=user_id, project_id=project_id, logger_name="text_extraction")
    return "\n".join(page_content)

async def process_pdf(pdf_path, page_number, TEMP_DIR, project_model=None, token_logger=None,
                     user_id=None, project_id=None, user_prompt: str = "", mongo_manager=None):
    """Process the entire PDF and extract its contents with optional token-aware extraction."""
    output_text = []
    try:
        # Read the file content asynchronously first
        async with aiofiles.open(pdf_path, 'rb') as f:
            pdf_data = await f.read()

        # Use BytesIO to work with the data in memory
        pdf_io = BytesIO(pdf_data)

        # Wrap document opening in asyncio.to_thread to avoid blocking
        def _open_documents():
            doc = fitz.open(stream=pdf_data, filetype="pdf")
            pdf = pdfplumber.open(pdf_io)
            return doc, pdf

        doc, pdf = await asyncio.to_thread(_open_documents)
        total_pages = doc.page_count

        try:
            # extracted_text_per_page = [page.extract_text() for page in pdf.pages]
            if page_number:
                page_numbers = expand_ranges(page_number)
                valid_page_numbers = [page_num for page_num in page_numbers if 1 <= page_num <= total_pages]
            else:
                valid_page_numbers = []

            if not valid_page_numbers:
                # valid_page_numbers = list(range(1, min(self.page_limit + 1, total_pages + 1)))
                valid_page_numbers = list(range(1, min(page_limit + 1, total_pages + 1)))

            extracted_text_per_page = []
            for page_number, page in enumerate(pdf.pages, start=1):
                # print(f"Processing page {page_number}...")
                LoggingHelper.info(f"Processing page {page_number}...", user_id=user_id, project_id=project_id, logger_name="text_extraction")

                page_content = await process_pdf_page(
                    page, doc, page_number, TEMP_DIR,
                    project_model=project_model,
                    token_logger=token_logger,
                    user_id=user_id,
                    project_id=project_id
                )
                # print(f"Prociing page after {pdf.pages},{page_number}")
                LoggingHelper.info(f"Processing page after {pdf.pages},{page_number}", user_id=user_id, project_id=project_id, logger_name="text_extraction")
                extracted_text_per_page.append(page_content)
                output_text.append(page_content)
                LoggingHelper.info(f"page content trimmed to first 100 characters is : {page_content[:100]}", user_id=user_id, project_id=project_id, logger_name="text_extraction")
            # LoggingHelper.info(f"Extracted text per page is : {extracted_text_per_page}", user_id=user_id, project_id=project_id, logger_name="text_extraction")

            # Apply token-aware extraction if parameters are provided
            if user_prompt and mongo_manager and project_id:
                try:
                    # Get model for token calculation
                    model = await mongo_manager.get_model_for_project(project_id)

                    # Apply token-aware extraction
                    extracted_text, included_pages, token_info = await extract_text_with_token_limit(
                        valid_page_numbers, extracted_text_per_page, user_prompt,
                        project_id, mongo_manager, model, page_number
                    )

                    LoggingHelper.info("Token-aware extraction completed for PDF processing",
                                     user_id=user_id, project_id=project_id, extra_data=token_info)

                    return extracted_text, included_pages, extracted_text_per_page, valid_page_numbers, token_info
                except Exception as e:
                    LoggingHelper.warning(f"Token-aware extraction failed in PDF processing, using fallback: {str(e)}",
                                        user_id=user_id, project_id=project_id)
                    # Fall back to original behavior

            # Original behavior for backward compatibility
            output_text = "\n".join(output_text)
            LoggingHelper.info(f"output text is : {output_text}", user_id=user_id, project_id=project_id, logger_name="text_extraction")
            LoggingHelper.info(f"output text, valid page numbers, first 100 characters of extracted text per page, valid page numbers : {output_text, valid_page_numbers, str(extracted_text_per_page)[:100], valid_page_numbers}", user_id=user_id, project_id=project_id, logger_name="text_extraction")
            # extracted_text_per_page= ""
            # extracted_text_per_page = output_text
            return output_text, valid_page_numbers, extracted_text_per_page, valid_page_numbers
        finally:
            pdf.close()
    finally:
        if 'doc' in locals():
            doc.close()


async def upload_and_process_pdf(file: UploadFile, page_number: Optional[str], TEMP_DIR,
                                project_model=None, token_logger=None, user_id=None, project_id=None,
                                user_prompt: str = "", mongo_manager=None):
    try:
        # Ensure temp directory exists
        await async_makedirs(TEMP_DIR, exist_ok=True)

        # Create unique filename to prevent collisions
        import uuid
        unique_id = uuid.uuid4().hex
        file_extension = os.path.splitext(file.filename)[1] if file.filename else '.pdf'
        unique_filename = f"temp_{unique_id}{file_extension}"
        temp_input_path = os.path.join(TEMP_DIR, unique_filename)

        file_content = await file.read()

        async with aiofiles.open(temp_input_path, "wb") as temp_file:
            await temp_file.write(file_content)

        # Process the PDF with token tracking support
        result = await process_pdf(
            temp_input_path, page_number, TEMP_DIR,
            project_model=project_model,
            token_logger=token_logger,
            user_id=user_id,
            project_id=project_id,
            user_prompt=user_prompt,
            mongo_manager=mongo_manager
        )

        # Clean up the temporary file
        await async_remove(temp_input_path)

        # Return the result (either 4 or 5 values depending on token-aware extraction)
        return result
    except Exception as e:
        # Return 4 values to match the expected unpacking, with error information
        error_message = f"Error processing PDF: {str(e)}"
        return error_message, [], [], []




def save_table_as_markdown(table, page_number, table_index):
    """Save a table as a Markdown file if it contains valid data."""
    if not table or len(table) <= 1:  # Check if the table is empty or has only headers
        return ""  # Skip empty tables

    markdown_table = []
    header = table[0]
    markdown_table.append("| " + " | ".join(map(str, header)) + " |")
    markdown_table.append("|" + " --- |" * len(header))
    for row in table[1:]:
        if any(cell.strip() for cell in row):  # Ensure at least one cell has data
            markdown_table.append("| " + " | ".join(map(str, row)) + " |")

    # Join the table rows into Markdown format
    return "\n".join(markdown_table)

def expand_ranges(input_str: str):
    result = []
    parts = input_str.split(",")
    for part in parts:
        if '-' in part:
            start, end = map(int, part.split('-'))
            result.extend(range(start, end + 1))
        else:
            result.append(int(part))
    return result if input_str else None

def convert_page_numbers_to_string(page_numbers: List[int]):
        # Convert the list of page numbers to a string with ranges
        if not page_numbers:
            return ''

        ranges = []
        start = page_numbers[0]
        end = page_numbers[0]

        for i in range(1, len(page_numbers)):
            if page_numbers[i] == end + 1:
                end = page_numbers[i]
            else:
                if start == end:
                    ranges.append(f"{start}")
                else:
                    ranges.append(f"{start}-{end}")
                start = end = page_numbers[i]

        if start == end:
            ranges.append(f"{start}")
        else:
            ranges.append(f"{start}-{end}")

        return ",".join(ranges)
