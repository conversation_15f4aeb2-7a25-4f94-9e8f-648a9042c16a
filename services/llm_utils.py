"""
LangChain-based LLM utility functions to replace OpenAI-specific implementations.
These functions provide the same interface but use the unified LLM service.
"""

import logging
import json
import tiktoken
import asyncio
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, AsyncGenerator
from bson import ObjectId

from services.llm_service import create_llm_service
from services.logging_utils import LoggingHelper
from services.unified_usage_tracking import UnifiedUsageLogger
from services.token_utils import get_tokens
from services import prompts
from models import insight_model
from exceptions.api_exceptions import handle_llm_error
import config

logger = logging.getLogger(__name__)

def _get_context_or_fallback():
    """Get user/project context with fallback to None for error handling."""
    try:
        from services.logging_middleware import get_current_user_id, get_current_project_id
        return get_current_user_id(), get_current_project_id()
    except:
        return None, None


async def get_intent(mongo_manager, openai_client, context: str, project_id: str, token_logger: Optional[UnifiedUsageLogger] = None, unified_logger: Optional[UnifiedUsageLogger] = None) -> Dict[str, Any]:
    """
    Get intent from document context using unified LLM service.
    Uses economy model from the same provider for cost optimization.
    Replaces the OpenAI-specific get_intent function.
    """
    try:
        # Get project model and use economy model from same provider
        project_model = await mongo_manager.get_model_for_project(project_id)
        from services.llm_service import get_provider_economy_model
        economy_model = get_provider_economy_model(project_model)

        llm_service = await create_llm_service(economy_model)

        # Set token logger if provided
        if token_logger:
            llm_service.set_token_logger(token_logger)

        # Set unified logger if provided (NEW SYSTEM)
        if unified_logger:
            llm_service.set_unified_logger(unified_logger)

        insight_prompt = prompts.get_intent_of_doc(context)
        messages = [{"role": "system", "content": insight_prompt}]

        LoggingHelper.info("Getting intent using economy model: {economy_model} (project model: {project_model})", project_id=project_id)

        # Use Pydantic model for structured output with token tracking
        from models.insight_model import DocumentIntentExtraction
        response = await llm_service.structured_output(
            messages=messages,
            pydantic_model=DocumentIntentExtraction,
            temperature=0,
            operation_type="intent_generation",
            operation_subtype="file_upload",
            operation_details={
                "context_length": len(context),
                "project_model": project_model,
                "economy_model": economy_model,
                "file_type": "document"
            }
        )

        # Convert Pydantic model to dict for MongoDB compatibility
        if hasattr(response, 'model_dump'):
            return response.model_dump()
        elif hasattr(response, 'dict'):
            # Keep for backward compatibility with older Pydantic versions
            return response.dict()
        else:
            return response

    except Exception as e:
        user_id, project_id_context = _get_context_or_fallback()
        LoggingHelper.error("Error in get_intent: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)
        handle_llm_error(e)


async def call_llm(prompt: str, project_id: str, mongo_manager, token_logger: Optional[UnifiedUsageLogger] = None, unified_logger: Optional[UnifiedUsageLogger] = None) -> Dict[str, Any]:
    """
    Call LLM with structured output using unified LLM service.
    Uses economy model from the same provider for cost optimization.
    Replaces the OpenAI-specific call_llm function.
    """
    try:
        # Get project model and use economy model from same provider
        project_model = await mongo_manager.get_model_for_project(project_id)
        from services.llm_service import get_provider_economy_model
        economy_model = get_provider_economy_model(project_model)

        llm_service = await create_llm_service(economy_model)

        # Set token logger if provided
        if token_logger:
            llm_service.set_token_logger(token_logger)

        # Set unified logger if provided (NEW SYSTEM)
        if unified_logger:
            llm_service.set_unified_logger(unified_logger)

        LoggingHelper.info("Calling LLM for source selection using economy model: {economy_model} (project model: {project_model})", project_id=project_id)

        messages = [{"role": "system", "content": prompt}]

        # Use structured output with Pydantic model and token tracking
        response = await llm_service.structured_output(
            messages=messages,
            pydantic_model=insight_model.SourceSelecton,
            temperature=0,
            operation_type="query_routing",
            operation_subtype="source_selection",
            operation_details={
                "prompt_length": len(prompt),
                "project_model": project_model,
                "economy_model": economy_model
            }
        )

        # Convert Pydantic model to dict for backward compatibility
        if hasattr(response, 'model_dump'):
            return response.model_dump()
        elif hasattr(response, 'dict'):
            # Keep for backward compatibility with older Pydantic versions
            return response.dict()
        else:
            return response

    except Exception as e:
        user_id, project_id_context = _get_context_or_fallback()
        LoggingHelper.error("Error in call_llm: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)
        handle_llm_error(e)


async def get_compliance_report(prompt: str, project_id: str = None, mongo_manager=None, token_logger: Optional[UnifiedUsageLogger] = None, unified_logger: Optional[UnifiedUsageLogger] = None) -> str:
    """
    Get compliance report using unified LLM service.
    Uses economy model from the same provider for cost optimization.
    Replaces the OpenAI-specific get_compliance_report function.
    """
    try:
        # Use project-specific economy model if available
        if project_id and mongo_manager:
            project_model = await mongo_manager.get_model_for_project(project_id)
            from services.llm_service import get_provider_economy_model
            model = get_provider_economy_model(project_model)
            LoggingHelper.info("Generating compliance report using economy model: {model} (project model: {project_model})", project_id=project_id)
        else:
            model = config.DEFAULT_MODEL
            LoggingHelper.info("Generating compliance report using default model: {model}", project_id=project_id)

        llm_service = await create_llm_service(model)

        # Set token logger if provided
        if token_logger:
            llm_service.set_token_logger(token_logger)

        # Set unified logger if provided (NEW SYSTEM)
        if unified_logger:
            llm_service.set_unified_logger(unified_logger)

        messages = [{"role": "system", "content": prompt}]

        # Use Pydantic model for structured output
        from models.insight_model import ComplianceReport
        response = await llm_service.structured_output(
            messages=messages,
            pydantic_model=ComplianceReport,
            temperature=0,
            operation_type="compliance_report",
            operation_subtype="generation",
            operation_details={
                "prompt_length": len(prompt),
                "model": model,
                "project_id": project_id
            }
        )

        # Return as JSON string for backward compatibility
        return json.dumps(response) if isinstance(response, dict) else response

    except Exception as e:
        user_id, project_id_context = _get_context_or_fallback()
        LoggingHelper.error("Error in get_compliance_report: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)
        handle_llm_error(e)


async def generate_title(prompt: str, summary: str, project_id: str = None, mongo_manager=None, token_logger: Optional[UnifiedUsageLogger] = None, unified_logger: Optional[UnifiedUsageLogger] = None) -> str:
    """
    Generate title using unified LLM service.
    Uses economy model from the same provider for cost optimization.
    Replaces the OpenAI-specific generate_title function.
    """
    try:
        title_prompt = prompts.get_title_prompt(prompt, summary)

        # Use project-specific economy model if available
        if project_id and mongo_manager:
            project_model = await mongo_manager.get_model_for_project(project_id)
            from services.llm_service import get_provider_economy_model
            model = get_provider_economy_model(project_model)
            LoggingHelper.info("Generating title using economy model: {model} (project model: {project_model})", project_id=project_id)
        else:
            model = config.DEFAULT_TITLE_GENERATION_MODEL
            LoggingHelper.info("Generating title using default model: {model}", project_id=project_id)

        llm_service = await create_llm_service(model)

        # Set token logger if provided
        if token_logger:
            llm_service.set_token_logger(token_logger)

        # Set unified logger if provided (NEW SYSTEM)
        if unified_logger:
            llm_service.set_unified_logger(unified_logger)

        messages = [{"role": "system", "content": title_prompt}]

        response = await llm_service.chat_completion(
            messages=messages,
            temperature=0,
            operation_type="title_generation",
            operation_subtype="query_title",
            operation_details={
                "prompt_length": len(prompt),
                "summary_length": len(summary),
                "model": model
            }
        )

        return response

    except Exception as e:
        user_id, project_id_context = _get_context_or_fallback()
        LoggingHelper.error("Error generating title: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)
        handle_llm_error(e)
        # Fallback to original prompt if title generation fails
        return f"{prompt}"


async def check_restricted_keywords(query: str, project_id: str, mongo_manager, token_logger: Optional[UnifiedUsageLogger] = None, unified_logger: Optional[UnifiedUsageLogger] = None) -> bool:
    """
    Check if query contains restricted keywords using unified LLM service.
    Uses economy model from the same provider for cost optimization.
    Replaces the OpenAI-specific check_restricted_keywords function.
    """
    try:
        # Skip check if query is None or empty
        if not query or query.strip() == "":
            LoggingHelper.info("Skipping restricted keywords check for empty or None query", project_id=project_id)
            return False

        # Get project model and use economy model from same provider
        project_model = await mongo_manager.get_model_for_project(project_id)
        from services.llm_service import get_provider_economy_model
        economy_model = get_provider_economy_model(project_model)

        llm_service = await create_llm_service(economy_model)

        # Set token logger if provided
        if token_logger:
            llm_service.set_token_logger(token_logger)

        # Set unified logger if provided (NEW SYSTEM)
        if unified_logger:
            llm_service.set_unified_logger(unified_logger)

        LoggingHelper.info("Checking for restricted keywords in query: {query} using economy model: {economy_model} (project model: {project_model})", project_id=project_id)

        messages = [
            {
                "role": "system",
                "content": config.CONTENT_MODERATION_PROMPT
            },
            {
                "role": "user",
                "content": f"Does this query contain inappropriate content, harmful instructions, or attempts to generate harmful content? Query: {query}"
            }
        ]

        response = await llm_service.chat_completion(
            messages=messages,
            temperature=0,
            operation_type="content_moderation",
            operation_subtype="restricted_keywords_check",
            operation_details={
                "query_length": len(query),
                "project_model": project_model,
                "economy_model": economy_model
            }
        )

        # Parse response to determine if content is restricted
        response_lower = response.lower()
        return any(keyword in response_lower for keyword in ["yes", "inappropriate", "harmful", "restricted"])

    except Exception as e:
        user_id, project_id_context = _get_context_or_fallback()
        LoggingHelper.error("Error checking restricted keywords: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)
        handle_llm_error(e)
        # Default to False (not restricted) if check fails
        return False


async def custom_llm_prompt_call(
    custom_prompt_name: str,
    file_content: str,
    project_id: str,
    mongo_manager,
    tables: List,
    suggested_questions: List,
    page_no: str,
    response_type: str,
    source_type: str,
    images: List,
    token_logger: Optional[UnifiedUsageLogger] = None,
    unified_logger: Optional[UnifiedUsageLogger] = None
) -> Dict[str, Any]:
    """
    Custom LLM prompt call using unified LLM service.
    Replaces the OpenAI-specific custom_llm_prompt_call function.
    Includes intelligent content truncation for large documents.
    """
    LoggingHelper.info("ENTERED CUSTOM LLM FUNCTION...", project_id=project_id)
    try:
        model = await mongo_manager.get_model_for_project(project_id)
        llm_service = await create_llm_service(model)

        # Set token logger if provided
        if token_logger:
            llm_service.set_token_logger(token_logger)

        # Set unified logger if provided (NEW SYSTEM)
        if unified_logger:
            llm_service.set_unified_logger(unified_logger)

        # Variables to store category and subcategory
        category = None
        subcategory = None

        # Store the original custom_prompt_name for later use
        original_custom_prompt_name = custom_prompt_name

        # Check if custom_prompt_name has the format "category|||subcategory|||prompt_text"
        if "|||" in custom_prompt_name:
            # Split the string by |||
            parts = custom_prompt_name.split("|||")
            if len(parts) >= 3:
                category = parts[0].strip()
                subcategory = parts[1].strip()
                prompt_text = parts[2].strip()
            elif len(parts) == 2:
                category = parts[0].strip()
                prompt_text = parts[1].strip()
            else:
                prompt_text = custom_prompt_name
        else:
            prompt_text = custom_prompt_name

        # Get context window and calculate safe limits
        from services.llm_service import get_model_context_window
        context_window = get_model_context_window(model)
        safety_margin = 5000  # Reserve tokens for response and safety
        max_safe_tokens = context_window - safety_margin
        
        # Build the prompt template without content to estimate its token usage
        if not category:
            # No category specified, use generic prompt
            prompt_template = f"""Your name is Neuquip Buddy and you are an expert analyst. Your task is to:

{prompt_text}

Here is the content to analyze:

[CONTENT_PLACEHOLDER]

Provide a detailed, professional analysis."""
        else:
            # Include category and subcategory in the prompt
            domain_expertise = f"{category}" if category else ""
            if subcategory:
                domain_expertise += f" specializing in {subcategory}"

            # Only add domain expertise if it exists
            if domain_expertise:
                prompt_template = f"""Your name is Neuquip Buddy and you are an expert {domain_expertise} analyst. Your task is to:

{prompt_text}

Here is the content to analyze:

[CONTENT_PLACEHOLDER]

Provide a detailed, professional analysis from a {domain_expertise} perspective."""
            else:
                prompt_template = f"""Your name is Neuquip Buddy and you are an expert analyst. Your task is to:

{prompt_text}

Here is the content to analyze:

[CONTENT_PLACEHOLDER]

Provide a detailed, professional analysis."""
                
        # Calculate tokens for prompt template (without actual content)
        from services.token_utils import get_tokens
        template_tokens = get_tokens(prompt_template.replace("[CONTENT_PLACEHOLDER]", ""), model)
        
        # Calculate available tokens for content
        available_for_content = max_safe_tokens - template_tokens
        
        # Check if file content needs truncation
        content_tokens = get_tokens(file_content, model)
        if content_tokens > available_for_content:
            LoggingHelper.warning(
                f"Content exceeds available tokens ({content_tokens} > {available_for_content}), applying intelligent truncation",
                project_id=project_id,
                extra_data={
                    "original_tokens": content_tokens,
                    "available_tokens": available_for_content,
                    "context_window": context_window,
                    "safety_margin": safety_margin,
                    "custom_prompt": original_custom_prompt_name
                }
            )
            
            # Import and use the intelligent truncation utility
            from services.text_extraction_utils import truncate_content_intelligently
            truncated_content = await truncate_content_intelligently(
                file_content, available_for_content, model
            )
            
            # Use truncated content
            file_content = truncated_content
            
            LoggingHelper.info(
                f"Content truncated from {content_tokens} to {get_tokens(file_content, model)} tokens",
                project_id=project_id,
                extra_data={"custom_prompt": original_custom_prompt_name}
            )

        # Build the full prompt with actual content
        full_prompt = prompt_template.replace("[CONTENT_PLACEHOLDER]", file_content)

        # Calculate input tokens for final prompt
        input_tokens_usage = get_tokens(full_prompt)

        messages = [{"role": "system", "content": full_prompt}]

        response = await llm_service.chat_completion(
            messages=messages,
            temperature=0,
            operation_type="custom_prompt",
            operation_subtype="completion",
            operation_details={
                "custom_prompt_name": custom_prompt_name,
                "file_content_length": len(file_content),
                "response_type": response_type,
                "source_type": source_type,
                "model": model
            }
        )

        # Add the title to the summary
        # query_title = await generate_title(prompt_text, response, project_id, mongo_manager, token_logger)

        # Since we now include title in the summary itself, we don't need separate title generation
        # llm_response = f"**{query_title}**\n\n{response}"
        llm_response = response
        output_tokens_usage = get_tokens(llm_response)

        # Generate a response ID and get object ID
        response_id = str(ObjectId())
        object_id = str(await mongo_manager.get_project_object_id(project_id))

        # Link token logger with response_id for proper tracking
        if token_logger:
            token_logger.set_response_id(response_id)
            LoggingHelper.info("Linked token events with response_id: {response_id}", project_id=project_id)

        return {
            "summary": llm_response,
            "tables": tables if tables else [],  # Keep as empty list to match InsightExtraction model
            "suggested_questions": suggested_questions,
            "page_no": page_no,
            "images": "",
            "input_tokens_used": input_tokens_usage,
            "output_tokens_used": output_tokens_usage,
            "response_type": response_type,
            "source_type": source_type,
            "object_id": project_id,
            "response_id": response_id,
            "response_time": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        user_id, project_id_context = _get_context_or_fallback()
        LoggingHelper.error("Error in custom_llm_prompt_call: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)
        handle_llm_error(e)


async def fake_llm_resonse(
    prompt: str,
    project_id: str,
    mongo_manager,
    extracted_text: str,
    chat_history: List,
    valid_page_numbers: List,
    response_type: str,
    visualization: str,
    current_date,
    source_type: str = "web search",
    token_logger: Optional[UnifiedUsageLogger] = None,
    user_id: str = None,
    unified_logger: Optional[UnifiedUsageLogger] = None
) -> AsyncGenerator[str, None]:
    """
    Real-time streaming LLM response using unified LLM service.
    Now uses real streaming instead of simulated chunking.
    Note: Function name has intentional typo to match original implementation.
    """
    try:
        # Log streaming start for non-data analysis (fake_llm_resonse)
        LoggingHelper.info("STREAMING_START - Non-data analysis real-time streaming initiated",
                          user_id=user_id, project_id=project_id,
                          extra_data={
                              "stream_type": "non_data_analysis_realtime",
                              "function": "fake_llm_resonse",
                              "source_type": source_type,
                              "prompt_length": len(prompt) if prompt else 0,
                              "extracted_text_length": len(str(extracted_text)) if extracted_text else 0,
                              "chat_history_length": len(chat_history) if chat_history else 0,
                              "response_type": response_type,
                              "visualization": visualization
                          })

        insight_prompt = prompts.get_insight_prompt(
            current_date, extracted_text, chat_history, prompt, response_type, visualization
        )

        input_tokens_used = get_tokens(insight_prompt)
        model = await mongo_manager.get_model_for_project(project_id)
        llm_service = await create_llm_service(model)

        # Set token logger if provided
        if token_logger:
            llm_service.set_token_logger(token_logger)

        # Set unified logger if provided (NEW SYSTEM)
        if unified_logger:
            llm_service.set_unified_logger(unified_logger)

        LoggingHelper.info("Starting real-time insight generation", user_id=user_id, project_id=project_id)

        messages = [{"role": "system", "content": insight_prompt}]

        # Stream LLM response in real-time
        accumulated_content = ""
        summary_content = ""
        streaming_summary = False
        summary_complete = False
        chunk_buffer = ""
        chunk_count = 0
        total_chunks_received = 0
        
        try:
            async for chunk in llm_service.stream_completion(
                messages,
                operation_type="insight_generation",
                operation_subtype="realtime_streaming",
                operation_details={
                    "prompt_length": len(prompt),
                    "extracted_text_length": len(str(extracted_text)),
                    "chat_history_length": len(chat_history),
                    "response_type": response_type,
                    "source_type": source_type,
                    "model": model
                },
                temperature=0
            ):
                # Debug: Track chunk reception
                total_chunks_received += 1
                chunk_len = len(chunk) if chunk else 0
                
                if total_chunks_received <= 5:  # Log first 5 chunks for debugging
                    LoggingHelper.info(f"Received chunk #{total_chunks_received}", user_id=user_id, project_id=project_id,
                                     extra_data={"chunk_length": chunk_len, "chunk_preview": chunk[:100] if chunk else "EMPTY"})
                
                # Accumulate content for final structured parsing
                accumulated_content += chunk
                
                # Skip streaming if summary is already complete
                if summary_complete:
                    continue
                
                # Buffer chunks for better pattern detection
                chunk_buffer += chunk
                chunk_count += 1
                
                # Process every 3 chunks or when we have substantial content
                if chunk_count >= 3 or len(chunk_buffer) > 50:
                    # Check if we should start streaming (look for title start)
                    if not streaming_summary and '**' in accumulated_content:
                        title_start_idx = accumulated_content.find('**')
                        if title_start_idx != -1:
                            # Start streaming from the title
                            streaming_summary = True
                            summary_content_from_title = accumulated_content[title_start_idx:]
                            
                            # Extract and yield clean content
                            clean_content = _extract_clean_summary_content_with_title(summary_content_from_title)
                            if clean_content:
                                summary_content = clean_content  # Track what we've already streamed
                                yield json.dumps({
                                    "type": "message",
                                    "content": clean_content
                                })
                    
                    # Continue streaming if we're already streaming
                    elif streaming_summary:
                        # Check if we've reached the end of summary field
                        end_patterns = ['", "tables"', '","tables"', '", "suggested_questions"', '","suggested_questions"', '"},', '"}', '"\n}', '",\n']
                        summary_ended = any(pattern in accumulated_content for pattern in end_patterns)
                        
                        if summary_ended:
                            # Extract final summary and stream only the new part
                            summary_complete = True
                            final_summary = _extract_final_summary_content(accumulated_content)
                            
                            # Only stream content we haven't streamed yet
                            if len(final_summary) > len(summary_content):
                                remaining_summary = final_summary[len(summary_content):]
                                if remaining_summary.strip():
                                    yield json.dumps({
                                        "type": "message",
                                        "content": remaining_summary
                                    })
                            
                            LoggingHelper.info("Summary streaming completed - boundary detected",
                                             user_id=user_id, project_id=project_id,
                                             extra_data={"summary_length": len(final_summary)})
                        else:
                            # Continue streaming new content
                            current_summary = _extract_clean_summary_content(
                                accumulated_content[accumulated_content.find('"summary"'):] if '"summary"' in accumulated_content else accumulated_content
                            )
                            
                            # Only stream new content we haven't seen before
                            if len(current_summary) > len(summary_content):
                                new_content = current_summary[len(summary_content):]
                                
                                # Check that new content doesn't contain end markers
                                end_check_patterns = ['", "', '",\n', '"}']
                                clean_new_content = new_content
                                for pattern in end_check_patterns:
                                    if pattern in clean_new_content:
                                        clean_new_content = clean_new_content[:clean_new_content.find(pattern)]
                                        summary_complete = True
                                        break
                                
                                if clean_new_content.strip():
                                    summary_content = current_summary  # Update what we've streamed
                                    yield json.dumps({
                                        "type": "message",
                                        "content": clean_new_content
                                    })
                                
                                if summary_complete:
                                    LoggingHelper.info("Summary streaming completed - end pattern in content",
                                                     user_id=user_id, project_id=project_id)
                    
                    # Reset buffer after processing
                    chunk_buffer = ""
                    chunk_count = 0

            # Send complete signal
            yield json.dumps({"type": "complete"})

            LoggingHelper.info("Real-time streaming completed successfully", user_id=user_id, project_id=project_id,
                             extra_data={
                                 "total_content_length": len(accumulated_content), 
                                 "summary_length": len(summary_content),
                                 "streaming_summary": streaming_summary,
                                 "summary_complete": summary_complete,
                                 "content_preview": accumulated_content[:300] if accumulated_content else "EMPTY",
                                 "content_ends_with": accumulated_content[-100:] if accumulated_content else "EMPTY",
                                 "total_chunks_received": total_chunks_received
                             })

        except Exception as streaming_error:
            LoggingHelper.error(f"Real-time streaming failed: {str(streaming_error)}", user_id=user_id, project_id=project_id)
            # Re-raise the error to be handled by the outer exception handler
            raise

        # Now parse the accumulated content into structured format
        try:
            # Import InsightExtraction model for structured parsing
            from models.insight_model import InsightExtraction
            
            # Clean and validate the accumulated content before parsing
            cleaned_content = accumulated_content.strip()
            
            # Handle JSON wrapped in markdown code blocks
            if cleaned_content.startswith('```json'):
                # Remove markdown code block wrapper
                lines = cleaned_content.split('\n')
                if lines[0].strip() == '```json' and lines[-1].strip() == '```':
                    # Remove first and last lines
                    cleaned_content = '\n'.join(lines[1:-1]).strip()
                    LoggingHelper.info("Removed markdown code block wrapper from JSON", 
                                     user_id=user_id, project_id=project_id,
                                     extra_data={"original_length": len(accumulated_content), "cleaned_length": len(cleaned_content)})
                elif lines[0].strip() == '```json':
                    # Remove first line only (in case closing ``` is missing)
                    cleaned_content = '\n'.join(lines[1:]).strip()
                    # Also remove trailing ``` if present
                    if cleaned_content.endswith('```'):
                        cleaned_content = cleaned_content[:-3].strip()
                    LoggingHelper.info("Removed partial markdown code block wrapper from JSON", 
                                     user_id=user_id, project_id=project_id,
                                     extra_data={"original_length": len(accumulated_content), "cleaned_length": len(cleaned_content)})
            
            # Check if content looks like JSON
            is_json_like = cleaned_content.startswith('{') and cleaned_content.endswith('}')
            
            # Log the JSON detection check
            LoggingHelper.info("JSON detection check", 
                              user_id=user_id, project_id=project_id,
                              extra_data={
                                  "is_json_like": is_json_like,
                                  "starts_with_brace": cleaned_content.startswith('{'),
                                  "ends_with_brace": cleaned_content.endswith('}'),
                                  "content_start": cleaned_content[:20] if cleaned_content else "EMPTY",
                                  "content_end": cleaned_content[-20:] if cleaned_content else "EMPTY"
                              })
            
            # Additional validation for content that might still have markdown artifacts
            if not is_json_like and cleaned_content:
                # Try to find JSON within the content
                start_brace = cleaned_content.find('{')
                end_brace = cleaned_content.rfind('}')
                if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                    potential_json = cleaned_content[start_brace:end_brace + 1]
                    LoggingHelper.info("Extracted potential JSON from content", 
                                     user_id=user_id, project_id=project_id,
                                     extra_data={"extracted_length": len(potential_json), "original_length": len(cleaned_content)})
                    cleaned_content = potential_json
                    is_json_like = True
            
            # Try to parse the accumulated content as JSON first
            try:
                if is_json_like and cleaned_content:
                    # The LLM should have generated JSON, try to parse it
                    parsed_json = json.loads(cleaned_content)
                    
                    # Transform tables data to match expected schema
                    if "tables" in parsed_json and isinstance(parsed_json["tables"], list):
                        original_tables = parsed_json["tables"]
                        parsed_json["tables"] = _transform_table_data(parsed_json["tables"])
                        LoggingHelper.info("Transformed table data to match expected schema", 
                                          user_id=user_id, project_id=project_id,
                                          extra_data={
                                              "original_table_count": len(original_tables),
                                              "transformed_table_count": len(parsed_json["tables"]),
                                              "original_keys": [list(table.keys()) for table in original_tables] if original_tables else [],
                                              "transformed_keys": [list(table.keys()) for table in parsed_json["tables"]] if parsed_json["tables"] else []
                                          })
                    
                    # Validate against Pydantic model
                    structured_response = InsightExtraction(**parsed_json)
                    parsed_response = structured_response.model_dump()
                    
                    # DEDUPLICATION FIX: Only add title if we haven't already streamed it
                    # Check if title was already streamed (indicated by streaming_summary being True)
                    if parsed_response.get("summary") and not parsed_response["summary"].startswith("**") and not streaming_summary:
                        # Only add title if we didn't stream content (meaning no streaming occurred)
                        title_start = accumulated_content.find('**')
                        if title_start != -1:
                            # Find the end of the title
                            title_end_pattern = '**'
                            title_end = accumulated_content.find(title_end_pattern, title_start + 2)
                            if title_end != -1:
                                # Extract the title including the ** markers
                                title_with_markers = accumulated_content[title_start:title_end + 2]
                                # Add title to the beginning of summary with proper formatting
                                parsed_response["summary"] = f"{title_with_markers}\n\n{parsed_response['summary']}"
                                
                                LoggingHelper.info("Added missing title to final summary (no streaming occurred)", 
                                                 user_id=user_id, project_id=project_id,
                                                 extra_data={"title": title_with_markers, "original_summary_length": len(parsed_response["summary"])})
                    elif streaming_summary:
                        LoggingHelper.info("Skipped title addition - content was already streamed", 
                                         user_id=user_id, project_id=project_id,
                                         extra_data={"summary_length": len(parsed_response.get("summary", ""))})
                    
                    LoggingHelper.info("Successfully parsed streamed content as structured JSON", 
                                     user_id=user_id, project_id=project_id,
                                     extra_data={"summary_length": len(parsed_response.get("summary", "")),
                                               "tables_count": len(parsed_response.get("tables", [])),
                                               "questions_count": len(parsed_response.get("suggested_questions", []))})
                else:
                    # Content doesn't look like JSON, treat as plain text
                    raise json.JSONDecodeError("Content is not JSON format", cleaned_content, 0)
                    
            except (json.JSONDecodeError, Exception) as parse_error:
                LoggingHelper.warning(f"Failed to parse streamed content as JSON: {str(parse_error)}", 
                                    user_id=user_id, project_id=project_id,
                                    extra_data={"content_length": len(cleaned_content), "content_preview": cleaned_content[:200]})
                
                # Try to extract summary from malformed JSON - avoid duplication
                if streaming_summary:
                    # If we already streamed content, use that instead of re-extracting
                    extracted_summary = summary_content or _extract_final_summary_content(accumulated_content)
                    LoggingHelper.info("Using already streamed content for fallback response", 
                                     user_id=user_id, project_id=project_id,
                                     extra_data={"streamed_content_length": len(summary_content)})
                else:
                    # Content might be plain text response instead of JSON
                    if not is_json_like and cleaned_content:
                        # Treat the entire content as a plain text summary
                        LoggingHelper.info("Content appears to be plain text, not JSON - using as summary", 
                                         user_id=user_id, project_id=project_id,
                                         extra_data={"content_length": len(cleaned_content)})
                        extracted_summary = cleaned_content
                    else:
                        # Try to extract from malformed JSON
                        extracted_summary = _extract_final_summary_content_with_title_preservation(accumulated_content)
                        if not extracted_summary:
                            # Last resort: treat everything as summary but clean it up
                            extracted_summary = accumulated_content.strip()
                            # Remove obvious JSON artifacts
                            if extracted_summary.startswith('{'):
                                # Try to find just the summary part
                                import re
                                # Look for "summary": "content" pattern
                                pattern = r'"summary":\s*"([^"]*(?:\\.[^"]*)*)"'
                                match = re.search(pattern, extracted_summary)
                                if match:
                                    extracted_summary = match.group(1).replace('\\"', '"').replace('\\n', '\n')
                                else:
                                    # Fallback: just remove JSON brackets
                                    extracted_summary = accumulated_content.strip('{}').strip()
                                    # But try to preserve title if it exists
                                    title_start = extracted_summary.find('**')
                                    if title_start != -1:
                                        extracted_summary = extracted_summary[title_start:]
                
                # Fallback: create structured response with extracted content
                parsed_response = {
                    "summary": extracted_summary,
                    "tables": [],
                    "suggested_questions": [
                        f"What are the key insights from this {response_type.lower()} analysis?",
                        f"Can you elaborate on the main findings?",
                        f"What recommendations follow from this analysis?"
                    ]
                }
                
                # Try to extract and transform tables from the content if they exist
                try:
                    # Look for table data in the JSON-like parts of the content
                    if '{' in accumulated_content and '}' in accumulated_content:
                        import re
                        # Look for "tables": [...]
                        tables_pattern = r'"tables"\s*:\s*(\[.*?\])'
                        tables_match = re.search(tables_pattern, accumulated_content, re.DOTALL)
                        if tables_match:
                            tables_json = tables_match.group(1)
                            try:
                                # Try to parse and transform tables
                                tables_data = json.loads(tables_json)
                                if isinstance(tables_data, list) and tables_data:
                                    transformed_tables = _transform_table_data(tables_data)
                                    parsed_response["tables"] = transformed_tables
                                    LoggingHelper.info("Successfully extracted and transformed tables from partial JSON", 
                                                     user_id=user_id, project_id=project_id,
                                                     extra_data={"tables_count": len(transformed_tables)})
                            except json.JSONDecodeError:
                                LoggingHelper.info("Found tables section but couldn't parse as JSON", 
                                                 user_id=user_id, project_id=project_id)
                except Exception as extract_error:
                    LoggingHelper.warning(f"Error extracting tables from content: {str(extract_error)}", 
                                        user_id=user_id, project_id=project_id)
                
                LoggingHelper.info("Created fallback structured response from extracted summary", 
                                 user_id=user_id, project_id=project_id,
                                 extra_data={"summary_length": len(extracted_summary)})

        except Exception as parsing_error:
            LoggingHelper.error(f"Failed to create structured response: {str(parsing_error)}", 
                              user_id=user_id, project_id=project_id)
            # Create minimal fallback response
            clean_summary = accumulated_content.strip() if accumulated_content else "Analysis completed."
            # If we have streamed content, use that instead
            if streaming_summary and summary_content:
                clean_summary = summary_content
            
            parsed_response = {
                "summary": clean_summary,
                "tables": [],
                "suggested_questions": []
            }
            
            LoggingHelper.info("Created minimal fallback response due to parsing error", 
                             user_id=user_id, project_id=project_id,
                             extra_data={"summary_length": len(clean_summary)})

        # Calculate token usage from the accumulated response
        output_tokens_used = get_tokens(accumulated_content)

        # Add additional fields to match expected response format
        parsed_response.update({
            'page_number': convert_page_numbers_to_string(valid_page_numbers),
            'input_tokens_used': input_tokens_used,
            'output_tokens_used': output_tokens_used,
            'response_type': response_type,
            'source_type': source_type,
            'response_time': datetime.now(timezone.utc).isoformat()
        })

        # Add unique IDs to tables and ensure tables is always a list
        if parsed_response.get("tables") and isinstance(parsed_response["tables"], list) and len(parsed_response["tables"]) > 0:
            for table in parsed_response["tables"]:
                table["chart_id"] = get_unique_id()
        else:
            # Ensure tables is always an empty list, not empty string, to match InsightExtraction model
            parsed_response["tables"] = []

        # Yield final structured response
        yield json.dumps({
            "type": "final_response",
            "content": parsed_response
        })

    except Exception as e:
        LoggingHelper.error("Error in fake_llm_response: {str(e)}", user_id=user_id, project_id=project_id)
        # Instead of raising exception, yield error event to frontend
        try:
            handle_llm_error(e)
        except Exception as api_error:
            # Yield error event that frontend can handle
            yield json.dumps({
                "type": "error",
                "content": str(api_error)
            })
            return  # Stop the generator


def _extract_clean_summary_content_with_title(content: str) -> str:
    """
    Extract clean summary content including title from partial JSON.
    Handles the new format where summary starts with **Title** pattern.
    """
    if not content:
        return ""
    
    # If content starts with JSON field marker, extract the value
    if content.startswith('"summary"'):
        # Find the start of the actual content
        start_patterns = ['"summary": "', '"summary":"']
        for pattern in start_patterns:
            if content.startswith(pattern):
                content = content[len(pattern):]
                break
    
    # Remove trailing JSON syntax that might appear during streaming
    end_patterns = ['", "tables"', '","tables"', '", "suggested_questions"', '","suggested_questions"', '",', '"']
    for pattern in end_patterns:
        if content.endswith(pattern):
            content = content[:-len(pattern)]
            break
    
    # Handle escaped quotes and basic JSON unescaping
    content = content.replace('\\"', '"').replace('\\n', '\n').replace('\\t', '\t')
    
    return content.strip()


def _extract_clean_summary_content(content: str) -> str:
    """
    Extract clean summary content from partial JSON, removing JSON syntax.
    Handles cases where we have partial content during streaming.
    """
    if not content:
        return ""
    
    # Remove JSON field markers and quotes
    if content.startswith('"summary"'):
        # Find the start of the actual content
        start_patterns = ['"summary": "', '"summary":"']
        for pattern in start_patterns:
            if content.startswith(pattern):
                content = content[len(pattern):]
                break
    
    # Remove trailing JSON syntax that might appear during streaming
    end_patterns = ['", "tables"', '","tables"', '", "suggested_questions"', '","suggested_questions"', '",', '"']
    for pattern in end_patterns:
        if content.endswith(pattern):
            content = content[:-len(pattern)]
            break
    
    # Handle escaped quotes and basic JSON unescaping
    content = content.replace('\\"', '"').replace('\\n', '\n').replace('\\t', '\t')
    
    return content.strip()


def _extract_final_summary_content(full_content: str) -> str:
    """
    Extract the complete summary content from the accumulated JSON.
    More robust parsing for the final summary extraction.
    """
    try:
        # First try to parse as complete JSON
        try:
            parsed_json = json.loads(full_content.strip())
            if isinstance(parsed_json, dict) and "summary" in parsed_json:
                return parsed_json["summary"]
        except json.JSONDecodeError:
            pass  # Continue with manual extraction
        
        # Find summary field boundaries
        summary_start = None
        start_patterns = ['"summary": "', '"summary":"']
        
        for pattern in start_patterns:
            idx = full_content.find(pattern)
            if idx != -1:
                summary_start = idx + len(pattern)
                break
        
        if summary_start is None:
            return ""
        
        # Find the end of the summary field
        content_from_start = full_content[summary_start:]
        
        # Look for field boundaries (accounting for escaped quotes)
        quote_count = 0
        escaped = False
        summary_end = None
        
        for i, char in enumerate(content_from_start):
            if escaped:
                escaped = False
                continue
                
            if char == '\\':
                escaped = True
                continue
                
            if char == '"':
                # Check if this quote is followed by a comma and next field
                remaining = content_from_start[i+1:].strip()
                if remaining.startswith(',') and ('"tables"' in remaining or '"suggested_questions"' in remaining):
                    summary_end = i
                    break
                # Also check for end of object
                elif remaining.startswith('}') or remaining.startswith(',\n'):
                    summary_end = i
                    break
        
        if summary_end is not None:
            summary_content = content_from_start[:summary_end]
        else:
            # Fallback: take everything until we see another field or end of object
            end_patterns = ['", "tables"', '","tables"', '", "suggested_questions"', '","suggested_questions"', '"}', '",\n']
            for pattern in end_patterns:
                idx = content_from_start.find(pattern)
                if idx != -1:
                    summary_content = content_from_start[:idx]
                    break
            else:
                summary_content = content_from_start
        
        # Clean up the content
        cleaned = summary_content.replace('\\"', '"').replace('\\n', '\n').replace('\\t', '\t').strip()
        
        # Remove any remaining JSON artifacts
        if cleaned.endswith('",') or cleaned.endswith('"'):
            cleaned = cleaned.rstrip('",').rstrip('"')
            
        return cleaned
        
    except Exception as e:
        # Last resort fallback
        LoggingHelper.warning(f"Failed to extract final summary, using regex fallback: {str(e)}")
        import re
        # Use regex to find summary content
        pattern = r'"summary":\s*"([^"]*(?:\\.[^"]*)*)"'
        match = re.search(pattern, full_content)
        if match:
            return match.group(1).replace('\\"', '"').replace('\\n', '\n')
        return ""


def _extract_final_summary_content_with_title_preservation(full_content: str) -> str:
    """
    Extract the complete summary content from the accumulated JSON with title preservation.
    This function prioritizes finding and preserving the title from the streamed content.
    """
    try:
        # First try to parse as complete JSON
        try:
            parsed_json = json.loads(full_content.strip())
            if isinstance(parsed_json, dict) and "summary" in parsed_json:
                summary_content = parsed_json["summary"]
                
                # Check if title is missing from the summary
                if not summary_content.startswith("**"):
                    # Look for title in the full content
                    title_start = full_content.find('**')
                    if title_start != -1:
                        title_end = full_content.find('**', title_start + 2)
                        if title_end != -1:
                            title_with_markers = full_content[title_start:title_end + 2]
                            return f"{title_with_markers}\n\n{summary_content}"
                
                return summary_content
        except json.JSONDecodeError:
            pass  # Continue with manual extraction
        
        # Manual extraction with title preservation
        # First, try to find the title in the content
        title_with_markers = ""
        title_start = full_content.find('**')
        if title_start != -1:
            title_end = full_content.find('**', title_start + 2)
            if title_end != -1:
                title_with_markers = full_content[title_start:title_end + 2]
        
        # Find summary field boundaries
        summary_start = None
        start_patterns = ['"summary": "', '"summary":"']
        
        for pattern in start_patterns:
            idx = full_content.find(pattern)
            if idx != -1:
                summary_start = idx + len(pattern)
                break
        
        if summary_start is None:
            # If no summary field found, return the content from title onwards
            return full_content[title_start:] if title_start != -1 else ""
        
        # Find the end of the summary field
        content_from_start = full_content[summary_start:]
        
        # Look for field boundaries (accounting for escaped quotes)
        summary_end = None
        escaped = False
        
        for i, char in enumerate(content_from_start):
            if escaped:
                escaped = False
                continue
                
            if char == '\\':
                escaped = True
                continue
                
            if char == '"':
                # Check if this quote is followed by a comma and next field
                remaining = content_from_start[i+1:].strip()
                if remaining.startswith(',') and ('"tables"' in remaining or '"suggested_questions"' in remaining):
                    summary_end = i
                    break
                # Also check for end of object
                elif remaining.startswith('}') or remaining.startswith(',\n'):
                    summary_end = i
                    break
        
        if summary_end is not None:
            summary_content = content_from_start[:summary_end]
        else:
            # Fallback: take everything until we see another field or end of object
            end_patterns = ['", "tables"', '","tables"', '", "suggested_questions"', '","suggested_questions"', '"}', '",\n']
            for pattern in end_patterns:
                idx = content_from_start.find(pattern)
                if idx != -1:
                    summary_content = content_from_start[:idx]
                    break
            else:
                summary_content = content_from_start
        
        # Clean up the content
        cleaned = summary_content.replace('\\"', '"').replace('\\n', '\n').replace('\\t', '\t').strip()
        
        # Remove any remaining JSON artifacts
        if cleaned.endswith('",') or cleaned.endswith('"'):
            cleaned = cleaned.rstrip('",').rstrip('"')
        
        # Combine title with cleaned content if title was found and not already in content
        if title_with_markers and not cleaned.startswith("**"):
            return f"{title_with_markers}\n\n{cleaned}"
        elif cleaned:
            return cleaned
        elif title_with_markers:
            return title_with_markers
        else:
            return ""
        
    except Exception as e:
        # Last resort fallback with title preservation
        LoggingHelper.warning(f"Failed to extract final summary with title preservation, using regex fallback: {str(e)}")
        import re
        
        # Try to find title first
        title_with_markers = ""
        title_start = full_content.find('**')
        if title_start != -1:
            title_end = full_content.find('**', title_start + 2)
            if title_end != -1:
                title_with_markers = full_content[title_start:title_end + 2]
        
        # Use regex to find summary content
        pattern = r'"summary":\s*"([^"]*(?:\\.[^"]*)*)"'
        match = re.search(pattern, full_content)
        if match:
            summary_content = match.group(1).replace('\\"', '"').replace('\\n', '\n')
            if title_with_markers and not summary_content.startswith("**"):
                return f"{title_with_markers}\n\n{summary_content}"
            return summary_content
        
        # Final fallback: return title if found, otherwise empty
        return title_with_markers if title_with_markers else ""


# get_tokens function moved to services/token_utils.py to avoid circular imports


def get_unique_id() -> str:
    """
    Generate unique ID for charts/tables.
    This function remains the same as it's provider-agnostic.
    """
    import uuid
    chart_id = uuid.uuid4()
    return str(chart_id)


# Import required functions for backward compatibility
from services.text_extraction_utils import convert_page_numbers_to_string

def _transform_table_data(tables: List) -> List:
    """
    Transform tables data to match expected schema.
    Maps common field names to the expected Pydantic model fields and adds any missing required fields.
    Required fields in TableData model:
    - chart_name
    - type_of_chart
    - chart_decription (note the typo)
    - x_labels
    - y_labels
    - values
    """
    transformed_tables = []
    
    # Define field mappings from common LLM output to expected model fields
    field_mappings = {
        "name": "chart_name",
        "title": "chart_name",
        "chart_title": "chart_name",
        "chart": "chart_name",
        
        "type": "type_of_chart",
        "chart_type": "type_of_chart",
        
        "description": "chart_decription",  # Note the typo in the model field
        "chart_description": "chart_decription",
        "desc": "chart_decription",
        
        "x_axis": "x_labels",
        "x_values": "x_labels",
        "x": "x_labels",
        "labels": "x_labels",
        "categories": "x_labels",
        
        "y_axis": "y_labels",
        "y_values": "y_labels",
        "y": "y_labels",
        "series": "y_labels",
        
        "data": "values",
        "y_data": "values",
        "chart_values": "values",
        "chart_data": "values"
    }
    
    # Fix any incomplete or invalid table entries
    cleaned_tables = []
    for table in tables:
        # Skip empty or non-dict tables
        if not table or not isinstance(table, dict):
            continue
            
        # Fix incomplete array values - sometimes LLM produces truncated arrays
        for key, value in table.items():
            # Check if the value is a string that looks like a truncated array
            if isinstance(value, str) and value.startswith('[') and not value.endswith(']'):
                # Try to complete the array syntax
                try:
                    # Add closing bracket if missing
                    fixed_value = value + ']'
                    # Try to parse it as JSON
                    parsed_value = json.loads(fixed_value)
                    # If successful, replace the truncated string with the parsed array
                    if isinstance(parsed_value, list):
                        table[key] = parsed_value
                except json.JSONDecodeError:
                    # If parsing fails, leave as is
                    pass
                    
        # Add the fixed table to the cleaned list
        cleaned_tables.append(table)
        
    # Process each cleaned table
    for table in cleaned_tables:
        transformed_table = {}
        
        # Process each field using the mappings
        for key, value in table.items():
            # Convert key to lowercase for case-insensitive matching
            key_lower = key.lower()
            
            # Check if this key maps to an expected field
            if key_lower in field_mappings:
                transformed_key = field_mappings[key_lower]
                transformed_table[transformed_key] = value
            else:
                # Keep original key as fallback
                transformed_table[key] = value
        
        # Ensure all required fields exist
        required_fields = ["chart_name", "type_of_chart", "chart_decription", "x_labels", "y_labels", "values"]
        
        # Handle missing chart_name
        if "chart_name" not in transformed_table:
            if "name" in table:
                transformed_table["chart_name"] = table["name"]
            else:
                transformed_table["chart_name"] = "Chart"
        
        # Handle missing type_of_chart
        if "type_of_chart" not in transformed_table:
            # Try to guess from other fields or content
            if any(t in str(transformed_table).lower() for t in ["bar", "column"]):
                transformed_table["type_of_chart"] = "bar"
            elif any(t in str(transformed_table).lower() for t in ["line"]):
                transformed_table["type_of_chart"] = "line"
            elif any(t in str(transformed_table).lower() for t in ["pie"]):
                transformed_table["type_of_chart"] = "pie"
            else:
                transformed_table["type_of_chart"] = "bar"  # Default to bar
        
        # Handle missing chart_decription
        if "chart_decription" not in transformed_table:
            chart_name = transformed_table.get("chart_name", "")
            transformed_table["chart_decription"] = f"Visualization of {chart_name}"
        
        # Handle missing x_labels
        if "x_labels" not in transformed_table:
            # See if we can find any array that might contain labels
            for key, value in table.items():
                if isinstance(value, list) and all(isinstance(item, str) for item in value):
                    transformed_table["x_labels"] = value
                    break
            # If still missing, create default labels
            if "x_labels" not in transformed_table:
                values = transformed_table.get("values", [])
                transformed_table["x_labels"] = [f"Item {i+1}" for i in range(len(values) if isinstance(values, list) else 5)]
        
        # Handle missing y_labels
        if "y_labels" not in transformed_table:
            transformed_table["y_labels"] = ["Value"]
        
        # Handle missing values
        if "values" not in transformed_table:
            # Look for any array of numbers
            for key, value in table.items():
                if isinstance(value, list) and all(isinstance(item, (int, float)) for item in value):
                    transformed_table["values"] = [int(v) if isinstance(v, float) and v.is_integer() else v for v in value]
                    break
            # If still missing, create default values
            if "values" not in transformed_table:
                transformed_table["values"] = [0] * len(transformed_table.get("x_labels", [5]))
        
        # Ensure values are integers (Pydantic model expects List[int])
        if "values" in transformed_table and isinstance(transformed_table["values"], list):
            try:
                transformed_table["values"] = [int(v) if isinstance(v, (int, float)) else 0 for v in transformed_table["values"]]
            except (ValueError, TypeError):
                transformed_table["values"] = [0] * len(transformed_table["values"])
        
        # Ensure x_labels and y_labels are lists of strings
        for label_field in ["x_labels", "y_labels"]:
            if label_field in transformed_table:
                if not isinstance(transformed_table[label_field], list):
                    transformed_table[label_field] = [str(transformed_table[label_field])]
                else:
                    transformed_table[label_field] = [str(label) for label in transformed_table[label_field]]
        
        transformed_tables.append(transformed_table)
    
    return transformed_tables
