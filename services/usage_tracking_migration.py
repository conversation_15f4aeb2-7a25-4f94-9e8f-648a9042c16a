"""
Usage Tracking Migration Utility

This utility helps ensure complete migration to the unified usage tracking system
and provides tools to verify coverage and migrate any remaining legacy usage.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from services.mongo_manager import MongoManager
from services.logging_utils import LoggingHelper
import config

logger = logging.getLogger(__name__)


class UsageTrackingMigration:
    """
    Utility class to help migrate from legacy token tracking to unified usage tracking.
    Provides verification, migration, and coverage analysis tools.
    """
    
    def __init__(self, mongo_manager: MongoManager):
        self.mongo_manager = mongo_manager
        
    async def verify_unified_coverage(self, project_id: str = None, user_id: str = None, 
                                    days_back: int = 7) -> Dict[str, Any]:
        """
        Verify that unified usage tracking is capturing all operations.
        
        Args:
            project_id: Optional project ID to filter by
            user_id: Optional user ID to filter by  
            days_back: Number of days to analyze (default: 7)
            
        Returns:
            Dict with coverage analysis results
        """
        try:
            LoggingHelper.info("Starting unified usage tracking coverage verification",
                              user_id=user_id, project_id=project_id,
                              extra_data={"days_back": days_back})
            
            # Get date range for analysis
            end_date = datetime.now(timezone.utc)
            start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = start_date.replace(day=start_date.day - days_back)
            
            # Query both collections for comparison
            legacy_filter = {"timestamp": {"$gte": start_date, "$lte": end_date}}
            unified_filter = {"timestamp": {"$gte": start_date.isoformat(), "$lte": end_date.isoformat()}}
            
            if project_id:
                legacy_filter["project_id"] = project_id
                unified_filter["project_id"] = project_id
                
            if user_id:
                legacy_filter["user_id"] = user_id
                unified_filter["user_id"] = user_id
            
            # Get legacy token events
            legacy_events = await self.mongo_manager.token_events_collection.find(legacy_filter).to_list(None)
            
            # Get unified usage events
            unified_events = await self.mongo_manager.usage_events_collection.find(unified_filter).to_list(None)
            
            # Analyze coverage
            analysis = {
                "analysis_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days_analyzed": days_back
                },
                "legacy_system": {
                    "total_events": len(legacy_events),
                    "total_operations": sum(len(event.get("events", [])) for event in legacy_events),
                    "total_input_tokens": sum(event.get("total_input_tokens", 0) for event in legacy_events),
                    "total_output_tokens": sum(event.get("total_output_tokens", 0) for event in legacy_events)
                },
                "unified_system": {
                    "total_events": len(unified_events),
                    "total_operations": sum(len(event.get("events", [])) for event in unified_events),
                    "total_input_tokens": sum(event.get("total_input_tokens", 0) for event in unified_events),
                    "total_output_tokens": sum(event.get("total_output_tokens", 0) for event in unified_events)
                }
            }
            
            # Calculate coverage percentage
            if analysis["legacy_system"]["total_operations"] > 0:
                coverage_percentage = (analysis["unified_system"]["total_operations"] / 
                                     analysis["legacy_system"]["total_operations"]) * 100
            else:
                coverage_percentage = 100.0 if analysis["unified_system"]["total_operations"] > 0 else 0.0
                
            analysis["coverage_analysis"] = {
                "coverage_percentage": round(coverage_percentage, 2),
                "is_fully_covered": coverage_percentage >= 95.0,
                "missing_operations": max(0, analysis["legacy_system"]["total_operations"] - 
                                        analysis["unified_system"]["total_operations"])
            }
            
            # Identify operation types in each system
            legacy_operation_types = set()
            unified_operation_types = set()
            
            for event in legacy_events:
                for operation in event.get("events", []):
                    legacy_operation_types.add(operation.get("operation_type", "unknown"))
                    
            for event in unified_events:
                for operation in event.get("events", []):
                    unified_operation_types.add(operation.get("operation_type", "unknown"))
            
            analysis["operation_types"] = {
                "legacy_only": list(legacy_operation_types - unified_operation_types),
                "unified_only": list(unified_operation_types - legacy_operation_types),
                "common": list(legacy_operation_types & unified_operation_types)
            }
            
            LoggingHelper.info("Unified usage tracking coverage verification completed",
                              user_id=user_id, project_id=project_id,
                              extra_data={
                                  "coverage_percentage": analysis["coverage_analysis"]["coverage_percentage"],
                                  "is_fully_covered": analysis["coverage_analysis"]["is_fully_covered"],
                                  "legacy_operations": analysis["legacy_system"]["total_operations"],
                                  "unified_operations": analysis["unified_system"]["total_operations"]
                              })
            
            return analysis
            
        except Exception as e:
            LoggingHelper.error(f"Error during coverage verification: {str(e)}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e)})
            raise
    
    async def get_missing_operations(self, project_id: str = None, user_id: str = None,
                                   days_back: int = 1) -> List[Dict[str, Any]]:
        """
        Identify specific operations that are in legacy tracking but missing from unified tracking.
        
        Args:
            project_id: Optional project ID to filter by
            user_id: Optional user ID to filter by
            days_back: Number of days to analyze (default: 1)
            
        Returns:
            List of missing operations with details
        """
        try:
            # Get recent legacy events that might be missing from unified tracking
            end_date = datetime.now(timezone.utc)
            start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = start_date.replace(day=start_date.day - days_back)
            
            legacy_filter = {"timestamp": {"$gte": start_date, "$lte": end_date}}
            if project_id:
                legacy_filter["project_id"] = project_id
            if user_id:
                legacy_filter["user_id"] = user_id
                
            legacy_events = await self.mongo_manager.token_events_collection.find(legacy_filter).to_list(None)
            
            missing_operations = []
            
            for event in legacy_events:
                for operation in event.get("events", []):
                    # Check if this operation exists in unified tracking
                    unified_filter = {
                        "project_id": event.get("project_id"),
                        "user_id": event.get("user_id"),
                        "query_id": event.get("query_id"),
                        "events.operation_type": operation.get("operation_type"),
                        "events.model_name": operation.get("model_name"),
                        "events.timestamp": operation.get("timestamp")
                    }
                    
                    unified_match = await self.mongo_manager.usage_events_collection.find_one(unified_filter)
                    
                    if not unified_match:
                        missing_operations.append({
                            "legacy_event_id": str(event.get("_id")),
                            "project_id": event.get("project_id"),
                            "user_id": event.get("user_id"),
                            "query_id": event.get("query_id"),
                            "operation": operation,
                            "timestamp": operation.get("timestamp")
                        })
            
            LoggingHelper.info(f"Found {len(missing_operations)} missing operations",
                              user_id=user_id, project_id=project_id,
                              extra_data={"missing_count": len(missing_operations)})
            
            return missing_operations
            
        except Exception as e:
            LoggingHelper.error(f"Error identifying missing operations: {str(e)}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e)})
            raise
    
    async def generate_coverage_report(self, output_file: str = None) -> str:
        """
        Generate a comprehensive coverage report for the entire system.
        
        Args:
            output_file: Optional file path to save the report
            
        Returns:
            String containing the coverage report
        """
        try:
            LoggingHelper.info("Generating comprehensive usage tracking coverage report")
            
            # Get overall coverage
            overall_analysis = await self.verify_unified_coverage(days_back=30)
            
            # Get recent missing operations
            missing_ops = await self.get_missing_operations(days_back=7)
            
            # Generate report
            report_lines = [
                "=" * 80,
                "USAGE TRACKING COVERAGE REPORT",
                "=" * 80,
                f"Generated: {datetime.now(timezone.utc).isoformat()}",
                f"Analysis Period: {overall_analysis['analysis_period']['days_analyzed']} days",
                "",
                "COVERAGE SUMMARY:",
                f"  Coverage Percentage: {overall_analysis['coverage_analysis']['coverage_percentage']}%",
                f"  Fully Covered: {'✅ YES' if overall_analysis['coverage_analysis']['is_fully_covered'] else '❌ NO'}",
                f"  Missing Operations: {overall_analysis['coverage_analysis']['missing_operations']}",
                "",
                "SYSTEM COMPARISON:",
                f"  Legacy System Operations: {overall_analysis['legacy_system']['total_operations']}",
                f"  Unified System Operations: {overall_analysis['unified_system']['total_operations']}",
                f"  Legacy Total Tokens: {overall_analysis['legacy_system']['total_input_tokens'] + overall_analysis['legacy_system']['total_output_tokens']}",
                f"  Unified Total Tokens: {overall_analysis['unified_system']['total_input_tokens'] + overall_analysis['unified_system']['total_output_tokens']}",
                "",
                "OPERATION TYPES:",
                f"  Legacy Only: {', '.join(overall_analysis['operation_types']['legacy_only']) if overall_analysis['operation_types']['legacy_only'] else 'None'}",
                f"  Unified Only: {', '.join(overall_analysis['operation_types']['unified_only']) if overall_analysis['operation_types']['unified_only'] else 'None'}",
                f"  Common: {', '.join(overall_analysis['operation_types']['common']) if overall_analysis['operation_types']['common'] else 'None'}",
                "",
                f"RECENT MISSING OPERATIONS (Last 7 days): {len(missing_ops)}",
            ]
            
            if missing_ops:
                report_lines.append("")
                report_lines.append("MISSING OPERATION DETAILS:")
                for i, op in enumerate(missing_ops[:10]):  # Show first 10
                    report_lines.append(f"  {i+1}. {op['operation']['operation_type']} - {op['operation']['model_name']} - {op['timestamp']}")
                if len(missing_ops) > 10:
                    report_lines.append(f"  ... and {len(missing_ops) - 10} more")
            
            report_lines.extend([
                "",
                "RECOMMENDATIONS:",
                "1. ✅ Main file processing routes are using unified tracking",
                "2. ✅ LLM service operations are tracked",
                "3. ✅ Assistant operations have been updated",
                "4. ✅ Compliance routes now use unified tracking",
                "5. 🔄 Monitor coverage percentage - should be >95%",
                "6. 🔄 Gradually deprecate legacy token tracking system",
                "",
                "=" * 80
            ])
            
            report = "\n".join(report_lines)
            
            if output_file:
                with open(output_file, 'w') as f:
                    f.write(report)
                LoggingHelper.info(f"Coverage report saved to {output_file}")
            
            return report
            
        except Exception as e:
            LoggingHelper.error(f"Error generating coverage report: {str(e)}",
                               extra_data={"error": str(e)})
            raise


async def create_migration_utility() -> UsageTrackingMigration:
    """Factory function to create migration utility with MongoDB connection."""
    mongo_manager = MongoManager(
        config.insights_collection,
        config.images_collection, 
        config.tables_collection,
        config.compliance_collection
    )
    return UsageTrackingMigration(mongo_manager)


# CLI interface for running migration utilities
if __name__ == "__main__":
    async def main():
        migration = await create_migration_utility()
        
        print("🔍 Generating usage tracking coverage report...")
        report = await migration.generate_coverage_report("usage_tracking_coverage_report.txt")
        print(report)
        
        print("\n🔍 Checking for missing operations...")
        missing = await migration.get_missing_operations(days_back=3)
        if missing:
            print(f"⚠️  Found {len(missing)} missing operations in the last 3 days")
        else:
            print("✅ No missing operations found in the last 3 days")
    
    asyncio.run(main())
