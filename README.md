# PDF Processing and Insight Extraction API

This repository contains a FastAPI-based application designed to process PDF files, extract text, and generate insights using OpenAI's API. MongoDB is used to store the extracted data and insights.

## Features

- **PDF Text Extraction:** Extracts text from uploaded PDF files and stores it in MongoDB.
- **OpenAI Insights:** Uses OpenAI to generate insights from the extracted PDF text.
- **Project Management:** Stores project-related data (e.g., prompts, responses, file names, user information) in MongoDB.
- **CORS Support:** Cross-origin requests are supported.
- **Session Management:** Handles sessions using the FastAPI `SessionMiddleware`.

## Requirements

- Python 3.8+
- FastAPI
- PyPDF2
- MongoDB
- OpenAI Python Client
- Uvicorn
- itsdangerous

## Installation

1. **Clone the repository:**

   ```bash
   git clone <repository_url>
   cd <repository_directory>
2. **python -m venv venv:**
source venv/bin/activate  # For Linux/Mac
# On Windows: venv\Scripts\activate

3. **Install the required packages:**
pip install -r requirements.txt

4. **Set environment variables for OpenAI API and Secret Key:**

    *Create a .env file with the following:*
    OPENAI_API_KEY=your_openai_api_key
    SECRET_KEY=your_secret_key
5. **Configure MongoDB and OpenAI:**

    *Make sure your config.py contains valid MongoDB connection details and settings for OpenAI:*
    mongo_client = AsyncIOMotorClient("mongodb+srv://your_mongo_db_url")
    dbname = "your_database_name"
    page_limit = 50  # Adjust the number of pages to be processed as needed

## Usage
**Run the FastAPI server:**
    uvicorn main:app --reload
**Endpoints:**
    POST /process/: Upload a PDF file and get insights.

    Request fields:
    file: PDF file to process.
    prompt: A prompt to generate insights based on the extracted text.
    project_id: The ID of the project for which insights are generated.
    user_id: User ID.
    page_number: Optional, to extract specific page ranges.
    file_name, file_type, title: Metadata for the file (optional).
    PUT /update-title/: Update the title of an existing project.

    Request body:
    project_id: The ID of the project.
    new_title: The new title for the project.
## Project Structure
    .
    ├── main.py               # Main FastAPI application code
    ├── config.py             # Configuration for MongoDB and OpenAI
    ├── requirements.txt      # Python dependencies
    └── README.md             # Documentation

## Future Enhancements
- Support for dynamic file formats (e.g., images, Word documents).
- More sophisticated error handling and validation.
- Integration of additional AI models for broader data insights.
## License
- This project is licensed under the MIT License. See the LICENSE file for more information.

    This block contains the full content of the `README.md` file for your FastAPI project.





