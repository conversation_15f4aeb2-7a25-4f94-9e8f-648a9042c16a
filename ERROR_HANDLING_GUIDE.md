# Enhanced Error Handling System

## Overview

The error handling system has been updated to properly distinguish between LLM-related errors and general application errors, providing appropriate user-friendly messages for each type.

## Key Changes

### 1. New Error Handler Functions

**`handle_llm_error(error)`** - For LLM/API related errors
- Uses existing OpenAI error mapping logic
- Provides specific, actionable error messages
- Handles errors from LangChain, OpenAI, DeepSeek, and other LLM providers

**`handle_general_error(error)`** - For general application errors
- Always returns the generic technical difficulties message
- Used for code errors, logic errors, database errors, etc.

### 2. Intelligent Error Classification

The system automatically determines if an error is LLM-related using:

**Error Type Patterns:**
- `outputparserexception`, `langchainexception`, `ratelimiterror`
- `apierror`, `authenticationerror`, `permissionerror`
- HTTP client errors: `httperror`, `connectionerror`, `timeout`

**Error Message Patterns:**
- API-related: "api key", "authentication", "rate limit", "quota"
- Model-related: "model", "context length", "token"
- Service-related: "timeout", "connection", "server error"
- Provider-specific: "openai", "deepseek"

**Error Attributes:**
- Errors with `status_code` or `response` attributes (HTTP errors)

### 3. Global Exception Handlers

Added to `main.py`:
- **APIError Handler**: Returns custom API errors as-is
- **HTTPException Handler**: Returns FastAPI HTTP exceptions as-is  
- **General Exception Handler**: Catches all unhandled exceptions and returns generic message

## Usage

### In LLM Services

```python
# OLD:
from exceptions.api_exceptions import handle_openai_error
try:
    # LLM operation
    response = await llm_service.chat_completion(messages)
except Exception as e:
    handle_openai_error(e)  # Only worked for OpenAI

# NEW:
from exceptions.api_exceptions import handle_llm_error
try:
    # LLM operation  
    response = await llm_service.chat_completion(messages)
except Exception as e:
    handle_llm_error(e)  # Works for all LLM providers
```

### In General Application Code

```python
from exceptions.api_exceptions import handle_general_error
try:
    # General application logic
    result = process_data(data)
except Exception as e:
    handle_general_error(e)  # Always returns generic message
```

## Error Messages

### LLM-Related Errors
Users receive specific, actionable messages:
- "The AI API key provided appear to be invalid. Please contact your administrator..."
- "We're receiving too many requests from your account. Please wait a moment..."
- "Your request exceeds the maximum allowed text length. Please reduce..."

### General Application Errors
Users always receive the generic message:
- "We're experiencing some technical difficulties. Please try again in a few moments. If the issue persists, contact your administrator."

## Files Modified

1. **`exceptions/api_exceptions.py`**
   - Added `handle_llm_error()` function
   - Added `handle_general_error()` function  
   - Added `_is_llm_related_error()` helper function

2. **`main.py`**
   - Added global exception handlers for unhandled errors

3. **`services/llm_service.py`**
   - Updated to use `handle_llm_error()` instead of `handle_openai_error()`

4. **`services/llm_utils.py`**
   - Updated all LLM functions to use `handle_llm_error()`

## Benefits

1. **Better User Experience**: Users get appropriate error messages based on error type
2. **LLM Provider Agnostic**: Works with OpenAI, DeepSeek, and any future LangChain providers
3. **Security**: Internal application errors don't leak implementation details
4. **Maintainability**: Centralized error handling logic
5. **Backward Compatibility**: Existing error handling still works

## Testing

Run the test script to verify functionality:
```bash
python simple_test.py
```

The test verifies that:
- LLM-related errors are correctly identified
- Non-LLM errors are correctly identified  
- The generic error message is properly defined

## Migration Notes

- Existing `handle_openai_error()` calls in LLM services have been updated to `handle_llm_error()`
- No changes needed for route handlers - global exception handlers catch unhandled errors
- The system is backward compatible with existing error handling patterns
