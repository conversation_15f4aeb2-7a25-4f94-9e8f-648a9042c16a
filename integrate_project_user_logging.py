#!/usr/bin/env python3
"""
Integration script for project+user logging system.

This script systematically integrates the project+user logging system
throughout the entire codebase, ensuring user_id and project_id are
available wherever logging occurs.
"""

import os
import sys
import re
from pathlib import Path
from typing import List, Dict, Tuple

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class LoggingIntegrator:
    """Handles systematic integration of project+user logging."""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.routes_dir = self.project_root / "routes"
        self.services_dir = self.project_root / "services"
        
        # Files to integrate (in order of priority)
        self.route_files = [
            "file_processing_routes.py",  # Already started
            "project_management_routes.py",
            "file_download_routes.py", 
            "compliance_routes.py",
            "file_details_routes.py",
            "templated_projects_routes.py"
        ]
        
        self.service_files = [
            "pdf_processor.py",
            "data_analysis.py",
            "data_analyst_agent.py",
            "mongo_manager.py",
            "token_tracking.py",
            "llm_utils.py",
            "file_utils.py"
        ]
        
        self.utility_files = [
            "auth_utils.py",
            "text_extraction_utils.py",
            "search_utils.py",
            "image_utils.py"
        ]
    
    def analyze_file_logging(self, file_path: Path) -> Dict[str, List[str]]:
        """Analyze logging patterns in a file."""
        if not file_path.exists():
            return {"error": [f"File not found: {file_path}"]}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {"error": [f"Error reading file: {e}"]}
        
        lines = content.split('\n')
        analysis = {
            "logger_declarations": [],
            "logging_calls": [],
            "print_statements": [],
            "user_id_usage": [],
            "project_id_usage": [],
            "function_definitions": []
        }
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Find logger declarations
            if re.search(r'logger\s*=\s*logging\.getLogger', line):
                analysis["logger_declarations"].append(f"Line {i}: {line_stripped}")
            
            # Find logging calls
            if re.search(r'logger\.(info|debug|warning|error|critical)', line):
                analysis["logging_calls"].append(f"Line {i}: {line_stripped}")
            
            # Find print statements (potential logging candidates)
            if re.search(r'print\s*\(', line) and not line_stripped.startswith('#'):
                analysis["print_statements"].append(f"Line {i}: {line_stripped}")
            
            # Find user_id usage
            if 'user_id' in line and not line_stripped.startswith('#'):
                analysis["user_id_usage"].append(f"Line {i}: {line_stripped}")
            
            # Find project_id usage
            if 'project_id' in line and not line_stripped.startswith('#'):
                analysis["project_id_usage"].append(f"Line {i}: {line_stripped}")
            
            # Find function definitions
            if re.search(r'^\s*(async\s+)?def\s+\w+', line):
                analysis["function_definitions"].append(f"Line {i}: {line_stripped}")
        
        return analysis
    
    def generate_integration_plan(self, file_path: Path) -> Dict[str, any]:
        """Generate integration plan for a specific file."""
        analysis = self.analyze_file_logging(file_path)
        
        if "error" in analysis:
            return {"error": analysis["error"]}
        
        plan = {
            "file": str(file_path),
            "priority": self._get_file_priority(file_path),
            "changes_needed": [],
            "context_availability": self._assess_context_availability(analysis),
            "integration_complexity": "low"
        }
        
        # Determine changes needed
        if analysis["logger_declarations"]:
            plan["changes_needed"].append("Replace logger declarations with LoggingHelper")
        
        if analysis["logging_calls"]:
            plan["changes_needed"].append(f"Update {len(analysis['logging_calls'])} logging calls")
        
        if analysis["print_statements"]:
            plan["changes_needed"].append(f"Convert {len(analysis['print_statements'])} print statements to logging")
        
        # Assess complexity
        total_changes = len(analysis["logging_calls"]) + len(analysis["print_statements"])
        if total_changes > 50:
            plan["integration_complexity"] = "high"
        elif total_changes > 20:
            plan["integration_complexity"] = "medium"
        
        return plan
    
    def _get_file_priority(self, file_path: Path) -> str:
        """Determine integration priority for a file."""
        if file_path.parent.name == "routes":
            return "high"
        elif file_path.name in ["pdf_processor.py", "data_analysis.py"]:
            return "high"
        elif file_path.parent.name == "services":
            return "medium"
        else:
            return "low"
    
    def _assess_context_availability(self, analysis: Dict[str, List[str]]) -> str:
        """Assess if user_id and project_id context is available."""
        has_user_id = len(analysis["user_id_usage"]) > 0
        has_project_id = len(analysis["project_id_usage"]) > 0
        
        if has_user_id and has_project_id:
            return "full"
        elif has_user_id or has_project_id:
            return "partial"
        else:
            return "none"
    
    def create_integration_report(self) -> Dict[str, any]:
        """Create comprehensive integration report."""
        report = {
            "summary": {
                "total_files": 0,
                "high_priority": 0,
                "medium_priority": 0,
                "low_priority": 0,
                "total_logging_calls": 0,
                "files_with_context": 0
            },
            "files": []
        }
        
        # Analyze all files
        all_files = []
        
        # Route files
        for filename in self.route_files:
            file_path = self.routes_dir / filename
            all_files.append(file_path)
        
        # Service files
        for filename in self.service_files:
            file_path = self.services_dir / filename
            all_files.append(file_path)
        
        # Utility files
        for filename in self.utility_files:
            file_path = self.services_dir / filename
            all_files.append(file_path)
        
        for file_path in all_files:
            if file_path.exists():
                plan = self.generate_integration_plan(file_path)
                if "error" not in plan:
                    report["files"].append(plan)
                    
                    # Update summary
                    report["summary"]["total_files"] += 1
                    
                    if plan["priority"] == "high":
                        report["summary"]["high_priority"] += 1
                    elif plan["priority"] == "medium":
                        report["summary"]["medium_priority"] += 1
                    else:
                        report["summary"]["low_priority"] += 1
                    
                    if plan["context_availability"] in ["full", "partial"]:
                        report["summary"]["files_with_context"] += 1
        
        return report
    
    def print_integration_report(self):
        """Print comprehensive integration report."""
        report = self.create_integration_report()
        
        print("=" * 80)
        print("PROJECT+USER LOGGING INTEGRATION REPORT")
        print("=" * 80)
        
        # Summary
        summary = report["summary"]
        print(f"\n📊 SUMMARY:")
        print(f"   Total files to integrate: {summary['total_files']}")
        print(f"   High priority files: {summary['high_priority']}")
        print(f"   Medium priority files: {summary['medium_priority']}")
        print(f"   Low priority files: {summary['low_priority']}")
        print(f"   Files with user/project context: {summary['files_with_context']}")
        
        # Group files by priority
        high_priority = [f for f in report["files"] if f["priority"] == "high"]
        medium_priority = [f for f in report["files"] if f["priority"] == "medium"]
        low_priority = [f for f in report["files"] if f["priority"] == "low"]
        
        # High priority files
        if high_priority:
            print(f"\n🔴 HIGH PRIORITY FILES ({len(high_priority)}):")
            for file_info in high_priority:
                self._print_file_info(file_info)
        
        # Medium priority files
        if medium_priority:
            print(f"\n🟡 MEDIUM PRIORITY FILES ({len(medium_priority)}):")
            for file_info in medium_priority:
                self._print_file_info(file_info)
        
        # Low priority files
        if low_priority:
            print(f"\n🟢 LOW PRIORITY FILES ({len(low_priority)}):")
            for file_info in low_priority:
                self._print_file_info(file_info)
        
        # Integration recommendations
        print(f"\n💡 INTEGRATION RECOMMENDATIONS:")
        print(f"   1. Start with HIGH PRIORITY files (routes and core services)")
        print(f"   2. Focus on files with 'full' or 'partial' context availability")
        print(f"   3. Update service constructors to accept user_id/project_id")
        print(f"   4. Use LoggingHelper for easy migration")
        print(f"   5. Test each file after integration")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"   1. Run: python integrate_project_user_logging.py --start-integration")
        print(f"   2. Review and test each integrated file")
        print(f"   3. Update function signatures to pass context")
        print(f"   4. Verify logging output in logs/users/ directory")
    
    def _print_file_info(self, file_info: Dict[str, any]):
        """Print information about a single file."""
        filename = Path(file_info["file"]).name
        context = file_info["context_availability"]
        complexity = file_info["integration_complexity"]
        changes = len(file_info["changes_needed"])
        
        context_emoji = {"full": "✅", "partial": "⚠️", "none": "❌"}[context]
        complexity_emoji = {"low": "🟢", "medium": "🟡", "high": "🔴"}[complexity]
        
        print(f"   📁 {filename}")
        print(f"      Context: {context_emoji} {context}")
        print(f"      Complexity: {complexity_emoji} {complexity}")
        print(f"      Changes needed: {changes}")
        if file_info["changes_needed"]:
            for change in file_info["changes_needed"]:
                print(f"        - {change}")
        print()


def main():
    """Main function."""
    project_root = os.path.dirname(os.path.abspath(__file__))
    integrator = LoggingIntegrator(project_root)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--start-integration":
        print("🚧 Integration functionality will be implemented in the next phase.")
        print("📋 For now, use this report to manually integrate files.")
    else:
        integrator.print_integration_report()


if __name__ == "__main__":
    main()
