# import logging
# from fastapi import APIRouter, UploadFile, Form, File, Request, HTTPException, Query, BackgroundTasks
# from services.pdf_processor import PDFProcessor
# from services.data_analysis import process_data_analysis
# from services.mongo_manager import MongoManager
# from fastapi.responses import FileResponse
# from sse_starlette.sse import EventSourceResponse
# import os
# import config
# from typing import List, Optional
# from models import insight_model
# import json as jp
# import numpy as np
# import asyncio
# from bson import ObjectId
# from services import utils
# import pandas as pd
# import json
# import urllib
# from datetime import datetime
# from services.utils import get_compliance_report, decode_jwt_token, generate_title
# from exceptions.api_exceptions import handle_openai_error

# from services.prompts import get_compliance_prompt
# from io import BytesIO
# import pandas as pd
# from openpyxl import load_workbook

# import uuid
# from starlette.background import BackgroundTask
# from reportlab.lib import colors
# from reportlab.lib.pagesizes import A4
# from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
# from reportlab.lib.units import inch
# from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
# from reportlab.lib.enums import TA_CENTER, TA_LEFT
# import re


# # Configure logging
# # logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# logger = logging.getLogger(__name__)

# router = APIRouter()

# mongo_manager = MongoManager(config.insights_collection, config.images_collection, config.tables_collection, config.compliance_collection)
# pdf_processor = PDFProcessor(config.openai_client, mongo_manager)




# @router.post(f"/{config.FileProcessUrl}/")
# async def process_files(
#     request: Request,
#     file: UploadFile = File(None),
#     prompt: str = Form(...),
#     file_name: Optional[str] = Form(None),
#     file_type: Optional[str] = Form(None),
#     project_id: str = Form(...),
#     user_id: str = Form(...),
#     page_number: Optional[str] = Form(None),
#     title: Optional[str] = Form(None),
#     response_type: str = Form(...),
#     visualization: bool = Form(False),
#     custom_prompt_name: Optional[str] = Form(None),
# ):

#     try:
#         print("hello")
#         # User Authentication
#         if config.VALIDATE_TOKEN:
#             token = request.headers.get('Authorization')
#             if not token or not token.startswith('Bearer '):
#                 logger.error("Authorization header is missing or invalid")
#                 raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

#             token = token.split(' ')[1]
#             payload = utils.decode_jwt_token(token)
#             if payload:
#                 user_email = payload['sub']
#                 is_valid_user = mongo_manager.check_user_exists(user_email)
#                 if not is_valid_user:
#                     logger.error("User not found")
#                     raise HTTPException(status_code=401, detail="Invalid user")
#                 else:
#                     logger.info("Valid User")
#             else:
#                 logger.error("Invalid token")
#                 raise HTTPException(status_code=401, detail="Invalid token")
#         else:
#             logger.info("Token validation is turned OFF. Skipping authentication checks.")




#         # Check for restricted keywords only when prompt is not the same as custom_prompt_name
#         try:
#             # Skip restricted content check if using a custom prompt, but still check if it's in the format with |||
#             if custom_prompt_name and prompt == custom_prompt_name:
#                 # If custom_prompt_name has the format "category|||subcategory|||prompt_text", we should still check for restricted content
#                 if "|||" in custom_prompt_name:
#                     logger.info(f"Custom prompt contains ||| format, so checking for restricted content: {custom_prompt_name}")
#                     is_restricted = await utils.check_restricted_keywords(prompt, project_id, mongo_manager)
#                 else:
#                     logger.info(f"Skipping restricted content check for standard custom prompt: {custom_prompt_name}")
#                     is_restricted = False
#             else:
#                 is_restricted = await utils.check_restricted_keywords(prompt, project_id, mongo_manager)

#             if is_restricted:
#                 logger.warning(f"Restricted content detected in query: {prompt}")

#                 restricted_message = "We apologize, but your query contains restricted or sensitive content that we cannot process. Please rephrase your question to avoid potentially harmful or inappropriate content."

#                 # Create the restricted response object
#                 restricted_response = {
#                     'summary': restricted_message,
#                     'tables': "",
#                     'page_number': '',
#                     'suggested_questions': [],
#                     'files': [],
#                     'images': "",
#                     'input_tokens_used': '',
#                     'output_tokens_used': '',
#                     'response_id': str(ObjectId()),
#                     'response_time': datetime.utcnow().isoformat(),
#                     'response_type': response_type,
#                     'source_type': ''
#                 }

#                 # Check if we should return directly or stream the response
#                 if custom_prompt_name and prompt == custom_prompt_name and file:
#                     logger.info(f"Returning restricted response directly for custom prompt with file: {custom_prompt_name}")
#                     return restricted_response
#                 else:
#                     # Create a streaming response for restricted content
#                     async def restricted_content_stream():
#                         # Stream message word by word
#                         words = restricted_message.split()
#                         for i, word in enumerate(words):
#                             # Add space after each word except the last one
#                             word_chunk = word + (" " if i < len(words) - 1 else "")
#                             yield json.dumps({
#                                 "type": "message",
#                                 "content": word_chunk
#                             })
#                             # Add a small delay between words for smoother streaming
#                             await asyncio.sleep(0.1)

#                         # Then yield complete signal
#                         yield json.dumps({"type": "complete"})

#                         # Finally yield the full response object
#                         yield json.dumps({
#                             "type": "final_response",
#                             "content": restricted_response
#                         })

#                     return EventSourceResponse(restricted_content_stream())
#         except Exception as e:
#             logger.error(f"Error checking restricted keywords: {str(e)}")
#             # In case of error checking restrictions, continue with the normal processing
#             # This is a defensive approach to not block user queries if the restriction check fails
#             logger.warning("Continuing with processing despite error in restricted content check")



#         logger.info("Processing file...")
#         logger.info(f"File: {file}")
#         logger.info(f"Prompt: {prompt}")
#         logger.info(f"Project ID: {project_id}")
#         logger.info(f"User ID: {user_id}")
#         logger.info(f"Page Number: {page_number}")
#         logger.info(f"Title: {title}")
#         logger.info(f"Response Type: {response_type}")
#         logger.info(f"Custom Prompt Name: {custom_prompt_name}")

#         # Check if this is a new project or an existing one
#         project_data, exists, _ = mongo_manager.get_or_initialize_project(project_id)

#         # Handle title appropriately
#         if title is None:
#             if exists:
#                 # For existing projects, use the existing title if none is provided
#                 title = project_data.get("title", 'Untitled')
#                 logger.info(f"Using existing title: {title} for project {project_id}")
#             else:
#                 # For new projects, set default title to 'Untitled'
#                 title = 'Untitled'
#                 logger.info("Set default title to 'Untitled' for new project")

#         if file:

#             # Create project directory if it doesn't exist
#             project_dir = os.path.join(config.file_path, project_id)
#             os.makedirs(project_dir, exist_ok=True)

#             # Create subdirectories and store their paths
#             data_analysis_dir = os.path.join(project_dir, config.data_analysis_folder_name)
#             non_data_analysis_dir = os.path.join(project_dir, config.non_data_analysis_folder_name)

#             # Create the directories
#             os.makedirs(data_analysis_dir, exist_ok=True)
#             os.makedirs(non_data_analysis_dir, exist_ok=True)

#             logger.info(f"Created dirs: {project_dir}, {data_analysis_dir}, {non_data_analysis_dir}")
#             temp_file=file
#             supported_filetypes = ["pdf", "doc", "docx", "txt", "csv", "xlsx"]
#             # Get the file name and content type
#             file_name = file.filename
#             # content_type = file.content_type.lower()

#             # Get extension from filename
#             file_type = file_name.split('.')[-1].lower() if '.' in file_name else ''

#             if file_type not in supported_filetypes:
#                 raise HTTPException(status_code=415, detail=f"Unsupported file type: {file_type}")
#             file_name_without_ext=file_name.split(".")[0]
#             logger.info(f"File type determined as: {file_type} from extension: {file_type} and filename is : {file_name_without_ext}")
#             # temp_file=file
#             secret_key=config.secret_key


#             if file_type=='pdf' or file_type=='doc' or file_type=='docx' or file_type=='txt':

#                 # Read the file content before saving
#                 file_content = await file.read()
#                 encrypted_content = utils.encrypt_data(file_content, secret_key)

#                 # Save encrypted content to a file
#                 encrypted_file_name = f"{file_name_without_ext}.enc"
#                 file_path = os.path.join(non_data_analysis_dir, encrypted_file_name)
#                 with open(file_path, "wb") as buffer:
#                     buffer.write(encrypted_content)
#                 logger.info(f"Saved encrypted file to: {file_path}")

#                 # Reset file position for subsequent reads
#                 await file.seek(0)

#                 previous_responses,previous_prompt="",""
#                 llm_call=False
#                 insight_data=""
#                 if file_type=='doc' or file_type=='docx' or file_type=='txt':
#                     content = await file.read()

#                     logger.info("intent----------------------------file_type --------------------- %s", file_type)
#                     if file_type=='txt':
#                         extracted_text=utils.read_txt_file(content)
#                     elif file_type=='doc' or file_type=='docx':
#                         extracted_text = utils.read_doc_file(content)

#                     page_number=[]
#                     page_numbers=[]
#                     valid_page_numbers=[]
#                     extracted_text_per_page=[extracted_text]

#                     logger.info("intent----------------------------extracted_text_per_page --------------------- %s", extracted_text_per_page)

#                 else:
#                     logger.info(f"Extract text from file content")
#                     if config.plain_txt_without_ocr:
#                     # extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = await utils.extract_text_from_file(file, page_number)
#                         print("in plain txt extraction")
#                         extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = await utils.extract_text_from_file(temp_file, page_number)
#                     else:
#                         TEMP_DIR = config.temp_path
#                         os.makedirs(TEMP_DIR, exist_ok=True)
#                         extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = await utils.upload_and_process_pdf(temp_file, page_number,TEMP_DIR)
#                     # extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = await utils.extract_text_from_file(file, page_number)
#                      # extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = await utils.extract_text_from_file(temp_file, page_number)
#                     # TEMP_DIR = config.temp_path
#                     # os.makedirs(TEMP_DIR, exist_ok=True)
#                     # extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = await utils.upload_and_process_pdf(temp_file, page_number,TEMP_DIR)
#                     logger.info("-----------------------------------")
#                     logger.info(f"Extracted text is{extracted_text}")
#                     logger.info("-----------------------------------")

#                 # logger.info("length of extracted text per page before trimming: %s", len(extracted_text_per_page))

#                 # if len(extracted_text_per_page) >:
#                 #     extracted_text_per_page=extracted_text_per_page[:15]
#                 #     logger.info("length of extracted text per page trimmed to first 15 pages: %s", len(extracted_text_per_page))

#                 try:
#                     intent = utils.get_intent(mongo_manager, config.openai_client, extracted_text_per_page[:7500], project_id)
#                     # Only parse if intent is returned as a string
#                     if isinstance(intent, str):
#                         intent = jp.loads(intent)
#                 except Exception as e:
#                     logger.error(f"Error getting intent from extracted text: {str(e)}")
#                     handle_openai_error(e)
#                     intent = {"context_intent": {"intent_description": "Unable to determine intent", "keywords": []}}

#                 pdf_file_path = non_data_analysis_dir

#                 # Construct the path
#                 pkl_file_path = os.path.join(
#                     pdf_file_path, file_name.split(".")[0] + ".pkl"
#                 )
#                 print(pkl_file_path)
#                 utils.put_pickle_file_context(pkl_file_path,{'texts': extracted_text_per_page, 'total_pages': len(extracted_text_per_page)})
#                 # logger.info("context------------------------------------------------ %s", extracted_text_per_page)

#                 logger.info("intent----------------------------context --------------------- %s", intent)
#                 previous_responses,previous_prompt="",""
#                 llm_call=False
#                 # Use the project_data we already retrieved
#                 if not exists:
#                     complete_chat=[]
#                     # complete_chat.append({
#                     #                 "filename": file_name or "",
#                     #                 "query": prompt,
#                     #                 "response": insight_data
#                     #             })
#                 else:
#                     complete_chat=project_data.get("complete_chat", [])
#                 logger.info(f"Process pdf called with file_name: {file_name}")

#                 async def event_generator():
#                     pdf_processor = PDFProcessor(config.openai_client, mongo_manager)
#                     async for event in pdf_processor.process_pdf(
#                         file=file,
#                         prompt=prompt,
#                         project_id=project_id,
#                         user_id=user_id,
#                         page_number=page_number,
#                         file_name=file_name,
#                         file_type=file_type,
#                         title=title,
#                         extracted_text=extracted_text,
#                         page_numbers=page_numbers,
#                         extracted_text_per_page=extracted_text_per_page,
#                         valid_page_numbers=valid_page_numbers,
#                         intent=intent,
#                         previous_responses=previous_responses,
#                         previous_prompt=previous_prompt,
#                         llm_call=False,
#                         insight_data="",
#                         custom_prompt_name=custom_prompt_name,
#                         response_type=response_type,
#                         visualization=visualization,
#                         complete_chat=complete_chat

#                     ):
#                         logger.info("Event: %s", event)
#                         jsonifed_event = json.loads(event)

#                         if jsonifed_event["type"] == "final_response":
#                             logger.info(f"Type of event content: {type(jsonifed_event['content'])}")
#                             yield jsonifed_event['content']  # Yield the content instead of returning
#                         else:
#                             yield {
#                                 "type": "message",
#                                 "content": event  # Wrap the original event in a JSON object
#                             }

#                 # Collect results from the event generator
#                 response_content = []
#                 async for result in event_generator():
#                     response_content.append(result)

#                 # Return the final response
#                 return response_content[0]

#             elif file_type == "csv" or file_type == "xlsx":
#                 try:
#                     # Read the file content as binary
#                     file_content = await file.read()

#                     # Encrypt the binary content using your encryption method
#                     encrypted_content = utils.encrypt_data(file_content, secret_key)

#                     # Save the encrypted content to a new file
#                     encrypted_file_name = f"{file_name_without_ext}.enc"
#                     encrypted_file_path = os.path.join(data_analysis_dir, encrypted_file_name)
#                     with open(encrypted_file_path, "wb") as buffer:
#                         buffer.write(encrypted_content)

#                     logger.info(f"Saved encrypted file to: {encrypted_file_path}")

#                     # If the file is CSV, you may want to process it as follows
#                     if file_type == 'csv':
#                         try:
#                             # Use pandas to handle CSV reading from binary content
#                             csv_data = pd.read_csv(BytesIO(file_content))
#                             # Process csv_data as needed (e.g., analysis or further encryption)
#                             logger.info("CSV file processed successfully.")
#                         except Exception as e:
#                             logger.error(f"Error processing CSV: {e}")

#                     # If the file is Excel (XLSX), use openpyxl to handle it
#                     elif file_type == 'xlsx':
#                         try:
#                             # Load the workbook from the binary content
#                             workbook = load_workbook(BytesIO(file_content))
#                             # Process the workbook as needed
#                             logger.info("Excel file processed successfully.")
#                         except Exception as e:
#                             logger.error(f"Error processing Excel file: {e}")

#                     # Reset file position for any subsequent reads
#                     await file.seek(0)

#                 except Exception as e:
#                     logger.error(f"Error reading file: {e}")
#                 # Save file in project-specific directory
#                 file_path = os.path.join(data_analysis_dir, file_name)
#                 with open(file_path, "wb") as buffer:
#                     buffer.write(await file.read())
#                 logger.info(f"Saved file to local path: {file_path}")
#                 # if w dont read file from local again and save there:
#                 #____________________________________________________________________________________________________
#                 # file_content = await file.read()

#                 # # Process the file content before saving it encrypted
#                 # file_text = None
#                 # if file_type.lower() == 'csv':
#                 #     try:
#                 #         # Read the CSV data from the binary content
#                 #         df = pd.read_csv(BytesIO(file_content))
#                 #         # Extract the first few rows as a string (file preview)
#                 #         file_text = df.head().to_string()
#                 #         logger.info(f"CSV file processed, preview: {file_text}")
#                 #     except Exception as e:
#                 #         logger.error(f"Error processing CSV: {e}")

#                 # elif file_type.lower() == 'xlsx':
#                 #     try:
#                 #         # Read the Excel data from the binary content
#                 #         df = pd.read_excel(BytesIO(file_content))
#                 #         # Extract the first few rows as a string (file preview)
#                 #         file_text = df.head().to_string()
#                 #         logger.info(f"Excel file processed, preview: {file_text}")
#                 #     except Exception as e:
#                 #         logger.error(f"Error processing Excel file: {e}")
#                 #____________________________________________________________________________________________________________

#                 try:
#                     if file_type.lower() == 'csv':
#                         df = pd.read_csv(file_path)
#                         file_text = df.head().to_string()
#                     elif file_type.lower() == 'xlsx':
#                         df = pd.read_excel(file_path)
#                         file_text = df.head().to_string()

#                 except Exception as e:
#                     logger.error(f"Error reading file: {e}")
#                     raise HTTPException(status_code=500, detail="Error reading file")

#                 try:
#                     logger.info(f"length of file text for data analysis intent: {len(file_text)}")
#                     intent = utils.get_intent(mongo_manager, config.openai_client, [file_text], project_id)
#                     # Only parse if intent is returned as a string
#                     if isinstance(intent, str):
#                         intent = json.loads(intent)
#                     logger.info(f"intent for data analysis mode: {intent}")
#                 except Exception as e:
#                     logger.error(f"Error getting intent for data analysis: {str(e)}")
#                     handle_openai_error(e)
#                     intent = {"context_intent": {"intent_description": "Unable to determine intent", "keywords": []}}

#                 # Replace direct call with streaming response
#                 response = await process_data_analysis(
#                             file_path, file_type, file_name, prompt,
#                             project_id, user_id, title, response_type,
#                             intent, "", visualization, custom_prompt_name
#                         )
#                 #         # Stream the response as a final_response event
#                 #         yield json.dumps(response)
#                 #     except Exception as e:
#                 #         logger.error(f"Error in data analysis event generator: {str(e)}")
#                 #         yield json.dumps({"type": "error", "content": str(e)})

#                 # return EventSourceResponse(event_generator())

#                 return response


#         else:
#             logger.info("Entered else block in process file route")
#             fileInDb = True
#             extracted_text=""
#             # try:
#             # last_existing_file_name = mongo_manager.get_file_name(project_id)[-1]  # get name of last existing file
#             # last_existing_file_extension = last_existing_file_name.split('.')[-1]  # get extension of last existing file
#             # We already have project_data from earlier

#             page_numbers, previous_responses, previous_prompt,intent = utils.extract_text_from_existing_project(project_data, page_number,project_id,file_name,)
#             valid_page_numbers = page_numbers
#             logger.info(f"Length of previous_responses before removing images: {len(previous_responses)}")
#             # Remove images from each response in previous_responses
#             for response in previous_responses:
#                 if 'images' in response:
#                     del response['images']
#             logger.info("Removed images from previous_responses")
#             logger.info(f"Length of previous_responses after removing images: {len(previous_responses)}")
#             filesearch_descriptions,dataset_descriptions=utils.get_files_and_description(intent)

#             chat_history = [{p: r} for p, r in zip(previous_prompt, previous_responses)]
#             # complete_chat=[{p: r} for p, r in zip(previous_prompt, previous_responses)]

#             source_to_select=utils.get_source(chat_history,prompt,filesearch_descriptions,dataset_descriptions,project_id,mongo_manager)
#             logger.info(f"source_to_select: {source_to_select}")
#             # print("source_to_select",source_to_select)
#             logger.info(f"type of source_to_select: {type(source_to_select)}")
#             # Only parse if it's returned as a string
#             if isinstance(source_to_select, str):
#                 source_to_select = jp.loads(source_to_select)
#             logger.info(f"source_to_select after processing: {source_to_select}")

#             llm_call=False
#             try:
#                 insight_data=""
#                 logger.info(f"source_to_select['source'][0]['FileTool']: {source_to_select['source'][0]['FileTool']}")
#                 logger.info(f"source_to_select['source'][0]['WebSeach']: {source_to_select['source'][0]['WebSeach']}")
#                 logger.info(f"source_to_select['source'][0]['SelfAnswer']: {source_to_select['source'][0]['SelfAnswer']}")
#                 if source_to_select['source'][0]['FileTool'] == True:
#                     extracted_text = []
#                     files_for_data_analysis = []

#                     # Check file type of first item to determine priority
#                     first_item = source_to_select['source'][0]['Filename'][0]
#                     first_ext = first_item.split(".")[1]
#                     is_data_analysis_priority = first_ext in ["csv", "xlsx"]
#                     logger.info(f"is_data_analysis_priority: {is_data_analysis_priority}")

#                     for item in source_to_select['source'][0]['Filename']:
#                         logger.info(f"file to get context of {item}")
#                         ext = item.split(".")[1]

#                         if is_data_analysis_priority:
#                             if ext in ["csv", "xlsx"]:
#                                 files_for_data_analysis.append(item)
#                         else:
#                             if ext in ["pdf", "doc", "docx", "txt"]:
#                                 # Construct the pickle path correctly
#                                 pkl_file_path = os.path.join(
#                                     config.file_path,
#                                     project_id,
#                                     config.non_data_analysis_folder_name,
#                                     item.split(".")[0] + ".pkl"
#                                 )
#                                 logger.info(f"Attempting to read pickle file from: {pkl_file_path}")

#                                 try:
#                                     file_context = utils.get_pickle_file_context(pkl_file_path, valid_page_numbers)
#                                     if file_context:
#                                         extracted_text.append(file_context)
#                                         logger.info(f"Successfully read context from pickle file")
#                                     else:
#                                         logger.warning(f"No context found in pickle file: {pkl_file_path}")
#                                 except Exception as e:
#                                     logger.error(f"Error reading pickle file {pkl_file_path}: {e}")
#                                     continue

#                     # Return appropriate response based on priority
#                     if is_data_analysis_priority and files_for_data_analysis:
#                         logger.info(f"process data analysis called with files_for_data_analysis: {files_for_data_analysis}")
#                         return await process_data_analysis(None, None, None, prompt, project_id, user_id, title, response_type, "", files_for_data_analysis, visualization)


#                 elif source_to_select['source'][0]['WebSeach'] == True:
#                     logger.info("Processing web search")
#                     if utils.paid_search:
#                         logger.info("Calling SERPER search")
#                         extracted_text= utils.perform_search(prompt)
#                     else:
#                         logger.info("Calling DUCKDUCKGO search")
#                         extracted_text = utils.duckduckgo_query(prompt)
#                     if extracted_text!= False:
#                         logger.info("Successfully extracted text from web search")
#                         llm_call = False
#                         insight_data = ""

#                         # Initialize complete_chat based on project status
#                         # Use the project_data we already retrieved
#                         if not exists:
#                             complete_chat = []
#                         else:
#                             complete_chat = project_data.get("complete_chat", [])

#                         logger.info(f"Length of extracted text: {len(extracted_text)}")
#                         logger.info("--------------------PAGES, web search---------------------")
#                         logger.info(f"Valid page numbers: {valid_page_numbers}")

#                         extracted_text_per_page, valid_page_numbers = "", 0

#                         async def event_generator():
#                             nonlocal llm_call, insight_data
#                             try:
#                                 async for event in utils.fake_llm_resonse(
#                                     source_to_select['query'], project_id,
#                                     mongo_manager, extracted_text, chat_history,
#                                     valid_page_numbers, response_type,
#                                     visualization, datetime.utcnow().date()
#                                 ):
#                                     event_data = json.loads(event)
#                                     if event_data["type"] == "final_response":
#                                         insight_data = event_data["content"]
#                                         if isinstance(insight_data, str):
#                                             insight_data = json.loads(insight_data)
#                                         insight_data["source_type"] = "web search"
#                                         query_title = await generate_title(prompt, insight_data["summary"])
#                                         insight_data["summary"] = f"""**{query_title}**\n\n{insight_data["summary"]}"""
#                                         logger.info(f"source type for web search case: {insight_data['source_type']}")
#                                         llm_call = True

#                                         # Now process through pdf_processor using the main flow
#                                         async for pdf_event in pdf_processor.process_pdf(
#                                             file=file,
#                                             prompt=prompt,
#                                             project_id=project_id,
#                                             user_id=user_id,
#                                             page_number=page_number,
#                                             file_name=file_name,
#                                             file_type=file_type,
#                                             title=title,
#                                             extracted_text=extracted_text,
#                                             page_numbers=page_numbers,
#                                             extracted_text_per_page=extracted_text_per_page,
#                                             valid_page_numbers=valid_page_numbers,
#                                             intent=intent,
#                                             previous_responses=previous_responses,
#                                             previous_prompt=previous_prompt,
#                                             llm_call=llm_call,
#                                             insight_data=insight_data,
#                                             custom_prompt_name = None,
#                                             response_type=response_type,
#                                             visualization=visualization,
#                                             complete_chat=complete_chat
#                                         ):
#                                             yield pdf_event
#                                     else:
#                                         yield event
#                             except Exception as e:
#                                 logger.error(f"Error in event generator: {str(e)}")
#                                 handle_openai_error(e)

#                         return EventSourceResponse(event_generator())


#                     else:
#                         logger.info("Failed to extract text from web search. Falling back to use self answer.")
#                         logger.info("Processing self answer")
#                         extracted_text = ""
#                         llm_call = False
#                         insight_data = ""

#                         # Initialize complete_chat based on project status
#                         # Use the project_data we already retrieved
#                         if not exists:
#                             complete_chat = []
#                         else:
#                             complete_chat = project_data.get("complete_chat", [])

#                         logger.info("--------------------PAGES, self answer---------------------")
#                         logger.info(f"Valid page numbers: {valid_page_numbers}")

#                         extracted_text_per_page, valid_page_numbers = "", 0

#                         async def event_generator():
#                             nonlocal llm_call, insight_data
#                             try:
#                                 async for event in utils.fake_llm_resonse(
#                                     source_to_select['query'], project_id,
#                                     mongo_manager, extracted_text, chat_history,
#                                     valid_page_numbers, response_type,
#                                     visualization, datetime.utcnow().date()
#                                 ):
#                                     event_data = json.loads(event)
#                                     if event_data["type"] == "final_response":
#                                         insight_data = event_data["content"]
#                                         if isinstance(insight_data, str):
#                                             insight_data = json.loads(insight_data)
#                                         insight_data["source_type"] = ""
#                                         query_title = await generate_title(prompt, insight_data["summary"])

#                                         insight_data["summary"] = f"""**{query_title}**\n\n{insight_data["summary"]}"""

#                                         logger.info(f"source type for self answer case: {insight_data['source_type']}")
#                                         llm_call = True

#                                         # Now process through pdf_processor using the main flow
#                                         async for pdf_event in pdf_processor.process_pdf(
#                                             file=file,
#                                             prompt=prompt,
#                                             project_id=project_id,
#                                             user_id=user_id,
#                                             page_number=page_number,
#                                             file_name=file_name,
#                                             file_type=file_type,
#                                             title=title,
#                                             extracted_text=extracted_text,
#                                             page_numbers=page_numbers,
#                                             extracted_text_per_page=extracted_text_per_page,
#                                             valid_page_numbers=valid_page_numbers,
#                                             intent=intent,
#                                             previous_responses=previous_responses,
#                                             previous_prompt=previous_prompt,
#                                             llm_call=llm_call,
#                                             insight_data=insight_data,
#                                             custom_prompt_name = None,
#                                             response_type=response_type,
#                                             visualization=visualization,
#                                             complete_chat=complete_chat
#                                         ):
#                                             yield pdf_event
#                                     else:
#                                         yield event
#                             except Exception as e:
#                                 logger.error(f"Error in event generator: {str(e)}")
#                                 handle_openai_error(e)

#                         return EventSourceResponse(event_generator())



#                 elif source_to_select['source'][0]['SelfAnswer'] == True:
#                     logger.info("Processing self answer")
#                     extracted_text = ""
#                     llm_call = False
#                     insight_data = ""

#                     # Initialize complete_chat based on project status
#                     # Use the project_data we already retrieved
#                     if not exists:
#                         complete_chat = []
#                     else:
#                         complete_chat = project_data.get("complete_chat", [])

#                     logger.info("--------------------PAGES, self answer---------------------")
#                     logger.info(f"Valid page numbers: {valid_page_numbers}")

#                     extracted_text_per_page, valid_page_numbers = "", 0

#                     async def event_generator():
#                         nonlocal llm_call, insight_data
#                         try:
#                             async for event in utils.fake_llm_resonse(
#                                 source_to_select['query'], project_id,
#                                 mongo_manager, extracted_text, chat_history,
#                                 valid_page_numbers, response_type,
#                                 visualization, datetime.utcnow().date()
#                             ):
#                                 event_data = json.loads(event)
#                                 if event_data["type"] == "final_response":
#                                     insight_data = event_data["content"]
#                                     if isinstance(insight_data, str):
#                                         insight_data = json.loads(insight_data)
#                                     insight_data["source_type"] = ""
#                                     query_title = await generate_title(prompt, insight_data["summary"])

#                                     insight_data["summary"] = f"""**{query_title}**\n\n{insight_data["summary"]}"""

#                                     logger.info(f"source type for self answer case: {insight_data['source_type']}")
#                                     llm_call = True

#                                     # Now process through pdf_processor using the main flow
#                                     async for pdf_event in pdf_processor.process_pdf(
#                                         file=file,
#                                         prompt=prompt,
#                                         project_id=project_id,
#                                         user_id=user_id,
#                                         page_number=page_number,
#                                         file_name=file_name,
#                                         file_type=file_type,
#                                         title=title,
#                                         extracted_text=extracted_text,
#                                         page_numbers=page_numbers,
#                                         extracted_text_per_page=extracted_text_per_page,
#                                         valid_page_numbers=valid_page_numbers,
#                                         intent=intent,
#                                         previous_responses=previous_responses,
#                                         previous_prompt=previous_prompt,
#                                         llm_call=llm_call,
#                                         insight_data=insight_data,
#                                         custom_prompt_name = None,
#                                         response_type=response_type,
#                                         visualization=visualization,
#                                         complete_chat=complete_chat
#                                     ):
#                                         yield pdf_event
#                                 else:
#                                     yield event
#                         except Exception as e:
#                             logger.error(f"Error in event generator: {str(e)}")
#                             handle_openai_error(e)

#                     return EventSourceResponse(event_generator())

#             except:

#                     logger.info("Switching to search in recently updated file")
#             # Use the project_data we already retrieved
#             if not exists:
#                 complete_chat=[]
#                 # complete_chat.append({
#                 #                 "filename": file_name or "",
#                 #                 "query": prompt,
#                 #                 "response": insight_data
#                 #             })
#             else:
#                 complete_chat=project_data.get("complete_chat", [])
#             print("file_context",len(extracted_text))

#             print("--------------------PAGES, use existing file---------------------")
#             print(valid_page_numbers)

#             # if not llm_call:
#             extracted_text_per_page,valid_page_numbers="",0

#             async def event_generator():

#                 async for event in pdf_processor.process_pdf(
#                     file=file,
#                     prompt=prompt,
#                     project_id=project_id,
#                     user_id=user_id,
#                     page_number=page_number,
#                     file_name=file_name,
#                     file_type=file_type,
#                     title=title,
#                     extracted_text=extracted_text,
#                     page_numbers=page_numbers,
#                     extracted_text_per_page=extracted_text_per_page,
#                     valid_page_numbers=valid_page_numbers,
#                     intent=intent,
#                     previous_responses=previous_responses,
#                     previous_prompt=previous_prompt,
#                     llm_call=llm_call,
#                     insight_data=insight_data,
#                     custom_prompt_name = None,
#                     response_type=response_type,
#                     visualization=visualization,
#                     complete_chat=complete_chat
#                 ):
#                     yield event


#         return EventSourceResponse(event_generator())

#     except Exception as e:
#         logger.error(f"Error in process_files: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

# @router.put(f"/{config.updateURL}/")
# async def update_title(request: Request, title_update_request: insight_model.TitleUpdateRequest):
#     logger.info("update title called")

#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization')
#         if not token or not token.startswith('Bearer '):
#             logger.error("Authorization header is missing or invalid")
#             raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

#         token = token.split(' ')[1]
#         logger.info("about to decode token")
#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")


#     try:
#         mongo_manager.update_project(title_update_request.project_id, title_update_request.new_title)
#     except Exception as e:
#         logger.error(f"Error updating title: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))
#     return {"message": "Title updated successfully."}

# @router.post(f"/{config.set_model_url}/")
# async def set_model(request: Request, set_model_request: insight_model.SetModelRequest):

#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization')
#         if not token or not token.startswith('Bearer '):
#             logger.error("Authorization header is missing or invalid")
#             raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

#         token = token.split(' ')[1]

#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")


#     mongo_manager.set_model_for_project(set_model_request.project_id, set_model_request.model_name)
#     return {"message": f"Model set to {set_model_request.model_name} successfully."}

# @router.get(f"/{config.get_model_url}/{{project_id}}")
# async def get_model(request: Request, project_id: str):
#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization')
#         if not token or not token.startswith('Bearer '):
#             logger.error("Authorization header is missing or invalid")
#             raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

#         token = token.split(' ')[1]

#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")



#     model_name = mongo_manager.get_model_for_project(project_id)
#     return model_name


# # @router.get(f"/{config.download_file_url}/{{file_path:path}}")
# @router.get(f"/chat/{config.download_file_url}/{{file_path:path}}")
# async def download_file(request: Request, file_path: str):
#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization').split(' ')[1]
#         if not token:
#             logger.error("Authorization header is missing")
#             raise HTTPException(status_code=401, detail="Authorization header is missing")

#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")


#     logger.info(f"Attempting to download file: {file_path}")

#     # Decode the URL-encoded file path
#     decoded_file_path = urllib.parse.unquote(file_path)
#     logger.info(f"Decoded file path: {decoded_file_path}")

#     # Remove any leading slashes
#     decoded_file_path = decoded_file_path.lstrip('/')

#     # Split the path into project_id and file_name
#     path_parts = decoded_file_path.split('/', 1)
#     if len(path_parts) != 2:
#         raise HTTPException(status_code=400, detail="Invalid file path")

#     project_id, file_name = path_parts

#     # Construct the full file path
#     full_file_path = os.path.join(os.getcwd(), project_id, file_name)
#     logger.info(f"Full file path: {full_file_path}")

#     if os.path.exists(full_file_path):
#         logger.info(f"File found: {full_file_path}")
#         return FileResponse(full_file_path, filename=file_name)
#     else:
#         logger.error(f"File not found: {full_file_path}")
#         raise HTTPException(status_code=404, detail=f"File not found: {decoded_file_path}")




# @router.post(f"/{config.compliance_generation_url}/")
# async def generate_compliance_report(
#     request: Request,
#     project_id: str = Form(...),
#     user_id: str = Form(...),
#     file_ids: str = Form(...),
#     framework_type: str = Form(...),
#     generate_pdf: bool = Form(default=False)
# ):
#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization').split(' ')[1]
#         if not token:
#             logger.error("Authorization header is missing")
#             raise HTTPException(status_code=401, detail="Authorization header is missing")

#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")





#     logger.info(f"Starting compliance report generation for project {project_id}, user {user_id}, framework {framework_type}")

#     # Convert _id to project_id
#     actual_project_id = utils.get_project_id_from_object_id(project_id)
#     if not actual_project_id:
#         logger.error(f"Project with _id {project_id} not found")
#         raise HTTPException(status_code=404, detail="Project not found")

#     logger.info(f"Received file IDs: {file_ids}, generate_pdf: {generate_pdf}")

#     logger.info(f"File IDs Type: {type(file_ids)}")

#     file_ids = file_ids.replace("'", '"')

#     # Parse file IDs from JSON string
#     file_ids = jp.loads(file_ids)

#     logger.info(f"Parsed file IDs to type: {type(file_ids)}")

#     # Get project data from MongoDB
#     project_data, exists, _ = mongo_manager.get_or_initialize_project(actual_project_id)
#     if not exists:
#         logger.error(f"Project {actual_project_id} not found")
#         raise HTTPException(status_code=404, detail="Project not found")

#     # Verify user has access to this project
#     if user_id != project_data.get("user_id", ""):
#         logger.error(f"User {user_id} not authorized for project {actual_project_id}")
#         raise HTTPException(status_code=403, detail="User not authorized for this project")

#     # Create mapping of file_id to file_name
#     file_id_to_name = {
#         file_detail["file_id"]: file_detail["file_name"]
#         for file_detail in project_data.get("file_details", [])
#         if "file_id" in file_detail
#     }

#     # Get file names for the provided file IDs
#     file_names = []
#     for file_id in file_ids:
#         if file_id not in file_id_to_name:
#             logger.error(f"File ID {file_id} not found in project {actual_project_id}")
#             raise HTTPException(status_code=404, detail=f"File ID {file_id} not found in project")
#         file_names.append(file_id_to_name[file_id])

#     # Create compliance reports directory if it doesn't exist
#     compliance_dir = os.path.join(config.file_path, actual_project_id, "compliance_reports")
#     os.makedirs(compliance_dir, exist_ok=True)

#     framework_type = framework_type.upper()

#     if generate_pdf:
#         logger.info("Generating combined PDF compliance report")
#         pdf_path = None
#         # Generate PDF file name
#         current_time = datetime.utcnow()
#         temp_filename = f"temp_{uuid.uuid4().hex}_{framework_type}_compliance.pdf"
#         pdf_path = os.path.join(compliance_dir, temp_filename)

#         # Process each file
#         valid_reports = []  # Track valid reports
#         missing_files = []  # Track missing files

#         # First validate all files have reports
#         for file_name in file_names:
#             report_filename = f"{file_name.split('.')[0]}_{framework_type}_compliance.json"
#             report_path = os.path.join(compliance_dir, report_filename)

#             if not os.path.exists(report_path):
#                 missing_files.append(file_name)
#                 logger.warning(f"Report file not found: {report_path}")
#                 continue

#             try:
#                 # Verify report file contains required data
#                 with open(report_path, 'r') as f:
#                     report_data = json.load(f)

#                 # Check for required fields
#                 required_fields = ['file_name', 'framework', 'average_score', 'summary', 'created_at']
#                 missing_fields = [field for field in required_fields if field not in report_data]

#                 if missing_fields:
#                     logger.error(f"Report for {file_name} is missing required fields: {missing_fields}")
#                     missing_files.append(file_name)
#                     continue

#                 valid_reports.append((file_name, report_path, report_data))

#             except json.JSONDecodeError:
#                 logger.error(f"Invalid JSON in report file for {file_name}")
#                 missing_files.append(file_name)
#                 continue
#             except Exception as e:
#                 logger.error(f"Error reading report file for {file_name}: {str(e)}")
#                 missing_files.append(file_name)
#                 continue

#         # If no valid reports found, raise 404
#         if not valid_reports:
#             error_msg = f"No valid compliance reports found for the requested files. Missing reports for: {', '.join(missing_files)}"
#             logger.error(error_msg)
#             # Clean up temporary PDF file if it exists
#             if pdf_path and os.path.exists(pdf_path):
#                 os.unlink(pdf_path)
#             raise HTTPException(status_code=404, detail=error_msg)

#         # Create the PDF document
#         doc = SimpleDocTemplate(
#             pdf_path,
#             pagesize=A4,
#             rightMargin=72,
#             leftMargin=72,
#             topMargin=72,
#             bottomMargin=72
#         )

#         # Define styles
#         styles = getSampleStyleSheet()
#         title_style = ParagraphStyle(
#             'CustomTitle',
#             parent=styles['Heading1'],
#             fontSize=24,
#             spaceAfter=15,
#             alignment=TA_CENTER
#         )
#         heading_style = ParagraphStyle(
#             'CustomHeading',
#             parent=styles['Heading2'],
#             fontSize=16,
#             spaceAfter=6
#         )
#         normal_style = ParagraphStyle(
#             'CustomNormal',
#             parent=styles['Normal'],
#             fontSize=12,
#             spaceAfter=3
#         )
#         summary_style = ParagraphStyle(
#             'CustomSummary',
#             parent=styles['Normal'],
#             fontSize=12,
#             spaceAfter=3,
#             leading=20,
#             alignment=TA_LEFT,
#             spaceBefore=3,
#             firstLineIndent=20
#         )
#         bold_style = ParagraphStyle(
#             'CustomBold',
#             parent=styles['Normal'],
#             fontSize=12,
#             spaceAfter=6,
#             leading=20,
#             alignment=TA_LEFT,
#             firstLineIndent=20,
#             fontName='Helvetica-Bold'
#         )

#         # Define color-coded score styles
#         score_style_green = ParagraphStyle(
#             'ScoreGreen',
#             parent=styles['Normal'],
#             fontSize=14,
#             spaceAfter=6,
#             textColor=colors.green,
#             fontName='Helvetica-Bold'
#         )
#         score_style_yellow = ParagraphStyle(
#             'ScoreYellow',
#             parent=styles['Normal'],
#             fontSize=14,
#             spaceAfter=6,
#             textColor=colors.yellow,  # Changed from orangered to yellow as per requirements
#             fontName='Helvetica-Bold'
#         )
#         score_style_red = ParagraphStyle(
#             'ScoreRed',
#             parent=styles['Normal'],
#             fontSize=14,
#             spaceAfter=6,
#             textColor=colors.red,
#             fontName='Helvetica-Bold'
#         )

#         def process_summary_text(text: str) -> List[Paragraph]:
#             """
#             Process summary text to handle bold sections marked with asterisks.
#             Returns a list of Paragraph objects with appropriate styling.
#             """
#             paragraphs = []

#             # Split text into lines
#             lines = text.split('\n')

#             for line in lines:
#                 if not line.strip():
#                     continue

#                 # Find all bold sections (text between ** **)
#                 bold_pattern = r'\*\*(.*?)\*\*'

#                 # Split the line at bold sections
#                 parts = re.split(bold_pattern, line)

#                 if len(parts) == 1:
#                     # No bold text in this line
#                     paragraphs.append(Paragraph(line.strip(), summary_style))
#                 else:
#                     # Combine parts with appropriate styling
#                     formatted_parts = []
#                     for i, part in enumerate(parts):
#                         if i % 2 == 0:  # Regular text
#                             if part.strip():
#                                 formatted_parts.append(part)
#                         else:  # Bold text
#                             formatted_parts.append(f'<b>{part}</b>')

#                     # Join parts and create a paragraph with mixed styling
#                     combined_text = ''.join(formatted_parts)
#                     paragraphs.append(Paragraph(combined_text, summary_style))

#             return paragraphs

#         # Build the PDF content
#         elements = []

#         # Add title
#         elements.append(Paragraph(f"{framework_type} Compliance Report", title_style))
#         elements.append(Paragraph(
#             f"Generated on: {current_time.strftime('%B %d, %Y at %I:%M %p')}",
#             normal_style
#         ))
#         elements.append(Spacer(1, 15))

#         # Add a legend for the color-coding
#         elements.append(Paragraph("Score Legend:", heading_style))
#         elements.append(Spacer(1, 6))

#         # Create a table for the legend
#         legend_data = [
#             ["Score Range", "Rating", "Color"],
#             ["8.0 - 10.0", "Good", ""],
#             ["6.0 - 7.9", "Needs Improvement", ""],
#             ["0.0 - 5.9", "Critical", ""]
#         ]
#         legend_table = Table(legend_data, colWidths=[1.5*inch, 2*inch, 1*inch])

#         # Style the legend table
#         legend_style = [
#             ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
#             ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
#             ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
#             ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
#             ('FONTSIZE', (0, 0), (-1, 0), 14),
#             ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
#             ('BACKGROUND', (0, 1), (-1, -1), colors.white),
#             ('GRID', (0, 0), (-1, -1), 1, colors.black),
#             # Color the rating cells
#             ('BACKGROUND', (2, 1), (2, 1), colors.green),
#             ('BACKGROUND', (2, 2), (2, 2), colors.yellow),
#             ('BACKGROUND', (2, 3), (2, 3), colors.red),
#             # Make the text bold in the first column
#             ('FONTNAME', (0, 1), (0, 3), 'Helvetica-Bold')
#         ]

#         legend_table.setStyle(TableStyle(legend_style))
#         elements.append(legend_table)
#         elements.append(Spacer(1, 30))

#         # Process each validated file
#         for i, (file_name, report_path, report_data) in enumerate(valid_reports):
#             if i > 0:
#                 elements.append(Spacer(1, 15))

#             # Add file information
#             elements.append(Paragraph(f"File: {report_data.get('file_name', '')}", heading_style))
#             elements.append(Paragraph(f"Framework: {report_data.get('framework', '')}", normal_style))

#             # Get the average score and apply color-coding
#             average_score = report_data.get('average_score', 0)

#             # Determine which style to use based on score range
#             if average_score >= 8.0:
#                 score_style = score_style_green
#                 score_text = f"Overall Average Score: {average_score:.2f} (Good)"
#                 bar_color = colors.green
#             elif average_score >= 6.0:
#                 score_style = score_style_yellow
#                 score_text = f"Overall Average Score: {average_score:.2f} (Needs Improvement)"
#                 bar_color = colors.yellow
#             else:
#                 score_style = score_style_red
#                 score_text = f"Overall Average Score: {average_score:.2f} (Critical)"
#                 bar_color = colors.red

#             elements.append(Paragraph(score_text, score_style))

#             # Add a visual progress bar for the score
#             bar_width = 4 * inch  # Same width as the table
#             progress = min(max(average_score / 10.0, 0), 1)  # Normalize score to 0-1 range

#             # Create a progress bar using a table with colored cells
#             progress_bar_data = [['', '']]
#             progress_bar = Table(progress_bar_data, colWidths=[bar_width * progress, bar_width * (1 - progress)])
#             progress_bar.setStyle(TableStyle([
#                 ('BACKGROUND', (0, 0), (0, 0), bar_color),
#                 ('BACKGROUND', (1, 0), (1, 0), colors.lightgrey),
#                 ('LINEBELOW', (0, 0), (1, 0), 1, colors.black),
#                 ('LINEABOVE', (0, 0), (1, 0), 1, colors.black),
#                 ('LINEBEFORE', (0, 0), (0, 0), 1, colors.black),
#                 ('LINEAFTER', (1, 0), (1, 0), 1, colors.black),
#             ]))

#             elements.append(Spacer(1, 6))
#             elements.append(progress_bar)
#             elements.append(Spacer(1, 12))

#             # Add individual factors as a table
#             factors = report_data.get('individual_factors_score', {})
#             if factors:
#                 elements.append(Spacer(1, 12))
#                 elements.append(Paragraph("Individual Factor Scores:", heading_style))

#                 # Create table data
#                 table_data = [["Factor", "Score"]]
#                 for factor, score in factors.items():
#                     table_data.append([factor, f"{float(score):.2f}"])

#                 # Create and style the table
#                 table = Table(table_data, colWidths=[4*inch, 1*inch])

#                 # Basic table styling
#                 table_style = [
#                     ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
#                     ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
#                     ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
#                     ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
#                     ('FONTSIZE', (0, 0), (-1, 0), 14),
#                     ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
#                     ('BACKGROUND', (0, 1), (-1, -1), colors.white),
#                     ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
#                     ('FONTSIZE', (0, 1), (-1, -1), 12),
#                     ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
#                     ('GRID', (0, 0), (-1, -1), 1, colors.black)
#                 ]

#                 # Add color-coding for each score in the table
#                 row_num = 1  # Start from row 1 (after header)
#                 for factor, score in factors.items():
#                     score_value = float(score)
#                     if score_value >= 8.0:
#                         table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.green))
#                         table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
#                     elif score_value >= 6.0:
#                         table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.yellow))
#                         table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
#                     else:
#                         table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.red))
#                         table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
#                     row_num += 1

#                 table.setStyle(TableStyle(table_style))
#                 elements.append(table)

#             # Add summary
#             elements.append(Spacer(1, 12))
#             elements.append(Paragraph("Summary:", heading_style))
#             summary = report_data.get('summary', '')

#             # Process summary text and add resulting paragraphs
#             summary_paragraphs = process_summary_text(summary)
#             for paragraph in summary_paragraphs:
#                 elements.append(paragraph)
#                 elements.append(Spacer(1, 3))

#             # Add creation date with reduced spacing
#             elements.append(Spacer(1, 3))
#             created_at = datetime.fromisoformat(report_data.get('created_at', ''))
#             elements.append(Paragraph(
#                 f"Report created: {created_at.strftime('%B %d, %Y at %I:%M %p')}",
#                 normal_style
#             ))

#         # Build the PDF
#         doc.build(elements)
#         logger.info(f"Generated temporary PDF report: {pdf_path}")

#         # Return the PDF file and ensure cleanup
#         background_tasks = BackgroundTask(
#             lambda: os.unlink(pdf_path) if os.path.exists(pdf_path) else None
#         )
#         return FileResponse(
#             pdf_path,
#             filename=f"combined_{framework_type}_compliance_{current_time.strftime('%Y%m%d_%H%M%S')}.pdf",
#             media_type="application/pdf",
#             background=background_tasks
#         )

#     else:
#         # Original functionality for generating individual reports
#         all_reports = []

#         # Generate report for each file individually
#         for file_name in file_names:
#             logger.info(f"Processing file: {file_name}")
#             ext = file_name.split(".")[-1].lower()

#             # Get content for this specific file
#             try:
#                 # Always use pickle files regardless of file type
#                 pkl_file_path = os.path.join(config.file_path, actual_project_id,
#                                            config.non_data_analysis_folder_name if ext not in ["csv", "xlsx"]
#                                            else config.data_analysis_folder_name,
#                                            f"{file_name.split('.')[0]}.pkl")

#                 logger.info(f"Reading pickle file: {pkl_file_path}")
#                 content = utils.read_pickle_file_for_compliance(pkl_file_path)

#                 # For Excel/CSV files, fall back to direct file reading if pickle doesn't exist
#                 if not content and ext in ["csv", "xlsx"]:
#                     logger.info(f"Pickle file not found for {file_name}, attempting direct file read")
#                     file_path = os.path.join(config.file_path, actual_project_id,
#                                            config.data_analysis_folder_name, file_name)
#                     if os.path.exists(file_path):
#                         content = utils.read_data_file(file_path)
#                         logger.info(f"Successfully read file directly: {file_path}")

#                 # If still no content, raise error
#                 if not content:
#                     error_msg = f"Failed to read content from file: {file_name}"
#                     logger.error(error_msg)
#                     raise ValueError(error_msg)

#                 logger.info(f"File content read having: {len(content)} bytes")

#                 # Generate compliance report for this file
#                 logger.info(f"Generating compliance report for {file_name}")
#                 prompt = get_compliance_prompt(content, framework_type)
#                 compliance_report = get_compliance_report(prompt)
#                 report_data = jp.loads(compliance_report)
#                 logger.info(f"Successfully generated compliance report for {file_name}")

#                 # Add file name to the report
#                 report_data["file_name"] = file_name

#                 # Create or override report file
#                 report_filename = f"{file_name.split('.')[0]}_{framework_type}_compliance.json"
#                 report_path = os.path.join(compliance_dir, report_filename)

#                 # Delete existing report file if it exists
#                 if os.path.exists(report_path):
#                     logger.info(f"Removing existing report file: {report_path}")
#                     os.remove(report_path)

#                 # Get current timestamp
#                 current_time = datetime.utcnow()

#                 # Add timestamp to report data before saving to file
#                 report_data["created_at"] = current_time.isoformat()

#                 # Save new report
#                 with open(report_path, 'w') as f:
#                     jp.dump(report_data, f, indent=4)
#                 logger.info(f"Saved compliance report to file: {report_path}")

#                 # Check if the file is valid for compliance reporting
#                 is_valid = report_data.get('is_valid', True)  # Default to True for backward compatibility

#                 if not is_valid:
#                     # If file is not valid, delete the report file we just created
#                     if os.path.exists(report_path):
#                         os.remove(report_path)
#                         logger.info(f"Removed invalid compliance report file: {report_path}")

#                     # Get the reason for invalidity
#                     reason = report_data.get('reason', 'No reason provided')
#                     error_msg = f"The content of {file_name} is not valid for {framework_type} framework check: {reason}"
#                     logger.error(error_msg)

#                     # Raise an HTTP exception to inform the user
#                     # The status code will not be included in the response message
#                     # because FastAPI uses the 'detail' property which doesn't include the status code
#                     raise HTTPException(status_code=400, detail=f"The content of {file_name} is not valid for {framework_type.upper()} framework type.")

#                 # If valid, proceed with saving the report
#                 average_score = report_data.get('average_score', 0)
#                 logger.info(f"Average compliance score for {file_name}: {average_score}")

#                 # Delete any existing compliance report for this file and framework type
#                 delete_result = mongo_manager.compliance_collection.delete_many({
#                     "project_id": actual_project_id,
#                     "file_name": file_name,
#                     "framework_type": framework_type
#                 })
#                 logger.info(f"Deleted {delete_result.deleted_count} existing compliance records for {file_name}")

#                 # Save to compliance collection
#                 compliance_data = {
#                     "file_name": file_name,
#                     "framework_type": framework_type,
#                     "average_score": average_score,
#                     "report_path": report_path,
#                     "created_at": current_time
#                 }

#                 mongo_manager.save_compliance_report(actual_project_id, compliance_data)
#                 logger.info(f"Successfully saved compliance data to MongoDB for {file_name}")

#                 # Include timestamp in the response
#                 all_reports.append({
#                     "file_name": file_name,
#                     "average_score": compliance_data["average_score"],
#                     "framework_type": framework_type,
#                     "created_at": current_time.isoformat()
#                 })

#             except HTTPException as http_e:
#                 # Re-raise HTTP exceptions without modification
#                 # Log the error using the detail property to avoid including the status code in logs
#                 logger.error(f"Error processing file {file_name}: {http_e.detail}")
#                 raise
#             except Exception as e:
#                 error_msg = f"Error processing file {file_name}: {str(e)}"
#                 logger.error(error_msg)
#                 raise HTTPException(status_code=500, detail=error_msg)

#         logger.info(f"Returning compliance reports with timestamps for {len(all_reports)} files")
#         return all_reports

# @router.get(f"/{config.get_file_details_url}/{{user_id}}/{{project_id}}")
# async def get_project_files(request: Request, user_id: str, project_id: str):

#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization').split(' ')[1]
#         if not token:
#             logger.error("Authorization header is missing")
#             raise HTTPException(status_code=401, detail="Authorization header is missing")

#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")




#     try:
#         # Convert _id to project_id
#         actual_project_id = utils.get_project_id_from_object_id(project_id)
#         if not actual_project_id:
#             logger.error(f"Project with _id {project_id} not found")
#             raise HTTPException(status_code=404, detail="Project not found")

#         file_details = mongo_manager.get_file_details(actual_project_id, user_id)
#         # Extract only the required fields
#         simplified_details = [
#             {
#                 "file_id": detail["file_id"],
#                 "file_name": detail["file_name"]
#             }
#             for detail in file_details
#         ]
#         return simplified_details
#     except Exception as e:
#         logger.error(f"Error getting file details for user {user_id}, project {project_id}: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

# @router.get(f"/{{user_id}}/{{project_id}}/{config.get_compliance_reports_url}")
# async def get_compliance_reports(request: Request, user_id: str, project_id: str):

#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization').split(' ')[1]
#         if not token:
#             logger.error("Authorization header is missing")
#             raise HTTPException(status_code=401, detail="Authorization header is missing")

#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")







#     try:
#         logger.info(f"Retrieving compliance reports for project {project_id} and user {user_id}")

#         # Convert _id to project_id
#         actual_project_id = utils.get_project_id_from_object_id(project_id)
#         if not actual_project_id:
#             logger.error(f"Project with _id {project_id} not found")
#             raise HTTPException(status_code=404, detail="Project not found")

#         # Check if project exists and user has access
#         project_data, exists, _ = mongo_manager.get_or_initialize_project(actual_project_id)

#         if not exists:
#             logger.error(f"Project {actual_project_id} not found")
#             raise HTTPException(status_code=404, detail="Project not found")

#         # Check if user has access to this project
#         if user_id != project_data.get("user_id", ""):
#             logger.error(f"User {user_id} not authorized for project {actual_project_id}")
#             raise HTTPException(status_code=403, detail="User not authorized to access this project")

#         # Get all compliance reports for the project
#         reports = mongo_manager.get_compliance_reports(actual_project_id)

#         # Check if there are any reports
#         if not reports:
#             logger.info(f"No compliance reports found for project {actual_project_id}")
#             # raise HTTPException(
#             #     status_code=404,
#             #     detail="No compliance reports have been generated for this project"
#             # )
#             return []

#         # Format the response
#         formatted_reports = []
#         for report in reports:
#             formatted_report = {
#                 "file_name": report.get("file_name"),
#                 "framework_type": report.get("framework_type"),
#                 "average_score": report.get("average_score"),
#                 "created_at": report.get("created_at").isoformat() if report.get("created_at") else None
#             }
#             formatted_reports.append(formatted_report)

#         logger.info(f"Found {len(formatted_reports)} compliance reports for project {actual_project_id}")
#         return formatted_reports

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error retrieving compliance reports for project {actual_project_id}: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

# @router.get(f"/{{user_id}}/{{project_id}}/{config.get_all_compliance_reports_pdf_url}")
# async def get_all_compliance_reports_pdf(request: Request, user_id: str, project_id: str):

#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization').split(' ')[1]
#         if not token:
#             logger.error("Authorization header is missing")
#             raise HTTPException(status_code=401, detail="Authorization header is missing")

#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")





#     try:
#         logger.info(f"Generating comprehensive compliance PDF for project {project_id}")

#         # Convert _id to project_id
#         actual_project_id = utils.get_project_id_from_object_id(project_id)
#         if not actual_project_id:
#             logger.error(f"Project with _id {project_id} not found")
#             raise HTTPException(status_code=404, detail="Project not found")

#         # Verify project and user access
#         project_data, exists, _ = mongo_manager.get_or_initialize_project(actual_project_id)

#         if not exists:
#             logger.error(f"Project {actual_project_id} not found")
#             raise HTTPException(status_code=404, detail="Project not found")

#         if user_id != project_data.get("user_id", ""):
#             logger.error(f"User {user_id} not authorized for project {actual_project_id}")
#             raise HTTPException(status_code=403, detail="User not authorized to access this project")

#         # Get all compliance reports
#         reports = mongo_manager.get_compliance_reports(actual_project_id)

#         if not reports:
#             logger.error(f"No compliance reports found for project {actual_project_id}")
#             raise HTTPException(status_code=404, detail="No compliance reports found for this project")

#         # Create compliance reports directory
#         compliance_dir = os.path.join(config.file_path, actual_project_id, "compliance_reports")
#         os.makedirs(compliance_dir, exist_ok=True)

#         # Generate PDF filename
#         current_time = datetime.utcnow()
#         temp_filename = f"temp_{uuid.uuid4().hex}_all_compliance_reports.pdf"
#         pdf_path = os.path.join(compliance_dir, temp_filename)

#         # Create PDF document
#         doc = SimpleDocTemplate(
#             pdf_path,
#             pagesize=A4,
#             rightMargin=72,
#             leftMargin=72,
#             topMargin=72,
#             bottomMargin=72
#         )

#         # Define styles (existing code)
#         styles = getSampleStyleSheet()
#         title_style = ParagraphStyle(
#             'CustomTitle',
#             parent=styles['Heading1'],
#             fontSize=24,
#             spaceAfter=30,
#             alignment=TA_CENTER
#         )
#         heading_style = ParagraphStyle(
#             'CustomHeading',
#             parent=styles['Heading2'],
#             fontSize=16,
#             spaceAfter=12
#         )
#         subheading_style = ParagraphStyle(
#             'CustomSubHeading',
#             parent=styles['Heading3'],
#             fontSize=14,
#             spaceAfter=8
#         )
#         normal_style = ParagraphStyle(
#             'CustomNormal',
#             parent=styles['Normal'],
#             fontSize=12,
#             spaceAfter=6
#         )
#         summary_style = ParagraphStyle(
#             'CustomSummary',
#             parent=styles['Normal'],
#             fontSize=12,
#             spaceAfter=6,
#             leading=20,
#             alignment=TA_LEFT,
#             firstLineIndent=20
#         )
#         bold_style = ParagraphStyle(
#             'CustomBold',
#             parent=styles['Normal'],
#             fontSize=12,
#             spaceAfter=6,
#             leading=20,
#             alignment=TA_LEFT,
#             firstLineIndent=20,
#             fontName='Helvetica-Bold'
#         )

#         # Define color-coded score styles
#         score_style_green = ParagraphStyle(
#             'ScoreGreen',
#             parent=styles['Normal'],
#             fontSize=14,
#             spaceAfter=6,
#             textColor=colors.green,
#             fontName='Helvetica-Bold'
#         )
#         score_style_yellow = ParagraphStyle(
#             'ScoreYellow',
#             parent=styles['Normal'],
#             fontSize=14,
#             spaceAfter=6,
#             textColor=colors.yellow,  # Changed from orangered to yellow as per requirements
#             fontName='Helvetica-Bold'
#         )
#         score_style_red = ParagraphStyle(
#             'ScoreRed',
#             parent=styles['Normal'],
#             fontSize=14,
#             spaceAfter=6,
#             textColor=colors.red,
#             fontName='Helvetica-Bold'
#         )

#         def process_summary_text(text: str) -> List[Paragraph]:
#             """
#             Process summary text to handle bold sections marked with asterisks.
#             Returns a list of Paragraph objects with appropriate styling.
#             """
#             paragraphs = []

#             # Split text into lines
#             lines = text.split('\n')

#             for line in lines:
#                 if not line.strip():
#                     continue

#                 # Find all bold sections (text between ** **)
#                 bold_pattern = r'\*\*(.*?)\*\*'

#                 # Split the line at bold sections
#                 parts = re.split(bold_pattern, line)

#                 if len(parts) == 1:
#                     # No bold text in this line
#                     paragraphs.append(Paragraph(line.strip(), summary_style))
#                 else:
#                     # Combine parts with appropriate styling
#                     formatted_parts = []
#                     for i, part in enumerate(parts):
#                         if i % 2 == 0:  # Regular text
#                             if part.strip():
#                                 formatted_parts.append(part)
#                         else:  # Bold text
#                             formatted_parts.append(f'<b>{part}</b>')

#                     # Join parts and create a paragraph with mixed styling
#                     combined_text = ''.join(formatted_parts)
#                     paragraphs.append(Paragraph(combined_text, summary_style))

#             return paragraphs

#         # Build PDF content
#         elements = []

#         # Add title
#         elements.append(Paragraph("Comprehensive Compliance Report", title_style))
#         elements.append(Paragraph(
#             f"Generated on: {current_time.strftime('%B %d, %Y at %I:%M %p')}",
#             normal_style
#         ))
#         elements.append(Spacer(1, 15))

#         # Add a legend for the color-coding
#         elements.append(Paragraph("Score Legend:", heading_style))
#         elements.append(Spacer(1, 6))

#         # Create a table for the legend
#         legend_data = [
#             ["Score Range", "Rating", "Color"],
#             ["8.0 - 10.0", "Good", ""],
#             ["6.0 - 7.9", "Needs Improvement", ""],
#             ["0.0 - 5.9", "Critical", ""]
#         ]
#         legend_table = Table(legend_data, colWidths=[1.5*inch, 2*inch, 1*inch])

#         # Style the legend table
#         legend_style = [
#             ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
#             ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
#             ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
#             ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
#             ('FONTSIZE', (0, 0), (-1, 0), 14),
#             ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
#             ('BACKGROUND', (0, 1), (-1, -1), colors.white),
#             ('GRID', (0, 0), (-1, -1), 1, colors.black),
#             # Color the rating cells
#             ('BACKGROUND', (2, 1), (2, 1), colors.green),
#             ('BACKGROUND', (2, 2), (2, 2), colors.yellow),
#             ('BACKGROUND', (2, 3), (2, 3), colors.red),
#             # Make the text bold in the first column
#             ('FONTNAME', (0, 1), (0, 3), 'Helvetica-Bold')
#         ]

#         legend_table.setStyle(TableStyle(legend_style))
#         elements.append(legend_table)
#         elements.append(Spacer(1, 30))

#         # Group reports by framework type
#         framework_reports = {}
#         for report in reports:
#             framework = report.get('framework_type', 'UNKNOWN')
#             if framework not in framework_reports:
#                 framework_reports[framework] = []
#             framework_reports[framework].append(report)

#         # Process each framework type
#         for framework, framework_group in framework_reports.items():
#             elements.append(Paragraph(f"{framework} Compliance Reports", heading_style))
#             elements.append(Spacer(1, 12))

#             # Process each report in the framework group
#             for report in framework_group:
#                 # Add file information
#                 elements.append(Paragraph(f"File: {report.get('file_name', '')}", subheading_style))

#                 # Get the average score and apply color-coding
#                 average_score = report.get('average_score', 0)

#                 # Determine which style to use based on score range
#                 if average_score >= 8.0:
#                     score_style = score_style_green
#                     score_text = f"Overall Average Score: {average_score:.2f} (Good)"
#                     bar_color = colors.green
#                 elif average_score >= 6.0:
#                     score_style = score_style_yellow
#                     score_text = f"Overall Average Score: {average_score:.2f} (Needs Improvement)"
#                     bar_color = colors.yellow
#                 else:
#                     score_style = score_style_red
#                     score_text = f"Overall Average Score: {average_score:.2f} (Critical)"
#                     bar_color = colors.red

#                 elements.append(Paragraph(score_text, score_style))

#                 # Add a visual progress bar for the score
#                 bar_width = 4 * inch  # Same width as the table
#                 progress = min(max(average_score / 10.0, 0), 1)  # Normalize score to 0-1 range

#                 # Create a progress bar using a table with colored cells
#                 progress_bar_data = [['', '']]
#                 progress_bar = Table(progress_bar_data, colWidths=[bar_width * progress, bar_width * (1 - progress)])
#                 progress_bar.setStyle(TableStyle([
#                     ('BACKGROUND', (0, 0), (0, 0), bar_color),
#                     ('BACKGROUND', (1, 0), (1, 0), colors.lightgrey),
#                     ('LINEBELOW', (0, 0), (1, 0), 1, colors.black),
#                     ('LINEABOVE', (0, 0), (1, 0), 1, colors.black),
#                     ('LINEBEFORE', (0, 0), (0, 0), 1, colors.black),
#                     ('LINEAFTER', (1, 0), (1, 0), 1, colors.black),
#                 ]))

#                 elements.append(Spacer(1, 6))
#                 elements.append(progress_bar)
#                 elements.append(Spacer(1, 12))

#                 # Get the full report content from the report file
#                 # Use the same case (uppercase) as when the file was created
#                 report_filename = f"{report['file_name'].split('.')[0]}_{framework}_compliance.json"
#                 report_path = os.path.join(compliance_dir, report_filename)

#                 try:
#                     with open(report_path, 'r') as f:
#                         full_report = json.load(f)

#                     # Check if the file is valid for compliance reporting
#                     # Note: This is for backward compatibility with reports generated before the change
#                     # New reports will always be valid since we now raise an error for invalid files
#                     is_valid = full_report.get('is_valid', True)

#                     if not is_valid:
#                         # If file is not valid for compliance reporting, show the reason
#                         elements.append(Spacer(1, 12))
#                         elements.append(Paragraph("Not Valid for Compliance Reporting", normal_style))
#                         reason = full_report.get('reason', 'No reason provided')
#                         elements.append(Paragraph(reason, summary_style))

#                         # Log a warning about this legacy invalid report
#                         logger.warning(f"Found legacy invalid compliance report for {report.get('file_name', '')}: {reason}")
#                     else:
#                         # Add individual factors as a table
#                         factors = full_report.get('individual_factors_score', {})
#                         if factors:
#                             elements.append(Spacer(1, 12))
#                             elements.append(Paragraph("Individual Factor Scores:", normal_style))

#                             # Create table data
#                             table_data = [["Factor", "Score"]]
#                             for factor, score in factors.items():
#                                 table_data.append([factor, f"{float(score):.2f}"])

#                             # Create and style the table
#                             table = Table(table_data, colWidths=[4*inch, 1*inch])

#                             # Basic table styling
#                             table_style = [
#                                 ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
#                                 ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
#                                 ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
#                                 ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
#                                 ('FONTSIZE', (0, 0), (-1, 0), 14),
#                                 ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
#                                 ('BACKGROUND', (0, 1), (-1, -1), colors.white),
#                                 ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
#                                 ('FONTSIZE', (0, 1), (-1, -1), 12),
#                                 ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
#                                 ('GRID', (0, 0), (-1, -1), 1, colors.black)
#                             ]

#                             # Add color-coding for each score in the table
#                             row_num = 1  # Start from row 1 (after header)
#                             for factor, score in factors.items():
#                                 score_value = float(score)
#                                 if score_value >= 8.0:
#                                     table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.green))
#                                     table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
#                                 elif score_value >= 6.0:
#                                     table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.yellow))
#                                     table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
#                                 else:
#                                     table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.red))
#                                     table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
#                                 row_num += 1

#                             table.setStyle(TableStyle(table_style))
#                             elements.append(table)

#                         # Add summary
#                         if 'summary' in full_report:
#                             elements.append(Spacer(1, 12))
#                             elements.append(Paragraph("Summary:", normal_style))
#                             summary = full_report.get('summary', '')

#                             # Process summary text and add resulting paragraphs
#                             summary_paragraphs = process_summary_text(summary)
#                             for paragraph in summary_paragraphs:
#                                 elements.append(paragraph)
#                                 elements.append(Spacer(1, 3))

#                 except Exception as e:
#                     logger.error(f"Error reading report file {report_path}: {e}")
#                     elements.append(Paragraph(f"Error reading detailed report: {str(e)}", normal_style))

#                 # Add creation date
#                 elements.append(Spacer(1, 6))
#                 created_at = report.get('created_at')
#                 if created_at:
#                     if isinstance(created_at, str):
#                         created_at = datetime.fromisoformat(created_at)
#                     elements.append(Paragraph(
#                         f"Report created: {created_at.strftime('%B %d, %Y at %I:%M %p')}",
#                         normal_style
#                     ))

#                 elements.append(Spacer(1, 20))

#             elements.append(Spacer(1, 30))

#         # Build the PDF
#         doc.build(elements)
#         logger.info(f"Generated comprehensive compliance PDF report: {pdf_path}")

#         # Return the PDF file and ensure cleanup
#         background_tasks = BackgroundTask(
#             lambda: os.unlink(pdf_path) if os.path.exists(pdf_path) else None
#         )
#         return FileResponse(
#             pdf_path,
#             filename=f"all_compliance_reports_{current_time.strftime('%Y%m%d_%H%M%S')}.pdf",
#             media_type="application/pdf",
#             background=background_tasks
#         )

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error generating comprehensive compliance PDF: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

# @router.get(f"/{{user_id}}/{{project_id}}/{config.get_all_compliance_reports_markdown_url}", response_model=insight_model.MarkdownResponse)
# async def get_all_compliance_reports_markdown(request: Request, user_id: str, project_id: str):

#     if config.VALIDATE_TOKEN:
#         token = request.headers.get('Authorization').split(' ')[1]
#         if not token:
#             logger.error("Authorization header is missing")
#             raise HTTPException(status_code=401, detail="Authorization header is missing")

#         payload = decode_jwt_token(token)
#         if payload:
#             user_email = payload['sub']
#             is_valid_user = mongo_manager.check_user_exists(user_email)
#             if not is_valid_user:
#                 logger.error("User not found")
#                 raise HTTPException(status_code=401, detail="Invalid user")
#             else:
#                 logger.info("Valid User")
#     else:
#         logger.info("Token validation is turned OFF. Skipping authentication checks.")

#     try:
#         logger.info(f"Generating comprehensive compliance markdown for project {project_id}")

#         # Convert _id to project_id
#         actual_project_id = utils.get_project_id_from_object_id(project_id)
#         if not actual_project_id:
#             logger.error(f"Project with _id {project_id} not found")
#             raise HTTPException(status_code=404, detail="Project not found")

#         # Verify project and user access
#         project_data, exists, _ = mongo_manager.get_or_initialize_project(actual_project_id)

#         if not exists:
#             logger.error(f"Project {actual_project_id} not found")
#             raise HTTPException(status_code=404, detail="Project not found")

#         if user_id != project_data.get("user_id", ""):
#             logger.error(f"User {user_id} not authorized for project {actual_project_id}")
#             raise HTTPException(status_code=403, detail="User not authorized to access this project")

#         # Get all compliance reports
#         reports = mongo_manager.get_compliance_reports(actual_project_id)

#         if not reports:
#             logger.error(f"No compliance reports found for project {actual_project_id}")
#             raise HTTPException(status_code=404, detail="No compliance reports found for this project")

#         # Create compliance reports directory if not exists
#         compliance_dir = os.path.join(config.file_path, actual_project_id, "compliance_reports")
#         os.makedirs(compliance_dir, exist_ok=True)

#         # Generate markdown content
#         current_time = datetime.utcnow()
#         markdown_content = f"# Comprehensive Compliance Report\n\n"
#         markdown_content += f"Generated on: {current_time.strftime('%B %d, %Y at %I:%M %p')}\n\n"

#         # Add a legend for the color-coding
#         markdown_content += "## Score Legend\n\n"
#         markdown_content += "| Score Range | Rating | Indicator |\n"
#         markdown_content += "|------------|--------|-----------|\n"
#         markdown_content += "| 8.0 - 10.0 | Good | 🟢 |\n"
#         markdown_content += "| 6.0 - 7.9 | Needs Improvement | 🟠 |\n"
#         markdown_content += "| 0.0 - 5.9 | Critical | 🔴 |\n\n"

#         # Group reports by framework type
#         framework_reports = {}
#         for report in reports:
#             framework = report.get('framework_type', 'UNKNOWN')
#             if framework not in framework_reports:
#                 framework_reports[framework] = []
#             framework_reports[framework].append(report)

#         # Process each framework type
#         for framework, framework_group in framework_reports.items():
#             markdown_content += f"## {framework} Compliance Reports\n\n"

#             # Process each report in the framework group
#             for report in framework_group:
#                 # Add file information
#                 markdown_content += f"### File: {report.get('file_name', '')}\n\n"

#                 # Get the average score and apply color-coding
#                 average_score = report.get('average_score', 0)

#                 # Determine which color indicator to use based on score range
#                 if average_score >= 8.0:
#                     score_indicator = "🟢" # Green circle
#                     score_label = "Good"
#                 elif average_score >= 6.0:
#                     score_indicator = "🟠" # Orange circle
#                     score_label = "Needs Improvement"
#                 else:
#                     score_indicator = "🔴" # Red circle
#                     score_label = "Critical"

#                 markdown_content += f"Overall Average Score: {average_score:.2f} {score_indicator} ({score_label})\n\n"

#                 # Get the full report content from the report file
#                 # Use the same case (uppercase) as when the file was created
#                 report_filename = f"{report['file_name'].split('.')[0]}_{framework}_compliance.json"
#                 report_path = os.path.join(compliance_dir, report_filename)

#                 try:
#                     with open(report_path, 'r') as f:
#                         full_report = json.load(f)

#                     # Check if the file is valid for compliance reporting
#                     # Note: This is for backward compatibility with reports generated before the change
#                     # New reports will always be valid since we now raise an error for invalid files
#                     is_valid = full_report.get('is_valid', True)

#                     if not is_valid:
#                         # If file is not valid for compliance reporting, show the reason
#                         markdown_content += "#### Not Valid for Compliance Reporting\n\n"
#                         reason = full_report.get('reason', 'No reason provided')
#                         markdown_content += f"{reason}\n\n"

#                         # Log a warning about this legacy invalid report
#                         logger.warning(f"Found legacy invalid compliance report for {report.get('file_name', '')}: {reason}")
#                     else:
#                         # Add individual factors as a table
#                         factors = full_report.get('individual_factors_score', {})
#                         if factors:
#                             markdown_content += "#### Individual Factor Scores:\n\n"

#                             # Create markdown table
#                             markdown_content += "| Factor | Score | Rating |\n"
#                             markdown_content += "|--------|-------|-------|\n"
#                             for factor, score in factors.items():
#                                 score_value = float(score)
#                                 # Determine which color indicator to use based on score range
#                                 if score_value >= 8.0:
#                                     score_indicator = "🟢" # Green circle
#                                     score_label = "Good"
#                                 elif score_value >= 6.0:
#                                     score_indicator = "🟠" # Orange circle
#                                     score_label = "Needs Improvement"
#                                 else:
#                                     score_indicator = "🔴" # Red circle
#                                     score_label = "Critical"
#                                 markdown_content += f"| {factor} | **{score_value:.2f}** | {score_indicator} {score_label} |\n"
#                             markdown_content += "\n"

#                         # Add summary
#                         if 'summary' in full_report:
#                             markdown_content += "#### Summary:\n\n"
#                             summary = full_report.get('summary', '')
#                             markdown_content += f"{summary}\n\n"

#                 except Exception as e:
#                     logger.error(f"Error reading report file {report_path}: {e}")
#                     markdown_content += f"Error reading detailed report: {str(e)}\n\n"

#                 # Add creation date
#                 created_at = report.get('created_at')
#                 if created_at:
#                     if isinstance(created_at, str):
#                         created_at = datetime.fromisoformat(created_at)
#                     markdown_content += f"Report created: {created_at.strftime('%B %d, %Y at %I:%M %p')}\n\n"

#                 markdown_content += "---\n\n"

#             markdown_content += "\n\n"

#         logger.info(f"Generated comprehensive compliance markdown report for project {actual_project_id}")

#         # Return the markdown content as JSON response
#         return {"markdown_content": markdown_content}

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error generating comprehensive compliance markdown: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))
