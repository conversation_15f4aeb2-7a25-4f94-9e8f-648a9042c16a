"""
Logging management routes for project+user logging system.

These routes provide APIs for managing, viewing, and analyzing project+user specific logs.
"""

import logging
from fastapi import APIRouter, Request, HTTPException, Query
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

from services.logging_utils import Lo<PERSON><PERSON><PERSON><PERSON>, log_analyzer, migration_helper
from services.project_user_logger import project_user_logger
from services.auth_utils import decode_jwt_token
from services.mongo_manager import MongoManager
import config

# Configure logging
def _get_context_or_fallback():
    """Get user/project context with fallback to None for admin operations."""
    try:
        from services.logging_middleware import get_current_user_id, get_current_project_id
        return get_current_user_id(), get_current_project_id()
    except:
        return None, None

router = APIRouter()

# Initialize services
mongo_manager = MongoManager(
    config.insights_collection, 
    config.images_collection, 
    config.tables_collection, 
    config.compliance_collection
)


@router.get("/logging/stats")
async def get_logging_stats(request: Request):
    """Get overall logging system statistics."""
    
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")
        
        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        user_email = payload['sub']
        is_valid_user = await mongo_manager.check_user_exists(user_email)
        if not is_valid_user:
            raise HTTPException(status_code=401, detail="Invalid user")
    
    try:
        # Get system-wide logging statistics
        stats = project_user_logger.get_log_stats()
        
        # Get user and project counts
        all_users = log_analyzer.get_all_users()
        total_projects = 0
        
        for user_id in all_users:
            user_projects = log_analyzer.get_user_projects(user_id)
            total_projects += len(user_projects)
        
        stats.update({
            "total_users_with_logs": len(all_users),
            "total_projects_with_logs": total_projects,
            "project_user_logging_enabled": config.PROJECT_USER_LOGGING_ENABLED
        })
        
        return stats
        
    except Exception as e:
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.error("Error getting logging stats", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail="Error retrieving logging statistics")


@router.get("/logging/users")
async def get_users_with_logs(request: Request):
    """Get list of all users who have logs."""
    
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")
        
        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        user_email = payload['sub']
        is_valid_user = await mongo_manager.check_user_exists(user_email)
        if not is_valid_user:
            raise HTTPException(status_code=401, detail="Invalid user")
    
    try:
        users = log_analyzer.get_all_users()
        
        # Get project counts for each user
        user_data = []
        for user_id in users:
            projects = log_analyzer.get_user_projects(user_id)
            user_data.append({
                "user_id": user_id,
                "project_count": len(projects),
                "projects": projects
            })
        
        return {
            "total_users": len(users),
            "users": user_data
        }
        
    except Exception as e:
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.error("Error getting users with logs", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail="Error retrieving users with logs")


@router.get("/logging/user/{user_id}/projects")
async def get_user_projects(request: Request, user_id: str):
    """Get list of projects for a specific user."""
    
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")
        
        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        user_email = payload['sub']
        is_valid_user = await mongo_manager.check_user_exists(user_email)
        if not is_valid_user:
            raise HTTPException(status_code=401, detail="Invalid user")
    
    try:
        projects = log_analyzer.get_user_projects(user_id)
        
        return {
            "user_id": user_id,
            "project_count": len(projects),
            "projects": projects
        }
        
    except Exception as e:
        user_id_ctx, project_id_ctx = _get_context_or_fallback()
        LoggingHelper.error("Error getting projects for user", user_id=user_id_ctx, project_id=project_id_ctx,
                           extra_data={"target_user_id": user_id, "error": str(e)})
        raise HTTPException(status_code=500, detail=f"Error retrieving projects for user {user_id}")


@router.get("/logging/user/{user_id}/project/{project_id}/logs")
async def get_project_logs(
    request: Request, 
    user_id: str, 
    project_id: str,
    lines: int = Query(100, description="Number of recent log lines to retrieve"),
    search: Optional[str] = Query(None, description="Search term to filter logs")
):
    """Get recent logs for a specific user+project combination."""
    
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")
        
        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        user_email = payload['sub']
        is_valid_user = await mongo_manager.check_user_exists(user_email)
        if not is_valid_user:
            raise HTTPException(status_code=401, detail="Invalid user")
    
    try:
        if search:
            # Search for specific terms
            results = await log_analyzer.search_logs(user_id, project_id, search, lines)
            return {
                "user_id": user_id,
                "project_id": project_id,
                "search_term": search,
                "results_count": len(results),
                "results": results
            }
        else:
            # Get recent logs
            logs = await log_analyzer.read_recent_logs(user_id, project_id, lines)
            return {
                "user_id": user_id,
                "project_id": project_id,
                "lines_requested": lines,
                "lines_returned": len(logs),
                "logs": logs
            }
        
    except Exception as e:
        user_id_ctx, project_id_ctx = _get_context_or_fallback()
        LoggingHelper.error("Error getting logs for user project", user_id=user_id_ctx, project_id=project_id_ctx,
                           extra_data={"target_user_id": user_id, "target_project_id": project_id, "error": str(e)})
        raise HTTPException(status_code=500, detail=f"Error retrieving logs for user {user_id}, project {project_id}")


@router.get("/logging/user/{user_id}/project/{project_id}/stats")
async def get_project_log_stats(request: Request, user_id: str, project_id: str):
    """Get statistics for a specific user+project log."""
    
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")
        
        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        user_email = payload['sub']
        is_valid_user = await mongo_manager.check_user_exists(user_email)
        if not is_valid_user:
            raise HTTPException(status_code=401, detail="Invalid user")
    
    try:
        stats = await log_analyzer.get_log_statistics(user_id, project_id)
        
        return {
            "user_id": user_id,
            "project_id": project_id,
            "statistics": stats
        }
        
    except Exception as e:
        user_id_ctx, project_id_ctx = _get_context_or_fallback()
        LoggingHelper.error("Error getting log stats for user project", user_id=user_id_ctx, project_id=project_id_ctx,
                           extra_data={"target_user_id": user_id, "target_project_id": project_id, "error": str(e)})
        raise HTTPException(status_code=500, detail=f"Error retrieving log statistics for user {user_id}, project {project_id}")


@router.get("/logging/migration-guide")
async def get_migration_guide():
    """Get migration guide for developers."""
    
    try:
        guide = migration_helper.create_migration_guide()
        
        return {
            "migration_guide": guide,
            "current_config": {
                "project_user_logging_enabled": config.PROJECT_USER_LOGGING_ENABLED,
                "base_directory": config.PROJECT_USER_LOG_BASE_DIR,
                "max_file_size": config.PROJECT_USER_LOG_MAX_SIZE,
                "backup_count": config.PROJECT_USER_LOG_BACKUP_COUNT,
                "retention_days": config.PROJECT_USER_LOG_RETENTION_DAYS
            }
        }
        
    except Exception as e:
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.error("Error getting migration guide", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail="Error retrieving migration guide")


@router.post("/logging/test")
async def test_project_logging(
    request: Request,
    user_id: str = Query(..., description="User ID for testing"),
    project_id: str = Query(..., description="Project ID for testing"),
    message: str = Query("Test log message", description="Test message to log")
):
    """Test endpoint for project+user logging system."""
    
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")
        
        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        user_email = payload['sub']
        is_valid_user = await mongo_manager.check_user_exists(user_email)
        if not is_valid_user:
            raise HTTPException(status_code=401, detail="Invalid user")
    
    try:
        # Test different logging levels
        LoggingHelper.info(f"INFO: {message}", user_id, project_id, 
                          extra_data={"test_type": "info", "timestamp": datetime.now(timezone.utc).isoformat()})
        
        LoggingHelper.warning(f"WARNING: {message}", user_id, project_id,
                             extra_data={"test_type": "warning", "timestamp": datetime.now(timezone.utc).isoformat()})
        
        LoggingHelper.error(f"ERROR: {message}", user_id, project_id,
                           extra_data={"test_type": "error", "timestamp": datetime.now(timezone.utc).isoformat()})
        
        return {
            "status": "success",
            "message": "Test logs created successfully",
            "user_id": user_id,
            "project_id": project_id,
            "test_message": message,
            "logs_created": 3
        }
        
    except Exception as e:
        user_id_ctx, project_id_ctx = _get_context_or_fallback()
        LoggingHelper.error("Error testing project logging", user_id=user_id_ctx, project_id=project_id_ctx,
                           extra_data={"test_user_id": user_id, "test_project_id": project_id, "error": str(e)})
        raise HTTPException(status_code=500, detail="Error testing project logging system")
