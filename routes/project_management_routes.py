import logging
from fastapi import APIRouter, Request, HTTPException
from services.mongo_manager import MongoManager
import config
from models import insight_model
from services.utils import decode_jwt_token
from services.logging_utils import LoggingHelper

# Configure logging - will use project+user specific logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
mongo_manager = MongoManager(config.insights_collection, config.images_collection, config.tables_collection, config.compliance_collection)

# Project management routes will be moved here
@router.put(f"/{config.updateURL}/")
async def update_title(request: Request, title_update_request: insight_model.TitleUpdateRequest):
    # Extract user_id from token for logging context
    user_id = None
    project_id = title_update_request.project_id

    LoggingHelper.info("Update title called", project_id=project_id,
                      extra_data={"new_title": title_update_request.new_title})

    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        LoggingHelper.info("About to decode token", project_id=project_id)
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            user_id = user_email  # Use email as user_id
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id)
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id)
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.", project_id=project_id)

    try:
        await mongo_manager.update_project(title_update_request.project_id, title_update_request.new_title)
        LoggingHelper.info("Title updated successfully", user_id=user_id, project_id=project_id,
                          extra_data={"new_title": title_update_request.new_title})
    except Exception as e:
        LoggingHelper.error(f"Error updating title: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "new_title": title_update_request.new_title})
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "Title updated successfully."}

@router.post(f"/{config.set_model_url}/")
async def set_model(request: Request, set_model_request: insight_model.SetModelRequest):
    # Extract user_id from token for logging context
    user_id = None
    project_id = set_model_request.project_id

    LoggingHelper.info("Set model called", project_id=project_id,
                      extra_data={"model_name": set_model_request.model_name})

    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            user_id = user_email  # Use email as user_id
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id)
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id)
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.", project_id=project_id)

    try:
        await mongo_manager.set_model_for_project(set_model_request.project_id, set_model_request.model_name)
        LoggingHelper.info("Model set successfully", user_id=user_id, project_id=project_id,
                          extra_data={"model_name": set_model_request.model_name})
    except Exception as e:
        LoggingHelper.error(f"Error setting model: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "model_name": set_model_request.model_name})
        raise HTTPException(status_code=500, detail=str(e))

    return {"message": f"Model set to {set_model_request.model_name} successfully."}

@router.get(f"/{config.get_model_url}/{{project_id}}")
async def get_model(request: Request, project_id: str):
    # Extract user_id from token for logging context
    user_id = None

    LoggingHelper.info("Get model called", project_id=project_id)

    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            user_id = user_email  # Use email as user_id
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id)
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id)
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.", project_id=project_id)

    try:
        model_name = await mongo_manager.get_model_for_project(project_id)
        LoggingHelper.info("Model retrieved successfully", user_id=user_id, project_id=project_id,
                          extra_data={"model_name": model_name})
        return model_name
    except Exception as e:
        LoggingHelper.error(f"Error getting model: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=str(e))
