import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import FileResponse
import os
import urllib
import config
from services.mongo_manager import MongoManager
from services.utils import decode_jwt_token
from services.logging_utils import LoggingHelper

# Configure logging - will use project+user specific logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
mongo_manager = MongoManager(config.insights_collection, config.images_collection, config.tables_collection, config.compliance_collection)

# File download routes will be moved here
@router.get(f"/chat/{config.download_file_url}/{{file_path:path}}")
async def download_file(request: Request, file_path: str):
    # Extract user_id from token and project_id from file path for logging context
    user_id = None
    project_id = None

    # Decode the URL-encoded file path first to extract project_id
    decoded_file_path = urllib.parse.unquote(file_path)
    decoded_file_path = decoded_file_path.lstrip('/')

    # Split the path into project_id and file_name
    path_parts = decoded_file_path.split('/', 1)
    if len(path_parts) == 2:
        project_id, file_name = path_parts

    LoggingHelper.info("File download requested", user_id=user_id, project_id=project_id,
                      extra_data={"file_path": file_path, "decoded_file_path": decoded_file_path})

    if config.VALIDATE_TOKEN:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", user_id=user_id, project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = auth_header.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            user_id = user_email  # Update user_id for logging
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id,
                                   extra_data={"user_email": user_email})
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id,
                                  extra_data={"user_email": user_email})
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.",
                          user_id=user_id, project_id=project_id)

    # Validate file path structure
    if len(path_parts) != 2:
        LoggingHelper.error("Invalid file path structure", user_id=user_id, project_id=project_id,
                           extra_data={"file_path": file_path, "decoded_file_path": decoded_file_path,
                                     "path_parts_count": len(path_parts)})
        raise HTTPException(status_code=400, detail="Invalid file path")

    # Construct the full file path
    full_file_path = os.path.join(os.getcwd(), project_id, file_name)
    LoggingHelper.info("Constructed full file path", user_id=user_id, project_id=project_id,
                      extra_data={"full_file_path": full_file_path, "file_name": file_name})

    if os.path.exists(full_file_path):
        LoggingHelper.info("File found, serving download", user_id=user_id, project_id=project_id,
                          extra_data={"full_file_path": full_file_path, "file_name": file_name})
        return FileResponse(full_file_path, filename=file_name)
    else:
        LoggingHelper.error("File not found", user_id=user_id, project_id=project_id,
                           extra_data={"full_file_path": full_file_path, "file_name": file_name,
                                     "decoded_file_path": decoded_file_path})
        raise HTTPException(status_code=404, detail=f"File not found: {decoded_file_path}")
