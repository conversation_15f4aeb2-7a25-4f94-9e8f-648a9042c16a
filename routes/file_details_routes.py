import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, HTTPException
import os
import config
from services.mongo_manager import MongoManager
from services import utils
from services.utils import decode_jwt_token
from services.logging_utils import Logging<PERSON>elper

# Configure logging - will use project+user specific logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
mongo_manager = MongoManager(config.insights_collection, config.images_collection, config.tables_collection, config.compliance_collection)

# File details routes will be moved here
@router.get(f"/{config.get_file_details_url}/{{user_id}}/{{project_id}}")
async def get_project_files(request: Request, user_id: str, project_id: str):
    # user_id and project_id are available from path parameters
    LoggingHelper.info("Get project files called", user_id=user_id, project_id=project_id)

    if config.VALIDATE_TOKEN:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", user_id=user_id, project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = auth_header.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id,
                                   extra_data={"user_email": user_email})
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id,
                                  extra_data={"user_email": user_email})
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.",
                          user_id=user_id, project_id=project_id)

    try:
        # Convert _id to project_id
        actual_project_id = await utils.get_project_id_from_object_id(project_id, mongo_manager)
        if not actual_project_id:
            LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,
                               extra_data={"object_id": project_id})
            raise HTTPException(status_code=404, detail="Project not found")

        LoggingHelper.info("Converting object_id to project_id", user_id=user_id, project_id=project_id,
                          extra_data={"object_id": project_id, "actual_project_id": actual_project_id})

        file_details = await mongo_manager.get_file_details(actual_project_id, user_id)

        # Extract only the required fields
        simplified_details = [
            {
                "file_id": detail["file_id"],
                "file_name": detail["file_name"]
            }
            for detail in file_details
        ]

        LoggingHelper.info("File details retrieved successfully", user_id=user_id, project_id=project_id,
                          extra_data={"file_count": len(simplified_details), "actual_project_id": actual_project_id})

        return simplified_details

    except HTTPException:
        # Re-raise HTTP exceptions without additional logging
        raise
    except Exception as e:
        LoggingHelper.error(f"Error getting file details: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "object_id": project_id})
        raise HTTPException(status_code=500, detail=str(e))
