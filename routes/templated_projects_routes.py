import logging
from fastapi import APIRout<PERSON>, Request, HTTPException
import config
from models import insight_model
from services.utils import decode_jwt_token
from utils.templated_projects_utils import TemplatedProjectsUtils
from services.logging_utils import LoggingHelper

# Configure logging - will use project+user specific logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize templated projects utils
templated_projects_utils = TemplatedProjectsUtils(
    config.templated_projects_collection,
    config.insights_collection
)

@router.post(f"/{config.create_templated_project_url}/")
async def create_templated_project(
    request: Request,
    create_request: insight_model.CreateTemplatedProjectRequest
):
    """
    Create a templated project copy from an existing project in insights collection.

    This endpoint:
    1. Takes a project_id from the insights collection
    2. Creates a copy in the templated_projects collection
    3. Excludes user-specific data like user_id (keeps it empty) and timestamps
    4. Removes file-specific timestamps and user data from file_details
    """
    # Extract user_id from token for logging context
    user_id = None
    project_id = create_request.project_id

    LoggingHelper.info("Create templated project called", project_id=project_id,
                      extra_data={"source_project_id": create_request.project_id})

    # Authentication check
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        LoggingHelper.info("About to decode token", project_id=project_id)
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            user_id = user_email  # Use email as user_id
            # Note: We're not checking user ownership of the project for templated projects
            # as these are meant to be shared templates
            LoggingHelper.info("Valid user authenticated", user_id=user_id, project_id=project_id,
                              extra_data={"user_email": user_email})
        else:
            LoggingHelper.error("Invalid token", project_id=project_id)
            raise HTTPException(status_code=401, detail="Invalid token")
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.", project_id=project_id)

    try:
        # Create templated project
        result = await templated_projects_utils.create_templated_project_from_insights(
            create_request.project_id
        )

        if result["success"]:
            LoggingHelper.info(f"Successfully {result['action']} templated project",
                              user_id=user_id, project_id=project_id,
                              extra_data={"result_project_id": result['project_id'],
                                        "action": result['action'], "original_project_id": result["original_project_id"]})
            return {
                "message": result["message"],
                "project_id": result["project_id"],
                "original_project_id": result["original_project_id"],
                "action": result["action"]
            }
        else:
            LoggingHelper.error(f"Failed to create templated project: {result['error']}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"error": result.get('error', 'Unknown error')})
            raise HTTPException(status_code=400, detail=result["message"])

    except ValueError as e:
        LoggingHelper.error(f"Validation error: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        LoggingHelper.error(f"Unexpected error creating templated project: {str(e)}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get(f"/get-templated-project/{{project_id}}")
async def get_templated_project(
    request: Request,
    project_id: str
):
    """
    Get a specific templated project by its ID.
    """
    # Extract user_id from token for logging context
    user_id = None

    LoggingHelper.info("Get templated project called", project_id=project_id)

    # Authentication check
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            LoggingHelper.error("Invalid token", project_id=project_id)
            raise HTTPException(status_code=401, detail="Invalid token")
        else:
            user_id = payload.get('sub')  # Extract user_id for logging
            LoggingHelper.info("Valid user authenticated", user_id=user_id, project_id=project_id)
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.", project_id=project_id)

    try:
        templated_project = await templated_projects_utils.get_templated_project(project_id)

        if not templated_project:
            LoggingHelper.error("Templated project not found", user_id=user_id, project_id=project_id)
            raise HTTPException(status_code=404, detail="Templated project not found")

        # Remove MongoDB _id from response
        if "_id" in templated_project:
            del templated_project["_id"]

        LoggingHelper.info("Templated project retrieved successfully", user_id=user_id, project_id=project_id,
                          extra_data={"project_title": templated_project.get("title", "Unknown")})
        return templated_project

    except HTTPException:
        raise
    except Exception as e:
        LoggingHelper.error(f"Error retrieving templated project: {str(e)}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get(f"/list-templated-projects/")
async def list_templated_projects(
    request: Request,
    limit: int = 50,
    skip: int = 0
):
    """
    List all templated projects with pagination.
    """
    # Extract user_id from token for logging context
    user_id = None

    LoggingHelper.info("List templated projects called",
                      extra_data={"limit": limit, "skip": skip})

    # Authentication check
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid",
                               extra_data={"limit": limit, "skip": skip})
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            LoggingHelper.error("Invalid token", extra_data={"limit": limit, "skip": skip})
            raise HTTPException(status_code=401, detail="Invalid token")
        else:
            user_id = payload.get('sub')  # Extract user_id for logging
            LoggingHelper.info("Valid user authenticated", user_id=user_id,
                              extra_data={"limit": limit, "skip": skip})
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.",
                          extra_data={"limit": limit, "skip": skip})

    try:
        # Validate pagination parameters
        if limit < 1 or limit > 100:
            LoggingHelper.error("Invalid limit parameter", user_id=user_id,
                               extra_data={"limit": limit, "valid_range": "1-100"})
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 100")
        if skip < 0:
            LoggingHelper.error("Invalid skip parameter", user_id=user_id,
                               extra_data={"skip": skip, "minimum": 0})
            raise HTTPException(status_code=400, detail="Skip must be non-negative")

        templated_projects = await templated_projects_utils.list_templated_projects(limit, skip)

        LoggingHelper.info("Templated projects listed successfully", user_id=user_id,
                          extra_data={"count": len(templated_projects), "limit": limit, "skip": skip})

        return {
            "templated_projects": templated_projects,
            "count": len(templated_projects),
            "limit": limit,
            "skip": skip
        }

    except HTTPException:
        raise
    except Exception as e:
        LoggingHelper.error(f"Error listing templated projects: {str(e)}", user_id=user_id,
                           extra_data={"error": str(e), "limit": limit, "skip": skip})
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete(f"/delete-templated-project/{{project_id}}")
async def delete_templated_project(
    request: Request,
    project_id: str
):
    """
    Soft delete a templated project by marking it as deleted.
    """
    # Extract user_id from token for logging context
    user_id = None

    LoggingHelper.info("Delete templated project called", project_id=project_id)

    # Authentication check
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            LoggingHelper.error("Invalid token", project_id=project_id)
            raise HTTPException(status_code=401, detail="Invalid token")
        else:
            user_id = payload.get('sub')  # Extract user_id for logging
            LoggingHelper.info("Valid user authenticated", user_id=user_id, project_id=project_id)
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.", project_id=project_id)

    try:
        # Check if templated project exists
        templated_project = await templated_projects_utils.get_templated_project(project_id)

        if not templated_project:
            LoggingHelper.error("Templated project not found for deletion", user_id=user_id, project_id=project_id)
            raise HTTPException(status_code=404, detail="Templated project not found")

        # Soft delete by marking as deleted
        await config.templated_projects_collection.update_one(
            {"project_id": project_id},
            {"$set": {"isDeleted": True}}
        )

        LoggingHelper.info("Successfully deleted templated project", user_id=user_id, project_id=project_id,
                          extra_data={"project_title": templated_project.get("title", "Unknown")})
        return {"message": "Templated project deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        LoggingHelper.error(f"Error deleting templated project: {str(e)}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post(f"/{config.import_templated_projects_url}/")
async def import_templated_projects(
    request: Request,
    import_request: insight_model.ImportTemplatedProjectsRequest
):
    """
    Import all templated projects to insights collection for a specific user.

    This endpoint:
    1. Gets all templated projects from templated_projects collection
    2. Creates copies in the insights collection with the specified user_id
    3. Assigns new project_ids and timestamps for each imported project
    4. Preserves all content while making them regular user projects
    """
    # Extract user_id from request and token for logging context
    target_user_id = import_request.user_id
    auth_user_id = None

    LoggingHelper.info("Import templated projects called", user_id=target_user_id,
                      extra_data={"target_user_id": target_user_id})

    # Authentication check
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", user_id=target_user_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        LoggingHelper.info("About to decode token", user_id=target_user_id)
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            auth_user_id = user_email
            LoggingHelper.info("Valid user authenticated", user_id=target_user_id,
                              extra_data={"auth_user_email": user_email})
        else:
            LoggingHelper.error("Invalid token", user_id=target_user_id)
            raise HTTPException(status_code=401, detail="Invalid token")
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.", user_id=target_user_id)

    try:
        # Import all templated projects for the user
        result = await templated_projects_utils.import_all_templated_projects_to_insights(
            import_request.user_id
        )

        if result["success"]:
            LoggingHelper.info(f"Successfully imported {result['imported_count']} templated projects",
                              user_id=target_user_id,
                              extra_data={"imported_count": result['imported_count'],
                                        "skipped_count": result['skipped_count'],
                                        "total_templated_projects": result['total_templated_projects']})
            return {
                "message": result["message"],
                "imported_count": result["imported_count"],
                "skipped_count": result["skipped_count"],
                "total_templated_projects": result["total_templated_projects"],
                "imported_projects": result["imported_projects"],
                "user_id": import_request.user_id
            }
        else:
            LoggingHelper.error(f"Failed to import templated projects: {result['error']}",
                               user_id=target_user_id,
                               extra_data={"error": result.get('error', 'Unknown error')})
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        LoggingHelper.error(f"Unexpected error importing templated projects: {str(e)}",
                           user_id=target_user_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get(f"/debug-templated-projects/")
async def debug_templated_projects(request: Request):
    """
    Debug endpoint to inspect templated projects collection.
    Shows the structure of all templated projects to help identify issues.
    """
    # Extract user_id from token for logging context
    user_id = None

    LoggingHelper.info("Debug templated projects called")

    # Authentication check
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid")
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if not payload:
            LoggingHelper.error("Invalid token")
            raise HTTPException(status_code=401, detail="Invalid token")
        else:
            user_id = payload.get('sub')  # Extract user_id for logging
            LoggingHelper.info("Valid user authenticated for debug", user_id=user_id)
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.")

    try:
        # Get all templated projects with their structure
        cursor = config.templated_projects_collection.find({})
        all_projects = await cursor.to_list(length=None)

        debug_info = []
        for project in all_projects:
            project_debug = {
                "_id": str(project.get("_id", "missing")),
                "project_id": project.get("project_id", "MISSING"),
                "title": project.get("title", "missing"),
                "is_templated_project": project.get("is_templated_project", "missing"),
                "isDeleted": project.get("isDeleted", "missing"),
                "user_id": project.get("user_id", "missing"),
                "has_file_details": len(project.get("file_details", [])),
                "all_fields": list(project.keys())
            }
            debug_info.append(project_debug)

        LoggingHelper.info("Debug templated projects completed", user_id=user_id,
                          extra_data={"total_projects": len(all_projects),
                                    "projects_with_files": sum(1 for p in all_projects if p.get("file_details"))})

        return {
            "total_projects": len(all_projects),
            "projects": debug_info
        }

    except Exception as e:
        LoggingHelper.error(f"Error debugging templated projects: {str(e)}", user_id=user_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
