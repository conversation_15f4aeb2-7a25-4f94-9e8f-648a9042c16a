import logging
from fastapi import APIRout<PERSON>, Request, HTTPException, Query
from typing import Optional, List, Dict, Any
from services.mongo_manager import MongoManager
import config
from services.utils import decode_jwt_token
from services.logging_utils import LoggingHelper

# Configure logging - will use project+user specific logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
mongo_manager = MongoManager(
    config.insights_collection, 
    config.images_collection, 
    config.tables_collection, 
    config.compliance_collection
)

async def authenticate_user(request: Request, project_id: str) -> Optional[str]:
    """
    Helper function to authenticate user and return user_id
    """
    user_id = None
    
    if config.VALIDATE_TOKEN:
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = token.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            user_id = user_email  # Use email as user_id
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id)
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id)
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.", project_id=project_id)
    
    return user_id

@router.get(f"/{config.usage_data_by_project_url}/{{project_id}}")
async def get_usage_data_by_project(
    request: Request,
    project_id: str,
    service_category: Optional[str] = Query(None, description="Filter by service category (llm, search, etc.)"),
    query_id: Optional[str] = Query(None, description="Filter by specific query ID")
):
    """
    Get token and websearch usage data for a specific project.
    
    Args:
        project_id (str): The project ID to get usage data for
        service_category (str, optional): Filter by service category (llm, search, etc.)
        query_id (str, optional): Filter by specific query ID
    
    Returns:
        List of usage events for the project
    """
    LoggingHelper.info("Get usage data by project called", project_id=project_id,
                      extra_data={"service_category": service_category, "query_id": query_id})
    
    # Authenticate user
    user_id = await authenticate_user(request, project_id)
    
    try:
        # Get usage events from MongoDB
        usage_events = await mongo_manager.get_usage_events(
            project_id=project_id,
            query_id=query_id,
            service_category=service_category
        )
        
        LoggingHelper.info("Usage data retrieved successfully", user_id=user_id, project_id=project_id,
                          extra_data={"events_count": len(usage_events), "service_category": service_category})
        
        return {
            "project_id": project_id,
            "total_events": len(usage_events),
            "usage_events": usage_events
        }
        
    except Exception as e:
        LoggingHelper.error(f"Error retrieving usage data by project: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "service_category": service_category})
        raise HTTPException(status_code=500, detail=f"Error retrieving usage data: {str(e)}")

@router.get(f"/{config.usage_data_by_response_url}/{{response_id}}")
async def get_usage_data_by_response(
    request: Request,
    response_id: str,
    project_id: str = Query(..., description="Project ID for authentication and logging")
):
    """
    Get token and websearch usage data for a specific response ID.
    
    Args:
        response_id (str): The response ID to get usage data for
        project_id (str): The project ID for authentication and logging
    
    Returns:
        Usage event data linked to the specific response
    """
    LoggingHelper.info("Get usage data by response called", project_id=project_id,
                      extra_data={"response_id": response_id})
    
    # Authenticate user
    user_id = await authenticate_user(request, project_id)
    
    try:
        # Get usage events from MongoDB by response_id
        usage_event = await mongo_manager.get_usage_events_by_response_id(response_id)
        
        if not usage_event:
            LoggingHelper.warning("No usage data found for response_id", user_id=user_id, project_id=project_id,
                                 extra_data={"response_id": response_id})
            raise HTTPException(status_code=404, detail="No usage data found for the specified response ID")
        
        # Verify the response belongs to the specified project
        if usage_event.get("project_id") != project_id:
            LoggingHelper.error("Response ID does not belong to specified project", user_id=user_id, project_id=project_id,
                               extra_data={"response_id": response_id, "actual_project_id": usage_event.get("project_id")})
            raise HTTPException(status_code=403, detail="Response ID does not belong to the specified project")
        
        LoggingHelper.info("Usage data retrieved successfully by response", user_id=user_id, project_id=project_id,
                          extra_data={"response_id": response_id, "events_count": len(usage_event.get("events", []))})
        
        return usage_event
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        LoggingHelper.error(f"Error retrieving usage data by response: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "response_id": response_id})
        raise HTTPException(status_code=500, detail=f"Error retrieving usage data: {str(e)}")

@router.get(f"/{config.usage_data_by_project_url}/{{project_id}}/{config.usage_data_summary_suffix}")
async def get_usage_summary_by_project(
    request: Request,
    project_id: str
):
    """
    Get aggregated usage statistics for a project.
    
    Args:
        project_id (str): The project ID to get usage summary for
    
    Returns:
        Aggregated usage statistics including total tokens, operations, etc.
    """
    LoggingHelper.info("Get usage summary by project called", project_id=project_id)
    
    # Authenticate user
    user_id = await authenticate_user(request, project_id)
    
    try:
        # Get aggregated usage statistics
        usage_summary = await mongo_manager.get_total_usage_for_project(project_id)
        
        LoggingHelper.info("Usage summary retrieved successfully", user_id=user_id, project_id=project_id,
                          extra_data={"total_tokens": usage_summary.get("total_tokens", 0),
                                     "total_operations": usage_summary.get("total_operations", 0)})
        
        return {
            "project_id": project_id,
            "summary": usage_summary
        }
        
    except Exception as e:
        LoggingHelper.error(f"Error retrieving usage summary: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"Error retrieving usage summary: {str(e)}")

@router.get(f"/{config.usage_data_by_project_url}/{{project_id}}/{config.usage_data_categories_suffix}")
async def get_usage_by_service_categories(
    request: Request,
    project_id: str
):
    """
    Get usage data broken down by service categories (llm, search, etc.) for a project.
    
    Args:
        project_id (str): The project ID to get categorized usage data for
    
    Returns:
        Usage data organized by service categories
    """
    LoggingHelper.info("Get usage by service categories called", project_id=project_id)
    
    # Authenticate user
    user_id = await authenticate_user(request, project_id)
    
    try:
        # Get all usage events for the project
        all_usage_events = await mongo_manager.get_usage_events(project_id=project_id)
        
        # Organize data by service categories
        category_breakdown = {}
        
        for usage_event in all_usage_events:
            # Get category totals if available in the document
            category_totals = usage_event.get("category_totals", {})
            
            for category, totals in category_totals.items():
                if category not in category_breakdown:
                    category_breakdown[category] = {
                        "operations": 0,
                        "input_tokens": 0,
                        "output_tokens": 0,
                        "total_tokens": 0
                    }
                
                category_breakdown[category]["operations"] += totals.get("operations", 0)
                category_breakdown[category]["input_tokens"] += totals.get("input_tokens", 0)
                category_breakdown[category]["output_tokens"] += totals.get("output_tokens", 0)
                category_breakdown[category]["total_tokens"] += totals.get("total_tokens", 0)
        
        LoggingHelper.info("Usage by categories retrieved successfully", user_id=user_id, project_id=project_id,
                          extra_data={"categories": list(category_breakdown.keys()),
                                     "total_events": len(all_usage_events)})
        
        return {
            "project_id": project_id,
            "category_breakdown": category_breakdown,
            "total_usage_events": len(all_usage_events)
        }
        
    except Exception as e:
        LoggingHelper.error(f"Error retrieving usage by categories: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        raise HTTPException(status_code=500, detail=f"Error retrieving usage by categories: {str(e)}") 