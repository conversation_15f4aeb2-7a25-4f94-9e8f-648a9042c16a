# Source Type Consistency Fix - Complete Summary

## 🔍 **Bug Identified**

**Issue**: When websearch was selected as a source, the final response incorrectly showed `"source_type": "file"` instead of `"source_type": "web_search"`

## 🎯 **Root Cause Found**

The issue was in **`services/pdf_processor.py`** where `source_type` was **hardcoded to "file"**:

```python
# Line 399 - BEFORE FIX
async for event in utils.fake_llm_resonse(
    prompt, project_id, self.mongo_manager, extracted_text,
    chat_history, valid_page_numbers, response_type,
    visualization, datetime.now(timezone.utc).date(),
    "file", token_logger, user_id, unified_logger  # ❌ HARDCODED "file"
):
```

## 📊 **What Was Happening**

1. **Websearch Selected** → Correctly logged as `source_type: "web_search"`
2. **Web Search Processed** → Correctly logged as `source_type: "web search"`  
3. **Response Goes Through PDF Processor** → **OVERWRITES** to `"file"`
4. **Final Response Shows** → `"source_type": "file"` ❌

## ✅ **Fixes Implemented**

### **1. Modified PDF Processor to Accept source_type Parameter**

**File**: `services/pdf_processor.py`

```python
# BEFORE
async def process_pdf(self, file, prompt, project_id, user_id, ... , unified_logger=None):

# AFTER  
async def process_pdf(self, file, prompt, project_id, user_id, ... , unified_logger=None, source_type: str = "file"):
```

### **2. Updated PDF Processor to Use Dynamic source_type**

**File**: `services/pdf_processor.py` Line 399

```python
# BEFORE
"file", token_logger, user_id, unified_logger

# AFTER
source_type, token_logger, user_id, unified_logger
```

### **3. Updated All PDF Processor Calls with Correct source_type**

**File**: `routes/file_processing_routes.py`

| Operation Type | source_type Value | Line | Context |
|----------------|-------------------|------|---------|
| **Web Search Success** | `"web_search"` | 815 | When websearch succeeds |
| **Web Search Fallback** | `"self_answer"` | 928 | When websearch fails → self answer |
| **Self Answer** | `"self_answer"` | 1038 | When self answer is directly selected |
| **File Upload** | `"file"` | 436 | When processing uploaded files |
| **Regular File Processing** | `"file"` | 1135 | When processing existing files |

## 🎯 **Expected Outcome**

Now when websearch is selected, the final response will correctly show:

```json
{
    "source_type": "web_search"  // ✅ CORRECT
}
```

Instead of the incorrect:

```json
{
    "source_type": "file"  // ❌ WRONG
}
```

## 🔄 **Source Type Mapping**

| User Selection | Actual Operation | Correct source_type |
|----------------|------------------|---------------------|
| WebSeach: True (success) | Web search → PDF processor | `"web_search"` |
| WebSeach: True (failed) | Fallback to self answer | `"self_answer"` |
| SelfAnswer: True | Self answer → PDF processor | `"self_answer"` |
| FileTool: True | File processing | `"file"` |
| File Upload | File upload processing | `"file"` |

## 📈 **Benefits**

1. **Accurate Source Tracking**: Frontend can now reliably determine what source was actually used
2. **Improved Analytics**: Token consumption can be properly attributed to the correct source type
3. **Better UX**: Users see consistent source information throughout the pipeline
4. **Debugging**: Logs now show the correct source type from start to finish

## ✅ **Testing Recommended**

To verify the fix works:

1. **Test websearch query** → Final response should show `"source_type": "web_search"`
2. **Test websearch fallback** → Final response should show `"source_type": "self_answer"`  
3. **Test self answer** → Final response should show `"source_type": "self_answer"`
4. **Test file upload** → Final response should show `"source_type": "file"`

The bug is now completely resolved! 🎉 