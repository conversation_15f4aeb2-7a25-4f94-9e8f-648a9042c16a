"""
File processing and path configurations.
"""

import os
import pytesseract

# File processing settings
page_limit = 180
ocr = True
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
vision = True
plain_txt_without_ocr = False
paid_search = True

# File paths
temp_path = "temp_files"
path = os.getcwd()
file_path = 'files'
non_data_analysis_folder_name = 'non_data_analysis'
data_analysis_folder_name = 'data_analysis'
data_mount_path = "/mnt/data/"

# File types
SUPPORTED_FILE_TYPES = ["pdf", "doc", "docx", "txt", "csv", "xlsx"]
DATA_ANALYSIS_FILE_TYPES = ["csv", "xlsx"]
DOCUMENT_FILE_TYPES = ["pdf", "doc", "docx", "txt"]

# Text extraction prompt
text_extraction_prompt = "Extract the text from the image in markdown format. Maintain the structure and hierarchy as it appears in the image. Ignore non-text elements. Make sure to structure the text in a readable format and not to miss any textual information from the image."
