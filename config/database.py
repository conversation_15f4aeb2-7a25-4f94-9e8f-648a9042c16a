"""
Database configurations for MongoDB connections and collections.
"""

from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure

# Database configuration
mongo = 'dev'  # 'local' or 'dev'

if mongo == 'local':
    mongo_client = AsyncIOMotorClient("mongodb://localhost:27017/")
    dbname = 'testdb'
else:
    # Add SSL parameters to fix certificate verification issues on macOS
    mongo_client = AsyncIOMotorClient(
        'mongodb+srv://Admin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0/NEUQUIP&tls=true&tlsAllowInvalidCertificates=true',
        tls=True,
        tlsAllowInvalidCertificates=True,
        tlsAllowInvalidHostnames=True,
        serverSelectionTimeoutMS=5000,
        connectTimeoutMS=10000,
        socketTimeoutMS=20000
    )
    dbname = 'DEV-NEUQUIP'
    # dbname = 'NEUQUIP'

try:
    # Get database and collections
    db = mongo_client[dbname]
    insights_collection = db["insights"]
    images_collection = db["images"]
    tables_collection = db["tables"]
    compliance_collection = db["compliances"]
    users_collection = db["users"]
    templated_projects_collection = db["templated_projects"]
    print("MongoDB async connection initialized!")
except ConnectionFailure as e:
    print(f"MongoDB connection failed: {e}")
