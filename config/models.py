"""
Model configurations for OpenAI and DeepSeek providers.
Contains model names, context windows, defaults, and provider-specific settings.
"""

# Model names - OpenAI
MODEL_GPT4O = "gpt-4o"
MODEL_GPT4O_MINI = "gpt-4o-mini"
MODEL_GPT4O_2024 = "gpt-4o-2024-08-06"
MODEL_GPT35_TURBO = "gpt-3.5-turbo"

# Model names - DeepSeek
MODEL_DEEPSEEK_CHAT = "deepseek-chat"
MODEL_DEEPSEEK_REASONER = "deepseek-reasoner"

# Context window sizes (in tokens) - OpenAI
MODEL_GPT4O_CONTEXT_WINDOW = 128000
MODEL_GPT4O_MINI_CONTEXT_WINDOW = 128000
MODEL_GPT4O_2024_CONTEXT_WINDOW = 128000
MODEL_GPT35_TURBO_CONTEXT_WINDOW = 16000

# Context window sizes (in tokens) - DeepSeek
# Updated to match actual API limits with safety margin
MODEL_DEEPSEEK_CHAT_CONTEXT_WINDOW = 65536  # Actual DeepSeek API limit
MODEL_DEEPSEEK_REASONER_CONTEXT_WINDOW = 65536  # Actual DeepSeek API limit

# Default models for different purposes
DEFAULT_MODEL = MODEL_DEEPSEEK_CHAT
DEFAULT_VISION_MODEL = MODEL_GPT4O
DEFAULT_TITLE_GENERATION_MODEL = MODEL_DEEPSEEK_CHAT
DEFAULT_MODERATION_MODEL = MODEL_DEEPSEEK_CHAT
DEFAULT_DATA_ANALYSIS_MODEL = MODEL_GPT4O_2024  # Keep OpenAI for data analysis (Assistants API)

# Default model for OpenAI Assistants API (must be OpenAI model)
DEFAULT_ASSISTANT_MODEL = MODEL_GPT4O_2024

# Provider-specific model tiers for cost optimization and consistency
PROVIDER_MODEL_TIERS = {
    "openai": {
        "premium": [MODEL_GPT4O, MODEL_GPT4O_2024, MODEL_GPT4O_MINI, MODEL_GPT35_TURBO],
        "economy": MODEL_GPT4O_MINI,  # Cheapest OpenAI model with good performance
        "vision": MODEL_GPT4O         # Has vision capabilities
    },
    "deepseek": {
        "premium": [MODEL_DEEPSEEK_CHAT, MODEL_DEEPSEEK_REASONER],
        "economy": MODEL_DEEPSEEK_CHAT,  # Already cost-effective
        "vision": MODEL_GPT4O            # Fallback to OpenAI (DeepSeek has no vision)
    }
}

# Default model names - using constants defined above
default_model_name = MODEL_DEEPSEEK_CHAT
default_model_name_for_vision = MODEL_GPT4O
