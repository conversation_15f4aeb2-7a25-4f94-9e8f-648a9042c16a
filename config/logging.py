"""
Logging configurations for both traditional and project+user specific logging.
"""

import os
import logging

# Logging configuration
log_directory = "logs"
log_file = os.path.join(log_directory, "app.log")
log_max_size = 10485760  # 10MB per file
log_backup_count = 5
log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
log_level = logging.INFO

# Root logger configuration
DISABLE_ROOT_LOGGER = True  # Set to True to disable app.log and force project+user logging

# Project+User wise logging configuration
PROJECT_USER_LOGGING_ENABLED = True  # Feature flag to enable/disable project+user logging
PROJECT_USER_LOG_BASE_DIR = "logs/users"  # Base directory for user-specific logs
PROJECT_USER_LOG_MAX_SIZE = 5242880  # 5MB per project log file
PROJECT_USER_LOG_BACKUP_COUNT = 3  # Number of backup files per project
PROJECT_USER_LOG_FORMAT = '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
PROJECT_USER_LOG_RETENTION_DAYS = 30  # Days to keep project logs
PROJECT_USER_LOG_CLEANUP_INTERVAL_HOURS = 24  # Hours between cleanup runs
PROJECT_USER_LOG_MAX_CONCURRENT_FILES = 100  # Max concurrent log file handles
PROJECT_USER_LOG_BUFFER_SIZE = 8192  # Buffer size for log writes
PROJECT_USER_LOG_FLUSH_INTERVAL = 5  # Seconds between forced flushes
PROJECT_USER_LOG_CONSOLE_OUTPUT = True  # Enable console output for project+user logs
PROJECT_USER_LOG_FORCE_DUAL_OUTPUT = True  # Force all logs to go to both file and console

# Enhanced logging formatting
ENHANCED_LOG_FORMATTING = False  # Disable emojis and Unicode characters for cleaner output
COMPACT_LOG_MODE = False  # Set to True for more compact logging (less visual formatting)
READABLE_LOG_SPACING = True  # Add spacing between log entries for better readability
SIMPLIFIED_JSON_STRUCTURE = True  # Use simplified, readable format instead of dense JSON

# System/fallback logging (when DISABLE_ROOT_LOGGER is True)
SYSTEM_LOG_TO_FILE = True  # Also log system messages to a file
SYSTEM_LOG_FILE = "logs/system.log"  # File for system/fallback logs
