"""
Server and security configurations.
"""

# Server configuration
server_host = "0.0.0.0"
server_port = 8000

# Security configurations
# secret_key = os.urandom(32)
# print(f"Secret key for descryptin is : {secret_key }")
secret_key = b"\xefo\xae\x03\xce\x81\xa6\xaf\xffq\xf3#\xba*\xcaY$\x9dQ\xab\xba\xa8\x86|\xc1\xab\xc1\x00D\x97l\x9c"
print(f"Secret key for descryptin is : {secret_key }")

JWT_SECRET_KEY = "/IQdfTv/czfbeemqe70e2DEQ0CFCxzdoP3WXzEqPCWQ="

# Timeout and retry settings
REQUEST_TIMEOUT = 180  # seconds
RETRY_INTERVAL = 5  # seconds
MAX_RETRIES = 3
