from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from routes.main_router import main_router
# from routes.data_analyst_routes import router as data_analyst_router

from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from services.logging_middleware import ProjectUserLoggingMiddleware
from exceptions.api_exceptions import APIError, handle_general_error
import os
import logging
from logging.handlers import RotatingFileHandler
import config
import asyncio
from services.async_fs_utils import async_makedirs

# Configure logging
# Create log directory if it doesn't exist
# Note: We're using synchronous os.makedirs here because this is during app startup
# before the async event loop is available
if not os.path.exists(config.log_directory):
    os.makedirs(config.log_directory)

# Configure root logger based on settings
if getattr(config, 'DISABLE_ROOT_LOGGER', False):
    # Setup handlers for system/fallback logs
    handlers = [logging.StreamHandler()]  # Always include console output

    # Add file handler for system logs if enabled
    if getattr(config, 'SYSTEM_LOG_TO_FILE', True):
        system_log_file = getattr(config, 'SYSTEM_LOG_FILE', 'logs/system.log')
        # Ensure system log directory exists
        system_log_dir = os.path.dirname(system_log_file)
        if not os.path.exists(system_log_dir):
            os.makedirs(system_log_dir)

        system_file_handler = RotatingFileHandler(
            system_log_file,
            maxBytes=config.log_max_size,
            backupCount=config.log_backup_count
        )
        handlers.append(system_file_handler)

    # Configure root logger with dual output (console + file)
    logging.basicConfig(
        level=logging.INFO,  # Allow INFO level to capture direct logging calls
        format='[SYSTEM] %(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=handlers
    )
    print("🔧 Root logger configured for system messages with dual output - application logs use project+user logging")
else:
    # Traditional logging setup with both file and console
    logging.basicConfig(
        level=config.log_level,
        format=config.log_format,
        handlers=[
            RotatingFileHandler(config.log_file, maxBytes=config.log_max_size, backupCount=config.log_backup_count),
            logging.StreamHandler()  # Keep console output
        ]
    )

app = FastAPI()


# Global Exception Handlers
@app.exception_handler(APIError)
async def api_error_handler(request: Request, exc: APIError):
    """Handle custom API errors (these are already user-friendly)"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle FastAPI HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle all other unhandled exceptions with generic message"""
    try:
        # Use our general error handler which will log and return generic message
        handle_general_error(exc)
    except APIError as api_error:
        # This will be our ServerError with the generic message
        return JSONResponse(
            status_code=api_error.status_code,
            content={"detail": api_error.detail}
        )
    except Exception:
        # Fallback in case something goes wrong with error handling itself
        return JSONResponse(
            status_code=500,
            content={"detail": "We're experiencing some technical difficulties. Please try again in a few moments. If the issue persists, contact your administrator."}
        )


# Add Project+User Logging Middleware (should be first for complete request tracking)
if config.PROJECT_USER_LOGGING_ENABLED:
    app.add_middleware(ProjectUserLoggingMiddleware)

# CORS and Session Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

secret_key = os.getenv('SECRET_KEY')
app.add_middleware(SessionMiddleware, secret_key=secret_key)

# Include Routes
app.include_router(main_router)
# app.include_router(data_analyst_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=config.server_host, port=config.server_port)
